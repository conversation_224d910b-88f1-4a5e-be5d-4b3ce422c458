print('app.py 已经开始执行')
from flask import Flask, render_template, request, redirect, url_for, session, jsonify, g
from functools import wraps
import database as db
import i18n_backend
import os
import sqlite3 # Import sqlite3 to use its IntegrityError
import threading
import time
import atexit

app = Flask(__name__)
# Replace os.urandom with a static secret key for debugging
app.secret_key = 'a_very_secret_key_for_testing_purposes'

# Define the absolute path for the database, consistent with database.py
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DB_PATH = os.path.join(BASE_DIR, 'printer.db')

# 全局变量控制定时任务
cleanup_thread = None
cleanup_stop_event = threading.Event()

# 登录装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'error': 'Not authenticated'}), 401
        return f(*args, **kwargs)
    return decorated_function

# 权限检查函数
def check_permission(permission_name):
    """检查当前用户是否有指定权限"""
    permissions = session.get('permissions', {})
    return permissions.get(permission_name, False)

def auto_cleanup_chat_records():
    """定时清理过期聊天记录的后台任务"""
    while not cleanup_stop_event.is_set():
        try:
            conn = db.create_connection()
            cursor = conn.cursor()

            # 获取系统设定
            cursor.execute('SELECT setting_key, setting_value FROM system_settings WHERE setting_key LIKE "chat_retention_%"')
            settings = {}
            for row in cursor.fetchall():
                settings[row[0]] = int(row[1])

            days = settings.get('chat_retention_days', 7)
            hours = settings.get('chat_retention_hours', 0)
            minutes = settings.get('chat_retention_minutes', 0)

            # 计算总分钟数
            total_minutes = days * 24 * 60 + hours * 60 + minutes

            if total_minutes > 0:
                # 删除过期的聊天记录
                cursor.execute('''
                    DELETE FROM public_chat_messages
                    WHERE created_at < datetime('now', '-{} minutes')
                '''.format(total_minutes))

                deleted_count = cursor.rowcount
                if deleted_count > 0:
                    print(f"自动清理了{deleted_count}条过期聊天记录")

                conn.commit()

            conn.close()

        except Exception as e:
            print(f"自动清理聊天记录时出错: {e}")

        # 每小时检查一次
        cleanup_stop_event.wait(3600)

def start_cleanup_thread():
    """启动清理线程"""
    global cleanup_thread
    if cleanup_thread is None or not cleanup_thread.is_alive():
        cleanup_stop_event.clear()
        cleanup_thread = threading.Thread(target=auto_cleanup_chat_records, daemon=True)
        cleanup_thread.start()
        print("聊天记录自动清理线程已启动")

def stop_cleanup_thread():
    """停止清理线程"""
    global cleanup_thread
    if cleanup_thread and cleanup_thread.is_alive():
        cleanup_stop_event.set()
        cleanup_thread.join(timeout=5)
        print("聊天记录自动清理线程已停止")

# 注册退出时的清理函数
atexit.register(stop_cleanup_thread)

def generate_print_content(label, quantity):
    """生成标签打印内容HTML - 使用与实时预览相同的渲染逻辑"""
    import re
    from datetime import datetime

    label_name = label[1]
    label_content = label[3]  # 这是富文本HTML内容
    label_type = label[2]
    v_align = label[13] if len(label) > 13 else 'center'  # 垂直对齐 (v_align字段)

    # 处理动态占位符（与前端updatePreview函数相同的逻辑）
    date = datetime.now()
    user_code = session.get('user_code', '用户代码')

    replacements = {
        r'\[date\]': f"{date.month:02d}/{date.day:02d}/{date.year}",
        r'\[week\]': ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][date.weekday()],
        r'\[user\]': user_code
    }

    processed_content = label_content
    for placeholder, value in replacements.items():
        processed_content = re.sub(placeholder, value, processed_content)

    # 垂直对齐映射
    v_align_map = {
        'top': 'flex-start',
        'center': 'center',
        'bottom': 'flex-end'
    }
    justify_content = v_align_map.get(v_align, 'center')

    # 生成多份标签内容
    labels_html = ""
    for i in range(quantity):
        labels_html += f"""
        <div class="label-print-item">
            <div class="label-print-content" style="justify-content: {justify_content};">
                {processed_content}
            </div>
        </div>
        """

    # 完整的HTML内容
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>打印标签 - {label_name}</title>
        <style>
            /* 屏幕显示样式 */
            @media screen {{
                body {{
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    padding: 20px;
                    background-color: #f5f5f5;
                    margin: 0;
                }}

                .print-container {{
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    padding: 20px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}

                .print-header {{
                    text-align: center;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 2px solid #eee;
                }}

                .print-header h2 {{
                    color: #333;
                    margin: 0 0 10px 0;
                }}

                .print-info {{
                    color: #666;
                    font-size: 14px;
                    margin: 5px 0;
                }}

                .print-buttons {{
                    text-align: center;
                    margin: 20px 0;
                }}

                .print-button {{
                    background: #007bff;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    font-size: 16px;
                    cursor: pointer;
                    margin: 0 10px;
                    border-radius: 5px;
                    transition: background-color 0.3s;
                }}

                .print-button:hover {{
                    background: #0056b3;
                }}

                .print-button.secondary {{
                    background: #6c757d;
                }}

                .print-button.secondary:hover {{
                    background: #545b62;
                }}

                .label-print-item {{
                    margin: 20px 0;
                    page-break-inside: avoid;
                }}

                .label-print-content {{
                    /* 与实时预览相同的尺寸和样式 */
                    width: 270px;
                    height: 180px;
                    border: 2px dashed #aaa;
                    background-color: #fff;
                    padding: 8px;
                    box-sizing: border-box;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                    margin: 0 auto;

                    /* 内容样式 */
                    word-break: break-all;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                    font-family: 'Microsoft YaHei', sans-serif;
                    font-size: 12px;
                    line-height: 1.4;

                    /* 确保预览时内容也居中 */
                    text-align: center;
                    align-items: center;
                }}

                /* 表格样式 - 与预览区域相同 */
                .label-print-content table {{
                    border-collapse: collapse;
                    width: auto;
                    max-width: 100%;
                    margin: 2px auto;
                    border: 1px solid #000;
                }}

                .label-print-content table td,
                .label-print-content table th {{
                    border: 1px solid #000;
                    padding: 2px 4px;
                    text-align: center;
                    font-size: 10px;
                    line-height: 1.2;
                }}

                .label-print-content > div {{
                    width: 100%;
                    box-sizing: border-box;
                    text-align: center;
                }}

                /* 确保预览时文本内容也居中 */
                .label-print-content p,
                .label-print-content div {{
                    margin: 0 auto;
                    text-align: center;
                }}
            }}

            /* 打印样式 */
            @media print {{
                * {{
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }}

                @page {{
                    size: 3in 2in;
                    margin: 0;
                    padding: 0;
                }}

                html, body {{
                    margin: 0;
                    padding: 0;
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    background: white;
                    width: 100%;
                    height: 100%;
                }}

                .print-container {{
                    width: 100%;
                    height: 100%;
                    margin: 0;
                    padding: 0;
                    display: flex;
                    flex-direction: column;
                }}

                .no-print {{
                    display: none !important;
                }}

                .label-print-item {{
                    width: 100%;
                    height: 100vh;
                    page-break-inside: avoid;
                    page-break-after: always;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0;
                    padding: 0;
                }}

                .label-print-item:last-child {{
                    page-break-after: avoid;
                }}

                .label-print-content {{
                    /* 打印时的实际尺寸 - 填满整个标签纸 */
                    width: 100%;
                    height: 100%;
                    border: none !important;
                    background-color: #fff !important;
                    padding: 8px;
                    box-sizing: border-box;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                    margin: 0;

                    /* 内容样式 */
                    word-break: break-all;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    font-size: 12pt;
                    line-height: 1.4;
                    -webkit-print-color-adjust: exact;
                    color-adjust: exact;

                    /* 确保内容在标签纸中央 */
                    text-align: center;
                    align-items: center;
                    justify-content: center;
                }}

                /* 打印时的表格样式 */
                .label-print-content table {{
                    border-collapse: collapse;
                    width: auto;
                    max-width: 100%;
                    margin: 2px auto;
                    border: 1px solid #000 !important;
                }}

                .label-print-content table td,
                .label-print-content table th {{
                    border: 1px solid #000 !important;
                    padding: 2px 4px;
                    text-align: center;
                    font-size: 10pt;
                    line-height: 1.2;
                    color: #000 !important;
                }}

                .label-print-content > div {{
                    width: 100%;
                    box-sizing: border-box;
                    text-align: center;
                }}

                /* 确保文本内容居中 */
                .label-print-content p,
                .label-print-content div {{
                    margin: 0 auto;
                    text-align: center;
                }}

                /* 确保所有内容都在标签中央 */
                .label-print-content * {{
                    max-width: 100%;
                    box-sizing: border-box;
                }}

                /* 特殊元素的居中处理 */
                .label-print-content img {{
                    display: block;
                    margin: 0 auto;
                    max-width: 100%;
                    height: auto;
                }}

                .label-print-content ul,
                .label-print-content ol {{
                    text-align: center;
                    list-style-position: inside;
                    margin: 0 auto;
                    padding: 0;
                }}

                .label-print-content li {{
                    text-align: center;
                }}

                /* 强制所有文本元素居中 */
                .label-print-content span,
                .label-print-content strong,
                .label-print-content em,
                .label-print-content b,
                .label-print-content i {{
                    text-align: inherit;
                }}
            }}
        </style>
    </head>
    <body>
        <div class="print-container">
            <div class="no-print print-header">
                <h2>标签打印预览</h2>
                <div class="print-info">标签名称: {label_name}</div>
                <div class="print-info">打印数量: {quantity}</div>
                <div class="print-buttons">
                    <button class="print-button" onclick="window.print()">开始打印</button>
                    <button class="print-button secondary" onclick="window.close()">关闭</button>
                </div>
            </div>
            {labels_html}
        </div>
    </body>
    </html>
    """

    return html_content

def init_db():
    """Initializes the database if it doesn't exist and seeds it."""
    if not os.path.exists(DB_PATH):
        print("Database not found. Creating and seeding...")
        db.create_tables()
        seed_database()
        print("Database created and seeded.")

@app.before_request
def before_request():
    """Run before each request. Initialize database if needed."""
    if not os.path.exists(DB_PATH):
        init_db()
    else:
        # Check if database needs seeding
        conn = db.create_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('SELECT COUNT(*) FROM users')
            user_count = cursor.fetchone()[0]
            if user_count == 0:
                seed_database()

            # 数据库迁移：添加缺失的权限字段
            migrate_database(cursor, conn)
        except:
            # Table doesn't exist, seed the database
            seed_database()
        finally:
            conn.close()

def migrate_database(cursor, conn):
    """Migrate database schema to add missing columns"""
    try:
        # 检查permissions表是否有can_view_audit_log字段
        cursor.execute("PRAGMA table_info(permissions)")
        columns = [column[1] for column in cursor.fetchall()]

        # 检查users表是否有language字段
        cursor.execute("PRAGMA table_info(users)")
        user_columns = [column[1] for column in cursor.fetchall()]

        if 'language' not in user_columns:
            try:
                cursor.execute('ALTER TABLE users ADD COLUMN language TEXT DEFAULT "en"')
                conn.commit()
                print("Added language column to users table")
            except Exception as e:
                print(f"Failed to add language column: {e}")

        # 需要添加的权限字段
        missing_columns = []
        required_columns = [
            'can_view_audit_log',
            'can_clear_audit_log',
            'can_manage_users_add',
            'can_manage_users_edit',
            'can_manage_users_delete',
            'can_manage_departments_add',
            'can_manage_departments_edit',
            'can_manage_departments_delete',
            'can_manage_user_groups',
            'can_manage_user_groups_add',
            'can_manage_user_groups_edit',
            'can_manage_user_groups_delete',
            'can_manage_labels_add',
            'can_manage_labels_edit',
            'can_manage_labels_delete',
            'can_manage_announcements_add',
            'can_manage_announcements_edit',
            'can_manage_announcements_delete',
            'can_reply_feedback',
            'can_delete_feedback',
            'can_manage_barcode_prefixes',
            'can_manage_barcode_prefixes_add',
            'can_manage_barcode_prefixes_edit',
            'can_manage_barcode_prefixes_delete',
            'can_manage_chatbot_kb',
            'can_manage_chatbot_kb_add',
            'can_manage_chatbot_kb_edit',
            'can_manage_chatbot_kb_delete',
            'can_submit_knowledge',
            'can_review_knowledge_submissions',
            'can_review_knowledge',
            'can_view_problem_solve',
            'can_reply_problem_solve',
            'can_delete_problem_solve',
            'can_print_labels',
            'can_search_labels',
            'can_copy_labels',
            'can_view_statistics',
            'can_view_reports',
            'can_export_data',
            'can_manage_refresh_permissions',
            'can_backup_restore',
            'can_manage_security',
            'can_manage_system_settings'
        ]

        for column in required_columns:
            if column not in columns:
                missing_columns.append(column)

        # 添加缺失的列
        for column in missing_columns:
            try:
                cursor.execute(f'ALTER TABLE permissions ADD COLUMN {column} BOOLEAN NOT NULL DEFAULT 0')
                print(f"Added column: {column}")
            except Exception as e:
                print(f"Failed to add column {column}: {e}")

        if missing_columns:
            conn.commit()
            print(f"Database migration completed. Added {len(missing_columns)} columns.")

    except Exception as e:
        print(f"Database migration error: {e}")

def seed_database():
    """Seed the database with initial data"""
    conn = db.create_connection()
    c = conn.cursor()
    
    try:
        # 1. Create User Groups
        groups = [('Admin',), ('PS User',), ('Normal User',)]
        c.executemany('INSERT INTO user_groups (name) VALUES (?)', groups)
        
        # 2. Define and Assign Permissions
        # Admin (id=1): all permissions
        c.execute('''
            INSERT INTO permissions (
                group_id, can_manage_backend, can_view_problem_solve, can_manage_users, can_manage_departments,
                can_manage_labels, can_manage_announcements, can_view_feedback, can_view_audit_log, can_clear_audit_log,
                can_submit_knowledge, can_review_knowledge_submissions, can_review_knowledge,
                can_reply_problem_solve, can_delete_problem_solve, can_reply_feedback, can_delete_feedback,
                can_print_labels, can_search_labels, can_copy_labels, can_use_chat, can_chat_robot, can_chat_public,
                can_view_statistics, can_view_reports, can_export_data, can_backup_restore, can_manage_security, can_manage_system_settings,
                can_manage_category_center, can_manage_category_center_add, can_manage_category_center_edit, can_manage_category_center_delete
            )
            VALUES (1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1)
        ''')
        # PS User (id=2): can see problem solve
        c.execute('''
            INSERT INTO permissions (group_id, can_view_problem_solve) VALUES (2, 1)
        ''')
        # Normal User (id=3): no special permissions by default
        c.execute('''
            INSERT INTO permissions (group_id) VALUES (3)
        ''')

        # 3. Create a default Department
        c.execute("INSERT INTO departments (name) VALUES ('General')")

        # 4. Create a default Admin user
        c.execute('''
            INSERT INTO users (card_id, name, code, department_id, group_id) 
            VALUES (?, ?, ?, ?, ?)
        ''', ('admin001', 'Admin User', 'ADM001', 1, 1))
        
        # Create a normal user for testing
        c.execute('''
            INSERT INTO users (card_id, name, code, department_id, group_id) 
            VALUES (?, ?, ?, ?, ?)
        ''', ('user001', 'Normal User', 'USR001', 1, 3))

        # Add some sample labels for testing
        sample_labels = [
            ('普通标签1', 'PL1', 'PL001', 'normal', 0),
            ('普通标签2', 'PL2', 'PL002', 'normal', 0),
            ('高级标签1', 'AL1', 'AL001', 'advanced', 1),
            ('高级标签2', 'AL2', 'AL002', 'advanced', 0),
            ('特殊标签1', 'SL1', 'SL001', 'special', 1),
            ('特殊标签2', 'SL2', 'SL002', 'special', 0),
            ('全局标签1', 'GL1', 'GL001', 'global', 1),
            ('全局标签2', 'GL2', 'GL002', 'global', 0),
        ]
        
        c.executemany('''
            INSERT INTO labels (name, content, type, prompt_for_quantity) 
            VALUES (?, ?, ?, ?)
        ''', [(name, f'Content for {name}', label_type, print_prompt) for name, short_name, code, label_type, print_prompt in sample_labels])

        # Add a sample announcement
        c.execute('''
            INSERT INTO announcements (content, author_id, created_at) 
            VALUES (?, ?, datetime('now'))
        ''', ('欢迎使用标签打印系统！这是一个测试公告。', 1))

        conn.commit()
        print("Database seeded with initial data.")
    except sqlite3.IntegrityError:
        print("Database already seeded.")
    finally:
        conn.close()

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        card_id = request.form['card_id']
        password = request.form.get('password', '')

        conn = db.create_connection()
        # Join with other tables to get all necessary info
        user_query = '''
            SELECT u.*, d.name as department_name, p.* FROM users u
            LEFT JOIN departments d ON u.department_id = d.id
            LEFT JOIN permissions p ON u.group_id = p.group_id
            WHERE u.card_id = ?
        '''
        user = conn.execute(user_query, (card_id,)).fetchone()
        conn.close()

        if user:
            # Check password if user has one
            user_password = user['password']
            if user_password and user_password.strip():
                # User has password, check it
                if password != user_password:
                    # Track failed login attempts
                    session_key = f'failed_attempts_{card_id}'
                    failed_attempts = session.get(session_key, 0) + 1
                    session[session_key] = failed_attempts

                    # Check if this is an AJAX request
                    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.headers.get('Accept') == 'application/json'

                    if failed_attempts >= 4:
                        # 4次及以上显示"Too many failed attempts"
                        if is_ajax:
                            return jsonify({'error': i18n_backend.t('too_many_failed_attempts'), 'show_reset': True, 'failed_attempts': failed_attempts})
                        else:
                            return render_template('login.html',
                                                 error=i18n_backend.t('too_many_failed_attempts'),
                                                 show_reset=True,
                                                 card_id=card_id)
                    else:
                        # 1-3次显示"Invalid password"
                        error_message = i18n_backend.t('invalid_password')
                        if is_ajax:
                            return jsonify({'error': error_message, 'failed_attempts': failed_attempts, 'max_attempts': 3})
                        else:
                            return render_template('login.html',
                                                 error=error_message,
                                                 card_id=card_id,
                                                 needs_password=True)

            # Clear failed attempts on successful login
            session.pop(f'failed_attempts_{card_id}', None)
            # Store user info in session
            session['user_id'] = user['id']
            session['user_name'] = user['name']
            session['user_code'] = user['code']
            session['department_name'] = user['department_name']

            # 加载用户的语言设置
            try:
                user_language = user['language'] if 'language' in user.keys() and user['language'] else 'en'
                session['language'] = user_language
            except (KeyError, TypeError):
                session['language'] = 'en'  # 默认英文
            
            # Store permissions in session
            permissions = {key: user[key] for key in user.keys() if key not in ['id', 'name', 'card_id', 'code', 'department_id', 'group_id', 'department_name']}
            
            # 🔧 自动设置后台管理权限：如果拥有后台管理权限分组下的任何一项权限，就设置can_manage_backend为True
            backend_permissions = [
                'can_manage_users', 'can_manage_users_add', 'can_manage_users_edit', 'can_manage_users_delete',
                'can_manage_departments', 'can_manage_departments_add', 'can_manage_departments_edit', 'can_manage_departments_delete',
                'can_manage_user_groups', 'can_manage_user_groups_add', 'can_manage_user_groups_edit', 'can_manage_user_groups_delete',
                'can_manage_labels', 'can_manage_labels_add', 'can_manage_labels_edit', 'can_manage_labels_delete',
                'can_manage_announcements', 'can_manage_announcements_add', 'can_manage_announcements_edit', 'can_manage_announcements_delete',
                'can_view_feedback', 'can_reply_feedback', 'can_delete_feedback',
                'can_view_audit_log',
                'can_manage_barcode_prefixes', 'can_manage_barcode_prefixes_add', 'can_manage_barcode_prefixes_edit', 'can_manage_barcode_prefixes_delete',
                'can_manage_chatbot_kb', 'can_manage_chatbot_kb_add', 'can_manage_chatbot_kb_edit', 'can_manage_chatbot_kb_delete',
                'can_manage_system_settings'
            ]
            
            # 检查是否有任何后台管理权限
            has_backend_permission = any(permissions.get(perm, False) for perm in backend_permissions)
            permissions['can_manage_backend'] = has_backend_permission

            # 如果有后台管理权限，自动给予Problem Solve相关权限
            if has_backend_permission:
                permissions['can_view_problem_solve'] = True
                permissions['can_reply_problem_solve'] = True
                permissions['can_delete_problem_solve'] = True
                # 管理员也应该有词库审核权限
                permissions['can_review_knowledge_submissions'] = True
                permissions['can_review_knowledge'] = True

            # 默认给所有用户知识提交权限
            permissions['can_submit_knowledge'] = True

            session['permissions'] = permissions

            # Check if this is an AJAX request
            is_ajax = (request.headers.get('Content-Type') == 'application/x-www-form-urlencoded' and
                      request.headers.get('X-Requested-With') == 'XMLHttpRequest') or \
                      request.headers.get('Accept', '').find('application/json') != -1
            if is_ajax:
                return jsonify({'success': True, 'redirect': url_for('index')})
            else:
                return redirect(url_for('index'))
        else:
            # Check if this is an AJAX request
            is_ajax = request.headers.get('Content-Type') == 'application/x-www-form-urlencoded'
            if is_ajax:
                return jsonify({'error': i18n_backend.t('invalid_card_id')})
            else:
                return render_template('login.html', error=i18n_backend.t('invalid_card_id'))
    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

@app.route('/')
def index():
    if 'user_id' not in session:
        print(f"User not logged in, redirecting to login")
        return redirect(url_for('login'))

    print(f"User {session.get('user_name')} accessing main page")
    version = '1.1.0'
    user_info = {
        'name': session.get('user_name', ''),
        'department': session.get('department_name', '')
    }
    return render_template(
        'index.html',
        user_info=user_info,
        permissions=session.get('permissions'),
        version=version,
        user_id=session.get('user_id'),
        user_name=session.get('user_name')
    )

@app.route('/admin')
def admin():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_backend'):
        return i18n_backend.t('access_denied'), 403 # Or redirect to index with an error

    # Pass user info to the admin template
    user_info = {
        'name': session.get('user_name'),
        'code': session.get('user_code'),
        'department': session.get('department_name')
    }
    return render_template('admin.html', user_info=user_info, permissions=permissions)

# API Routes for frontend functionality
@app.route('/api/labels')
def get_labels():
    """Get all labels for the main page grid"""
    if 'user_id' not in session:
        return jsonify(i18n_backend.get_error_response('not_authenticated', 401))
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT id, name, content, type, prompt_for_quantity, problem_solve, department_names, v_align, copy_info, print_quantity, label_code_shortcut, usage_question_enabled, usage_question_title
            FROM labels
            WHERE is_hidden = 0 OR is_hidden IS NULL
            ORDER BY name
        ''')
        labels = []
        for row in cursor.fetchall():
            labels.append({
                'id': row[0],
                'name': row[1],
                'content': row[2],
                'type': row[3],
                'print_prompt': bool(row[4]),
                'problem_solve': bool(row[5]),
                'department_names': row[6] or '',
                'v_align': row[7] or 'center',
                'copy_info': row[8] or '',
                'print_quantity': row[9] or 1,
                'label_code_shortcut': row[10] or '',
                'usage_question_enabled': bool(row[11]) if len(row) > 11 else False,
                'usage_question_title': row[12] if len(row) > 12 else ''
            })
        return jsonify(labels)
    except Exception as e:
        print('加载标签失败详细报错:', e)
        return jsonify({'success': False, 'message': '加载标签失败，请检查网络或联系管理员'})
    finally:
        conn.close()

@app.route('/api/departments')
def get_departments():
    """Get all departments for the dropdown"""
    if 'user_id' not in session:
        return jsonify(i18n_backend.get_error_response('not_authenticated', 401))
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('SELECT id, name FROM departments ORDER BY name')
        departments = [{'id': row[0], 'name': row[1]} for row in cursor.fetchall()]
        return jsonify(departments)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/announcements')
def get_announcements():
    """Get all announcements for the marquee"""
    if 'user_id' not in session:
        return jsonify(i18n_backend.get_error_response('not_authenticated', 401))
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT id, content FROM announcements 
            ORDER BY created_at DESC
        ''')
        announcements = []
        for row in cursor.fetchall():
            announcements.append({
                'id': row[0],
                'content': row[1]
            })
        return jsonify({'announcements': announcements})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/quick-search')
def quick_search():
    """Search labels by name, short_name, or code"""
    if 'user_id' not in session:
        return jsonify(i18n_backend.get_error_response('not_authenticated', 401))
    
    query = request.args.get('q', '').strip()
    if not query:
        return jsonify([])
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        # 获取用户部门信息
        user_department = None
        if 'user_id' in session:
            cursor.execute('SELECT department FROM users WHERE id = ?', (session['user_id'],))
            user_result = cursor.fetchone()
            if user_result:
                user_department = user_result[0]

        # 搜索标签（包括隐藏的）
        cursor.execute('''
            SELECT id, name, content, type, copy_info, print_quantity, label_code_shortcut, department_names, is_hidden
            FROM labels
            WHERE name LIKE ? OR content LIKE ? OR label_code_shortcut LIKE ?
            ORDER BY
                CASE
                    WHEN department_names LIKE ? THEN 1
                    WHEN department_names IS NULL OR department_names = '' THEN 2
                    ELSE 3
                END,
                name
            LIMIT 10
        ''', (f'%{query}%', f'%{query}%', f'%{query}%', f'%{user_department}%' if user_department else '%'))
        
        results = []
        for row in cursor.fetchall():
            department_names = row[7] or ''
            department_display = department_names if department_names else '通用部门'

            results.append({
                'id': row[0],
                'name': row[1],
                'content': row[2],
                'type': row[3],
                'copy_info': row[4] or '',
                'print_quantity': row[5] or 1,
                'label_code_shortcut': row[6] or '',
                'department_names': department_names,
                'department_display': department_display,
                'is_hidden': bool(row[8]) if len(row) > 8 else False
            })

        # 搜索分类代码
        cursor.execute('''
            SELECT id, name, copy_content, department_names
            FROM category_codes
            WHERE name LIKE ? OR copy_content LIKE ?
            ORDER BY
                CASE
                    WHEN department_names LIKE ? THEN 1
                    WHEN department_names IS NULL OR department_names = '' THEN 2
                    ELSE 3
                END,
                name
            LIMIT 5
        ''', (f'%{query}%', f'%{query}%', f'%{user_department}%' if user_department else '%'))

        for row in cursor.fetchall():
            department_names = row[3] or ''
            department_display = department_names if department_names else '通用部门'

            results.append({
                'id': f'category_{row[0]}',
                'name': row[1],
                'content': row[2],
                'type': 'category',
                'copy_info': row[2],
                'print_quantity': 0,
                'label_code_shortcut': '',
                'department_names': department_names,
                'department_display': department_display,
                'is_category': True
            })

        return jsonify(results)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/print-label/<int:label_id>', methods=['POST'])
def print_label(label_id):
    """Print a specific label"""
    if 'user_id' not in session:
        return jsonify(i18n_backend.get_error_response('not_authenticated', 401))
    
    data = request.get_json() or {}
    quantity = data.get('quantity', None)  # 如果前端没有指定数量，使用标签的默认数量
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        # Get label details
        cursor.execute('SELECT * FROM labels WHERE id = ?', (label_id,))
        label = cursor.fetchone()
        
        if not label:
            return jsonify({'error': 'Label not found'}), 404
        
        # 如果前端没有指定数量，使用标签的print_quantity字段
        if quantity is None:
            quantity = label[12] if len(label) > 12 else 1  # print_quantity字段
        
        # Log the print action
        cursor.execute('''
            INSERT INTO print_logs (user_id, label_id, quantity, printed_at)
            VALUES (?, ?, ?, datetime('now'))
        ''', (session['user_id'], label_id, quantity))
        
        conn.commit()
        
        # 生成打印内容HTML
        print_content = generate_print_content(label, quantity)

        return jsonify({
            'success': True,
            'message': f'准备打印 {quantity} 张标签: {label[1]}',
            'print_content': print_content,
            'label': {
                'id': label[0],
                'name': label[1],
                'type': label[4],
                'copy_info': label[9] if len(label) > 9 else '',
                'print_quantity': label[12] if len(label) > 12 else 1
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# Admin API Routes
@app.route('/api/users')
def get_users():
    """Get all users for admin management"""
    if 'user_id' not in session:
        return jsonify(i18n_backend.get_error_response('not_authenticated', 401))

    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_users'):
        return jsonify(i18n_backend.get_error_response('access_denied', 403))
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT u.*, d.name as department_name, ug.name as group_name 
            FROM users u
            LEFT JOIN departments d ON u.department_id = d.id
            LEFT JOIN user_groups ug ON u.group_id = ug.id
            ORDER BY u.name
        ''')
        users = []
        for row in cursor.fetchall():
            users.append({
                'id': row[0],
                'card_id': row[1],
                'name': row[2],
                'code': row[3],
                'department_id': row[4],
                'group_id': row[5],
                'department_name': row[6],
                'group_name': row[7]
            })
        return jsonify(users)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/users', methods=['POST'])
def create_user():
    """Create a new user"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_users'):
        return jsonify({'error': 'Access denied'}), 403
    
    data = request.get_json()
    if not data or not data.get('card_id') or not data.get('name'):
        return jsonify({'error': 'Missing required fields'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT INTO users (card_id, name, code, department_id, group_id, password)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            data['card_id'],
            data['name'],
            data.get('code', ''),
            data.get('department_id'),
            data.get('group_id', 3),  # Default to Normal User group
            data.get('password', '')  # Default to empty password
        ))
        
        conn.commit()
        
        # Log the action
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], f'添加用户 - 员工卡ID: {data["card_id"]}, 姓名: "{data["name"]}", 代码: {data.get("code", "无")}, 部门ID: {data.get("department_id", "无")}, 用户组ID: {data.get("group_id", 3)}'))
        
        conn.commit()

        return jsonify(i18n_backend.get_success_response('user_created_success'))
    except sqlite3.IntegrityError:
        return jsonify(i18n_backend.get_error_response('card_id_exists', 400))
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/users/<int:user_id>', methods=['PUT'])
def update_user(user_id):
    """Update a user"""
    if 'user_id' not in session:
        return jsonify(i18n_backend.get_error_response('not_authenticated', 401))

    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_users'):
        return jsonify(i18n_backend.get_error_response('access_denied', 403))

    data = request.get_json()
    if not data:
        return jsonify(i18n_backend.get_error_response('no_data_provided', 400))
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        # Check if password should be updated
        if 'password' in data:
            cursor.execute('''
                UPDATE users SET card_id = ?, name = ?, code = ?, department_id = ?, group_id = ?, password = ?
                WHERE id = ?
            ''', (
                data.get('card_id'),
                data.get('name'),
                data.get('code', ''),
                data.get('department_id'),
                data.get('group_id'),
                data.get('password', ''),
                user_id
            ))
        else:
            cursor.execute('''
                UPDATE users SET card_id = ?, name = ?, code = ?, department_id = ?, group_id = ?
                WHERE id = ?
            ''', (
                data.get('card_id'),
                data.get('name'),
                data.get('code', ''),
                data.get('department_id'),
                data.get('group_id'),
                user_id
            ))
        
        conn.commit()
        
        # Log the action
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], f'修改用户 - ID: {user_id}, 员工卡ID: "{data.get("card_id", "未知")}", 姓名: "{data.get("name", "未知")}", 代码: {data.get("code", "无")}, 部门ID: {data.get("department_id", "无")}, 用户组ID: {data.get("group_id", "无")}'))
        
        conn.commit()

        return jsonify(i18n_backend.get_success_response('user_updated_success'))
    except sqlite3.IntegrityError as e:
        if 'card_id' in str(e):
            return jsonify(i18n_backend.get_error_response('card_id_exists', 400))
        else:
            return jsonify(i18n_backend.get_error_response('data_integrity_error', 400))
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/users/<int:user_id>', methods=['DELETE'])
def delete_user(user_id):
    """Delete a user"""
    if 'user_id' not in session:
        return jsonify(i18n_backend.get_error_response('not_authenticated', 401))

    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_users'):
        return jsonify(i18n_backend.get_error_response('access_denied', 403))
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        # 先获取用户信息用于日志记录
        cursor.execute('SELECT card_id, name, code, department_id, group_id FROM users WHERE id = ?', (user_id,))
        user = cursor.fetchone()
        if not user:
            return jsonify({'error': '用户不存在'}), 404
        
        card_id, name, code, dept_id, group_id = user
        
        cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))
        conn.commit()
        
        # Log the action
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], f'删除用户 - ID: {user_id}, 员工卡ID: {card_id}, 姓名: "{name}", 代码: {code or "无"}, 部门ID: {dept_id or "无"}, 用户组ID: {group_id or "无"}'))
        
        conn.commit()
        
        return jsonify({'success': True, 'message': '用户删除成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/user-groups')
def get_user_groups():
    """Get all user groups"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT ug.id as group_id, ug.name, p.* FROM user_groups ug
            LEFT JOIN permissions p ON ug.id = p.group_id
            ORDER BY ug.name
        ''')
        groups = []
        for row in cursor.fetchall():
            row_dict = dict(zip([column[0] for column in cursor.description], row))
            groups.append({
                'id': row_dict.get('group_id'),
                'name': row_dict.get('name'),
                'permissions': {
                    # 后台管理权限
                    'can_manage_backend': bool(row_dict.get('can_manage_backend', 0)),
                    'can_manage_users': bool(row_dict.get('can_manage_users', 0)),
                    'can_manage_users_add': bool(row_dict.get('can_manage_users_add', 0)),
                    'can_manage_users_edit': bool(row_dict.get('can_manage_users_edit', 0)),
                    'can_manage_users_delete': bool(row_dict.get('can_manage_users_delete', 0)),
                    'can_manage_departments': bool(row_dict.get('can_manage_departments', 0)),
                    'can_manage_departments_add': bool(row_dict.get('can_manage_departments_add', 0)),
                    'can_manage_departments_edit': bool(row_dict.get('can_manage_departments_edit', 0)),
                    'can_manage_departments_delete': bool(row_dict.get('can_manage_departments_delete', 0)),
                    'can_manage_user_groups': bool(row_dict.get('can_manage_user_groups', 0)),
                    'can_manage_user_groups_add': bool(row_dict.get('can_manage_user_groups_add', 0)),
                    'can_manage_user_groups_edit': bool(row_dict.get('can_manage_user_groups_edit', 0)),
                    'can_manage_user_groups_delete': bool(row_dict.get('can_manage_user_groups_delete', 0)),
                    'can_manage_labels': bool(row_dict.get('can_manage_labels', 0)),
                    'can_manage_labels_add': bool(row_dict.get('can_manage_labels_add', 0)),
                    'can_manage_labels_edit': bool(row_dict.get('can_manage_labels_edit', 0)),
                    'can_manage_labels_delete': bool(row_dict.get('can_manage_labels_delete', 0)),
                    'can_manage_category_center': bool(row_dict.get('can_manage_category_center', 0)),
                    'can_manage_category_center_add': bool(row_dict.get('can_manage_category_center_add', 0)),
                    'can_manage_category_center_edit': bool(row_dict.get('can_manage_category_center_edit', 0)),
                    'can_manage_category_center_delete': bool(row_dict.get('can_manage_category_center_delete', 0)),
                    'can_manage_announcements': bool(row_dict.get('can_manage_announcements', 0)),
                    'can_manage_announcements_add': bool(row_dict.get('can_manage_announcements_add', 0)),
                    'can_manage_announcements_edit': bool(row_dict.get('can_manage_announcements_edit', 0)),
                    'can_manage_announcements_delete': bool(row_dict.get('can_manage_announcements_delete', 0)),
                    'can_view_feedback': bool(row_dict.get('can_view_feedback', 0)),
                    'can_reply_feedback': bool(row_dict.get('can_reply_feedback', 0)),
                    'can_delete_feedback': bool(row_dict.get('can_delete_feedback', 0)),
                    'can_view_audit_log': bool(row_dict.get('can_view_audit_log', 0)),
                    'can_clear_audit_log': bool(row_dict.get('can_clear_audit_log', 0)),
                    'can_manage_barcode_prefixes': bool(row_dict.get('can_manage_barcode_prefixes', 0)),
                    'can_manage_barcode_prefixes_add': bool(row_dict.get('can_manage_barcode_prefixes_add', 0)),
                    'can_manage_barcode_prefixes_edit': bool(row_dict.get('can_manage_barcode_prefixes_edit', 0)),
                    'can_manage_barcode_prefixes_delete': bool(row_dict.get('can_manage_barcode_prefixes_delete', 0)),
                    'can_manage_chatbot_kb': bool(row_dict.get('can_manage_chatbot_kb', 0)),
                    'can_manage_chatbot_kb_add': bool(row_dict.get('can_manage_chatbot_kb_add', 0)),
                    'can_manage_chatbot_kb_edit': bool(row_dict.get('can_manage_chatbot_kb_edit', 0)),
                    'can_manage_chatbot_kb_delete': bool(row_dict.get('can_manage_chatbot_kb_delete', 0)),
                    'can_review_knowledge_submissions': bool(row_dict.get('can_review_knowledge_submissions', 0)),
                    
                    # 前台功能权限
                    'can_view_problem_solve': bool(row_dict.get('can_view_problem_solve', 0)),
                    'can_reply_problem_solve': bool(row_dict.get('can_reply_problem_solve', 0)),
                    'can_delete_problem_solve': bool(row_dict.get('can_delete_problem_solve', 0)),
                    'can_print_labels': bool(row_dict.get('can_print_labels', 0)),
                    'can_search_labels': bool(row_dict.get('can_search_labels', 0)),
                    'can_copy_labels': bool(row_dict.get('can_copy_labels', 0)),
                    'can_use_chat': bool(row_dict.get('can_use_chat', 0)),
                    'can_chat_robot': bool(row_dict.get('can_chat_robot', 0)),
                    'can_chat_public': bool(row_dict.get('can_chat_public', 0)),
                    'can_review_knowledge': bool(row_dict.get('can_review_knowledge', 0)),
                    
                    # 数据查看权限
                    'can_view_statistics': bool(row_dict.get('can_view_statistics', 0)),
                    'can_view_reports': bool(row_dict.get('can_view_reports', 0)),
                    'can_export_data': bool(row_dict.get('can_export_data', 0)),
                    
                    # 系统设置权限
                    'can_manage_refresh_permissions': bool(row_dict.get('can_manage_refresh_permissions', 0)),
                    'can_backup_restore': bool(row_dict.get('can_backup_restore', 0)),
                    'can_manage_security': bool(row_dict.get('can_manage_security', 0)),
                    'can_manage_system_settings': bool(row_dict.get('can_manage_system_settings', 0)),
                    'can_refresh_permissions': bool(row_dict.get('can_refresh_permissions', 0)),
                }
            })
        return jsonify(groups)
    except Exception as e:
        print(f"Error getting user groups: {e}")
        return jsonify({'error': 'Failed to get user groups'}), 500
    finally:
        conn.close()

@app.route('/api/user-groups', methods=['POST'])
def create_user_group():
    """Create a new user group"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    # 添加权限验证 - 需要用户组管理权限
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_user_groups'):
        return jsonify({'error': 'Access denied'}), 403
    
    data = request.get_json()
    if not data or not data.get('name'):
        return jsonify({'error': 'Missing required fields'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('INSERT INTO user_groups (name) VALUES (?)', (data['name'],))
        group_id = cursor.lastrowid
        
        # 处理所有权限字段
        p = data.get('permissions', {})
        
        # 🔧 自动设置后台管理权限：如果拥有后台管理权限分组下的任何一项权限，就设置can_manage_backend为True
        backend_permissions = [
            'can_manage_users', 'can_manage_users_add', 'can_manage_users_edit', 'can_manage_users_delete',
            'can_manage_departments', 'can_manage_departments_add', 'can_manage_departments_edit', 'can_manage_departments_delete',
            'can_manage_user_groups', 'can_manage_user_groups_add', 'can_manage_user_groups_edit', 'can_manage_user_groups_delete',
            'can_manage_labels', 'can_manage_labels_add', 'can_manage_labels_edit', 'can_manage_labels_delete',
            'can_manage_announcements', 'can_manage_announcements_add', 'can_manage_announcements_edit', 'can_manage_announcements_delete',
            'can_view_feedback', 'can_reply_feedback', 'can_delete_feedback',
            'can_view_audit_log', 'can_clear_audit_log',
            'can_manage_barcode_prefixes', 'can_manage_barcode_prefixes_add', 'can_manage_barcode_prefixes_edit', 'can_manage_barcode_prefixes_delete',
            'can_manage_chatbot_kb', 'can_manage_chatbot_kb_add', 'can_manage_chatbot_kb_edit', 'can_manage_chatbot_kb_delete',
            'can_manage_system_settings'
        ]
        
        # 检查是否有任何后台管理权限
        has_backend_permission = any(p.get(perm, False) for perm in backend_permissions)
        p['can_manage_backend'] = has_backend_permission
        
        vals = {
            'group_id': group_id,
            # 后台管理权限
            'can_manage_backend': int(bool(p.get('can_manage_backend', False))),
            'can_manage_users': int(bool(p.get('can_manage_users', False))),
            'can_manage_users_add': int(bool(p.get('can_manage_users_add', False))),
            'can_manage_users_edit': int(bool(p.get('can_manage_users_edit', False))),
            'can_manage_users_delete': int(bool(p.get('can_manage_users_delete', False))),
            'can_manage_departments': int(bool(p.get('can_manage_departments', False))),
            'can_manage_departments_add': int(bool(p.get('can_manage_departments_add', False))),
            'can_manage_departments_edit': int(bool(p.get('can_manage_departments_edit', False))),
            'can_manage_departments_delete': int(bool(p.get('can_manage_departments_delete', False))),
            'can_manage_user_groups': int(bool(p.get('can_manage_user_groups', False))),
            'can_manage_user_groups_add': int(bool(p.get('can_manage_user_groups_add', False))),
            'can_manage_user_groups_edit': int(bool(p.get('can_manage_user_groups_edit', False))),
            'can_manage_user_groups_delete': int(bool(p.get('can_manage_user_groups_delete', False))),
            'can_manage_labels': int(bool(p.get('can_manage_labels', False))),
            'can_manage_labels_add': int(bool(p.get('can_manage_labels_add', False))),
            'can_manage_labels_edit': int(bool(p.get('can_manage_labels_edit', False))),
            'can_manage_labels_delete': int(bool(p.get('can_manage_labels_delete', False))),
            'can_manage_announcements': int(bool(p.get('can_manage_announcements', False))),
            'can_manage_announcements_add': int(bool(p.get('can_manage_announcements_add', False))),
            'can_manage_announcements_edit': int(bool(p.get('can_manage_announcements_edit', False))),
            'can_manage_announcements_delete': int(bool(p.get('can_manage_announcements_delete', False))),
            'can_view_feedback': int(bool(p.get('can_view_feedback', False))),
            'can_reply_feedback': int(bool(p.get('can_reply_feedback', False))),
            'can_delete_feedback': int(bool(p.get('can_delete_feedback', False))),
            'can_view_audit_log': int(bool(p.get('can_view_audit_log', False))),
            'can_clear_audit_log': int(bool(p.get('can_clear_audit_log', False))),
            'can_manage_barcode_prefixes': int(bool(p.get('can_manage_barcode_prefixes', False))),
            'can_manage_barcode_prefixes_add': int(bool(p.get('can_manage_barcode_prefixes_add', False))),
            'can_manage_barcode_prefixes_edit': int(bool(p.get('can_manage_barcode_prefixes_edit', False))),
            'can_manage_barcode_prefixes_delete': int(bool(p.get('can_manage_barcode_prefixes_delete', False))),
            'can_manage_chatbot_kb': int(bool(p.get('can_manage_chatbot_kb', False))),
            'can_manage_chatbot_kb_add': int(bool(p.get('can_manage_chatbot_kb_add', False))),
            'can_manage_chatbot_kb_edit': int(bool(p.get('can_manage_chatbot_kb_edit', False))),
            'can_manage_chatbot_kb_delete': int(bool(p.get('can_manage_chatbot_kb_delete', False))),
            'can_review_knowledge_submissions': int(bool(p.get('can_review_knowledge_submissions', False))),
            
            # 前台功能权限
            'can_view_problem_solve': int(bool(p.get('can_view_problem_solve', False))),
            'can_reply_problem_solve': int(bool(p.get('can_reply_problem_solve', False))),
            'can_delete_problem_solve': int(bool(p.get('can_delete_problem_solve', False))),
            'can_reply_feedback': int(bool(p.get('can_reply_feedback', False))),
            'can_delete_feedback': int(bool(p.get('can_delete_feedback', False))),
            'can_print_labels': int(bool(p.get('can_print_labels', False))),
            'can_search_labels': int(bool(p.get('can_search_labels', False))),
            'can_copy_labels': int(bool(p.get('can_copy_labels', False))),
            
            # 数据查看权限
            'can_view_statistics': int(bool(p.get('can_view_statistics', False))),
            'can_view_reports': int(bool(p.get('can_view_reports', False))),
            'can_export_data': int(bool(p.get('can_export_data', False))),
            
            # 系统设置权限
            'can_manage_refresh_permissions': int(bool(p.get('can_manage_refresh_permissions', False))),
            'can_backup_restore': int(bool(p.get('can_backup_restore', False))),
            'can_manage_security': int(bool(p.get('can_manage_security', False))),
            'can_manage_system_settings': int(bool(p.get('can_manage_system_settings', False))),
            'can_refresh_permissions': int(bool(p.get('can_refresh_permissions', False)))
        }
        
        # 构建动态SQL语句，包含所有权限字段
        permission_fields = list(vals.keys())
        permission_fields.remove('group_id')
        
        sql = f'''
            INSERT INTO permissions (
                group_id, {', '.join(permission_fields)}
            )
            VALUES (
                :group_id, {', '.join([':' + field for field in permission_fields])}
            )
        '''
        
        cursor.execute(sql, vals)
        
        conn.commit()
        
        # 记录详细的操作日志
        permissions_list = []
        permission_names = {
            'can_manage_backend': '后台管理入口',
            'can_manage_users': '用户管理',
            'can_manage_users_add': '添加用户',
            'can_manage_users_edit': '编辑用户',
            'can_manage_users_delete': '删除用户',
            'can_manage_departments': '部门管理',
            'can_manage_departments_add': '添加部门',
            'can_manage_departments_edit': '编辑部门',
            'can_manage_departments_delete': '删除部门',
            'can_manage_user_groups': '用户组管理',
            'can_manage_user_groups_add': '添加用户组',
            'can_manage_user_groups_edit': '编辑用户组',
            'can_manage_user_groups_delete': '删除用户组',
            'can_manage_labels': '标签管理',
            'can_manage_labels_add': '添加标签',
            'can_manage_labels_edit': '编辑标签',
            'can_manage_labels_delete': '删除标签',
            'can_manage_announcements': '公告管理',
            'can_manage_announcements_add': '添加公告',
            'can_manage_announcements_edit': '编辑公告',
            'can_manage_announcements_delete': '删除公告',
            'can_view_feedback': '查看反馈',
            'can_reply_feedback': '回复反馈',
            'can_delete_feedback': '删除反馈',
            'can_view_audit_log': '操作日志',
            'can_clear_audit_log': '清空操作日志',
            'can_manage_barcode_prefixes': '条形码前缀管理',
            'can_manage_barcode_prefixes_add': '添加条形码前缀',
            'can_manage_barcode_prefixes_edit': '编辑条形码前缀',
            'can_manage_barcode_prefixes_delete': '删除条形码前缀',
            'can_manage_chatbot_kb': '聊天机器人词库管理',
            'can_manage_chatbot_kb_add': '添加聊天机器人词条',
            'can_manage_chatbot_kb_edit': '编辑聊天机器人词条',
            'can_manage_chatbot_kb_delete': '删除聊天机器人词条',
            'can_submit_knowledge': '知识提交权限',
            'can_review_knowledge_submissions': '知识提交审核权限',
            'can_view_problem_solve': '显示Problem Solve按钮',
            'can_reply_problem_solve': 'Problem Solve回复权限',
            'can_delete_problem_solve': 'Problem Solve删除权限',
            'can_reply_feedback': '反馈回复权限',
            'can_delete_feedback': '反馈删除权限',
            'can_print_labels': '标签打印权限',
            'can_search_labels': '标签搜索权限',
            'can_copy_labels': '标签复制权限',
            'can_view_statistics': '查看统计数据',
            'can_view_reports': '查看报表',
            'can_export_data': '导出数据',
            'can_manage_refresh_permissions': '权限刷新',
            'can_backup_restore': '备份恢复',
            'can_manage_security': '安全设置',
            'can_manage_system_settings': '系统设定',
            'can_refresh_permissions': '权限刷新'
        }
        
        for perm_key, perm_name in permission_names.items():
            if p.get(perm_key):
                permissions_list.append(perm_name)
        
        permissions_str = ', '.join(permissions_list) if permissions_list else '无权限'
        log_action = f'添加用户组 - ID: {group_id}, 名称: "{data["name"]}", 权限: {permissions_str}'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        
        conn.commit()
        return jsonify({'success': True, 'message': '用户组创建成功，相关用户需重新登录以使权限生效'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/departments', methods=['POST'])
def create_department():
    """Create a new department"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    data = request.get_json()
    if not data or not data.get('name'):
        return jsonify({'error': 'Missing required fields'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('INSERT INTO departments (name) VALUES (?)', (data['name'],))
        new_dept_id = cursor.lastrowid
        conn.commit()
        
        # 记录详细的操作日志
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], f'添加部门 - ID: {new_dept_id}, 名称: "{data["name"]}"'))
        
        conn.commit()
        
        return jsonify(i18n_backend.get_success_response('department_created_success'))
    except sqlite3.IntegrityError:
        return jsonify(i18n_backend.get_error_response('department_name_exists', 400))
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/departments/<int:dept_id>', methods=['PUT'])
def update_department(dept_id):
    """Update a department"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    data = request.get_json()
    if not data or not data.get('name'):
        return jsonify({'error': 'Missing required fields'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        # 先获取原部门信息用于日志记录
        cursor.execute('SELECT name FROM departments WHERE id = ?', (dept_id,))
        old_dept = cursor.fetchone()
        if not old_dept:
            return jsonify({'error': '部门不存在'}), 404
        
        old_name = old_dept[0]
        
        cursor.execute('UPDATE departments SET name = ? WHERE id = ?', (data['name'], dept_id))
        conn.commit()
        
        # 记录详细的操作日志
        log_action = f'修改部门 - ID: {dept_id}, 名称: "{old_name}" → "{data["name"]}"'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        
        conn.commit()
        
        return jsonify({'success': True, 'message': '部门更新成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/departments/<int:dept_id>', methods=['DELETE'])
def delete_department(dept_id):
    """Delete a department"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        # 先获取部门信息用于日志记录
        cursor.execute('SELECT name FROM departments WHERE id = ?', (dept_id,))
        dept = cursor.fetchone()
        if not dept:
            return jsonify({'error': '部门不存在'}), 404
        
        dept_name = dept[0]
        
        cursor.execute('DELETE FROM departments WHERE id = ?', (dept_id,))
        conn.commit()
        
        # 记录详细的操作日志
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], f'删除部门 - ID: {dept_id}, 名称: "{dept_name}"'))
        
        conn.commit()
        
        return jsonify({'success': True, 'message': '部门删除成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/admin/announcements')
def get_admin_announcements():
    """Get all announcements for admin management"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_announcements'):
        return jsonify({'error': 'Access denied'}), 403
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT a.*, u.name as author_name 
            FROM announcements a
            LEFT JOIN users u ON a.author_id = u.id
            ORDER BY a.created_at DESC
        ''')
        announcements = []
        for row in cursor.fetchall():
            announcements.append({
                'id': row[0],
                'content': row[1],
                'author_id': row[2],
                'created_at': row[3],
                'author_name': row[4]
            })
        return jsonify(announcements)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/admin/announcements', methods=['POST'])
def create_announcement():
    """Create a new announcement"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_announcements'):
        return jsonify({'error': 'Access denied'}), 403
    
    data = request.get_json()
    if not data or not data.get('content'):
        return jsonify({'error': 'Missing required fields'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT INTO announcements (content, author_id, created_at)
            VALUES (?, ?, datetime('now'))
        ''', (data['content'], session['user_id']))
        
        new_announcement_id = cursor.lastrowid
        conn.commit()
        
        # 记录详细的操作日志
        content_preview = data['content'][:50] + '...' if len(data['content']) > 50 else data['content']
        log_action = f'发布公告 - ID: {new_announcement_id}, 内容预览: "{content_preview}"'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        
        conn.commit()
        
        return jsonify(i18n_backend.get_success_response('announcement_created_success'))
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/feedback', methods=['POST'])
def create_feedback():
    """Create a new feedback"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    data = request.get_json()
    if not data or not data.get('title') or not data.get('content'):
        return jsonify({'error': 'Missing required fields'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT INTO feedback (title, content, user_id, created_at)
            VALUES (?, ?, ?, datetime('now'))
        ''', (data['title'], data['content'], session['user_id']))
        
        conn.commit()
        
        # Log the action
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], f'Submitted feedback: {data["title"]}'))
        
        conn.commit()
        
        return jsonify({'success': True, 'message': '反馈提交成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/feedback')
def get_feedback():
    """Get all feedback"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_view_feedback'):
        return jsonify({'error': 'Access denied'}), 403
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT f.*, u.name as user_name, ru.name as replied_by_name
            FROM feedback f
            LEFT JOIN users u ON f.user_id = u.id
            LEFT JOIN users ru ON f.replied_by_id = ru.id
            ORDER BY f.created_at DESC
        ''')
        feedback_list = []
        for row in cursor.fetchall():
            feedback_list.append({
                'id': row['id'],
                'title': row['title'],
                'content': row['content'],
                'user_id': row['user_id'],
                'created_at': row['created_at'],
                'reply': row['reply'],
                'replied_by_id': row['replied_by_id'],
                'is_read_by_user': bool(row['is_read_by_user']),
                'user_name': row['user_name'],
                'replied_by_name': row['replied_by_name']
            })
        return jsonify(feedback_list)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/feedback/<int:feedback_id>/reply', methods=['POST'])
def reply_feedback(feedback_id):
    """Reply to feedback"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_view_feedback'):
        return jsonify({'error': 'Access denied'}), 403
    
    data = request.get_json()
    if not data or not data.get('reply'):
        return jsonify({'error': 'Missing reply content'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        # 先获取反馈信息，包括提交者ID
        cursor.execute('''
            SELECT f.*, u.name as submitter_name 
            FROM feedback f
            LEFT JOIN users u ON f.user_id = u.id
            WHERE f.id = ?
        ''', (feedback_id,))
        feedback = cursor.fetchone()
        
        if not feedback:
            return jsonify({'error': 'Feedback not found'}), 404
        
        # 更新反馈回复
        cursor.execute('''
            UPDATE feedback SET reply = ?, replied_by_id = ?
            WHERE id = ?
        ''', (data['reply'], session['user_id'], feedback_id))
        
        # 创建通知给反馈提交者
        # 获取用户语言设置
        cursor.execute('SELECT language FROM users WHERE id = ?', (feedback['user_id'],))
        user_lang_result = cursor.fetchone()
        user_language = user_lang_result[0] if user_lang_result and user_lang_result[0] else 'en'

        # 使用国际化翻译
        notification_title = i18n_backend.t('feedback_reply_notification_title', user_language)
        reply_preview = data["reply"][:100] + ("..." if len(data["reply"]) > 100 else "")
        notification_content = i18n_backend.t('feedback_reply_notification_content', user_language).format(
            title=feedback["title"],
            reply=reply_preview
        )

        cursor.execute('''
            INSERT INTO notifications (user_id, title, content, type, created_at, read_status)
            VALUES (?, ?, ?, ?, datetime('now'), 0)
        ''', (
            feedback['user_id'],
            notification_title,
            notification_content,
            'feedback_reply'
        ))
        
        conn.commit()
        
        # 记录详细的操作日志
        reply_preview = data['reply'][:50] + '...' if len(data['reply']) > 50 else data['reply']
        log_action = f'回复反馈 - ID: {feedback_id}, 标题: "{feedback["title"]}", 提交者: {feedback["submitter_name"]}, 回复内容: "{reply_preview}"'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        
        conn.commit()
        
        return jsonify({'success': True, 'message': '回复成功，用户将收到通知'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/labels/<int:label_id>', methods=['GET'])
def get_label(label_id):
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    conn = db.create_connection()
    cursor = conn.cursor()
    try:
        cursor.execute('''
            SELECT id, name, content, type, prompt_for_quantity, problem_solve, department_names, v_align, copy_info, print_quantity, label_code_shortcut, usage_question_enabled, usage_question_title, is_hidden
            FROM labels WHERE id = ?
        ''', (label_id,))
        row = cursor.fetchone()
        if not row:
            return jsonify({'error': '标签不存在'}), 404
        label = {
            'id': row[0],
            'name': row[1],
            'content': row[2],
            'type': row[3],
            'print_prompt': bool(row[4]),
            'problem_solve': bool(row[5]) if row[5] is not None else False,
            'department_names': row[6] or '',
            'v_align': row[7] or 'center',
            'copy_info': row[8] or '',
            'print_quantity': row[9] or 1,
            'label_code_shortcut': row[10] or '',
            'usage_question_enabled': bool(row[11]) if len(row) > 11 and row[11] is not None else False,
            'usage_question_title': row[12] or '' if len(row) > 12 else '',
            'is_hidden': bool(row[13]) if len(row) > 13 and row[13] is not None else False
        }
        return jsonify(label)
    except Exception as e:
        print('加载单个标签失败详细报错:', e)
        return jsonify({'error': '加载标签失败'}), 500
    finally:
        conn.close()

# Additional API routes for complete functionality
@app.route('/api/user-groups/<int:group_id>', methods=['PUT'])
def update_user_group(group_id):
    """Update a user group"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    # 添加权限验证 - 需要用户组管理权限
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_user_groups'):
        return jsonify({'error': 'Access denied'}), 403
    
    data = request.get_json()
    
    # --- Start Debug Logging ---
    print("\n--- [DEBUG] Updating User Group ---")
    print(f"Received data for group_id {group_id}: {data}")
    # --- End Debug Logging ---

    if not data or not data.get('name'):
        return jsonify({'error': 'Missing required fields'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('UPDATE user_groups SET name = ? WHERE id = ?', (data['name'], group_id))
        
        # 处理所有权限字段
        p = data.get('permissions', {})
        
        # 🔧 自动设置后台管理权限：如果拥有后台管理权限分组下的任何一项权限，就设置can_manage_backend为True
        backend_permissions = [
            'can_manage_users', 'can_manage_users_add', 'can_manage_users_edit', 'can_manage_users_delete',
            'can_manage_departments', 'can_manage_departments_add', 'can_manage_departments_edit', 'can_manage_departments_delete',
            'can_manage_user_groups', 'can_manage_user_groups_add', 'can_manage_user_groups_edit', 'can_manage_user_groups_delete',
            'can_manage_labels', 'can_manage_labels_add', 'can_manage_labels_edit', 'can_manage_labels_delete',
            'can_manage_announcements', 'can_manage_announcements_add', 'can_manage_announcements_edit', 'can_manage_announcements_delete',
            'can_view_feedback', 'can_reply_feedback', 'can_delete_feedback',
            'can_view_audit_log', 'can_clear_audit_log',
            'can_manage_barcode_prefixes', 'can_manage_barcode_prefixes_add', 'can_manage_barcode_prefixes_edit', 'can_manage_barcode_prefixes_delete',
            'can_manage_chatbot_kb', 'can_manage_chatbot_kb_add', 'can_manage_chatbot_kb_edit', 'can_manage_chatbot_kb_delete',
            'can_manage_system_settings'
        ]
        
        # 检查是否有任何后台管理权限
        has_backend_permission = any(p.get(perm, False) for perm in backend_permissions)
        p['can_manage_backend'] = has_backend_permission
        
        vals = {
            'group_id': group_id,
            # 后台管理权限
            'can_manage_backend': int(bool(p.get('can_manage_backend', False))),
            'can_manage_users': int(bool(p.get('can_manage_users', False))),
            'can_manage_users_add': int(bool(p.get('can_manage_users_add', False))),
            'can_manage_users_edit': int(bool(p.get('can_manage_users_edit', False))),
            'can_manage_users_delete': int(bool(p.get('can_manage_users_delete', False))),
            'can_manage_departments': int(bool(p.get('can_manage_departments', False))),
            'can_manage_departments_add': int(bool(p.get('can_manage_departments_add', False))),
            'can_manage_departments_edit': int(bool(p.get('can_manage_departments_edit', False))),
            'can_manage_departments_delete': int(bool(p.get('can_manage_departments_delete', False))),
            'can_manage_user_groups': int(bool(p.get('can_manage_user_groups', False))),
            'can_manage_user_groups_add': int(bool(p.get('can_manage_user_groups_add', False))),
            'can_manage_user_groups_edit': int(bool(p.get('can_manage_user_groups_edit', False))),
            'can_manage_user_groups_delete': int(bool(p.get('can_manage_user_groups_delete', False))),
            'can_manage_labels': int(bool(p.get('can_manage_labels', False))),
            'can_manage_labels_add': int(bool(p.get('can_manage_labels_add', False))),
            'can_manage_labels_edit': int(bool(p.get('can_manage_labels_edit', False))),
            'can_manage_labels_delete': int(bool(p.get('can_manage_labels_delete', False))),
            'can_manage_category_center': int(bool(p.get('can_manage_category_center', False))),
            'can_manage_category_center_add': int(bool(p.get('can_manage_category_center_add', False))),
            'can_manage_category_center_edit': int(bool(p.get('can_manage_category_center_edit', False))),
            'can_manage_category_center_delete': int(bool(p.get('can_manage_category_center_delete', False))),
            'can_manage_announcements': int(bool(p.get('can_manage_announcements', False))),
            'can_manage_announcements_add': int(bool(p.get('can_manage_announcements_add', False))),
            'can_manage_announcements_edit': int(bool(p.get('can_manage_announcements_edit', False))),
            'can_manage_announcements_delete': int(bool(p.get('can_manage_announcements_delete', False))),
            'can_view_feedback': int(bool(p.get('can_view_feedback', False))),
            'can_reply_feedback': int(bool(p.get('can_reply_feedback', False))),
            'can_delete_feedback': int(bool(p.get('can_delete_feedback', False))),
            'can_view_audit_log': int(bool(p.get('can_view_audit_log', False))),
            'can_clear_audit_log': int(bool(p.get('can_clear_audit_log', False))),
            'can_manage_barcode_prefixes': int(bool(p.get('can_manage_barcode_prefixes', False))),
            'can_manage_barcode_prefixes_add': int(bool(p.get('can_manage_barcode_prefixes_add', False))),
            'can_manage_barcode_prefixes_edit': int(bool(p.get('can_manage_barcode_prefixes_edit', False))),
            'can_manage_barcode_prefixes_delete': int(bool(p.get('can_manage_barcode_prefixes_delete', False))),
            'can_manage_chatbot_kb': int(bool(p.get('can_manage_chatbot_kb', False))),
            'can_manage_chatbot_kb_add': int(bool(p.get('can_manage_chatbot_kb_add', False))),
            'can_manage_chatbot_kb_edit': int(bool(p.get('can_manage_chatbot_kb_edit', False))),
            'can_manage_chatbot_kb_delete': int(bool(p.get('can_manage_chatbot_kb_delete', False))),
            
            # 前台功能权限
            'can_view_problem_solve': int(bool(p.get('can_view_problem_solve', False))),
            'can_reply_problem_solve': int(bool(p.get('can_reply_problem_solve', False))),
            'can_delete_problem_solve': int(bool(p.get('can_delete_problem_solve', False))),
            'can_reply_feedback': int(bool(p.get('can_reply_feedback', False))),
            'can_delete_feedback': int(bool(p.get('can_delete_feedback', False))),
            'can_print_labels': int(bool(p.get('can_print_labels', False))),
            'can_search_labels': int(bool(p.get('can_search_labels', False))),
            'can_copy_labels': int(bool(p.get('can_copy_labels', False))),
            'can_use_chat': int(bool(p.get('can_use_chat', False))),
            'can_chat_robot': int(bool(p.get('can_chat_robot', False))),
            'can_chat_public': int(bool(p.get('can_chat_public', False))),
            'can_review_knowledge': int(bool(p.get('can_review_knowledge', False))),
            'can_review_knowledge_submissions': int(bool(p.get('can_review_knowledge_submissions', False))),
            
            # 数据查看权限
            'can_view_statistics': int(bool(p.get('can_view_statistics', False))),
            'can_view_reports': int(bool(p.get('can_view_reports', False))),
            'can_export_data': int(bool(p.get('can_export_data', False))),
            
            # 系统设置权限
            'can_manage_refresh_permissions': int(bool(p.get('can_manage_refresh_permissions', False))),
            'can_backup_restore': int(bool(p.get('can_backup_restore', False))),
            'can_manage_security': int(bool(p.get('can_manage_security', False))),
            'can_manage_system_settings': int(bool(p.get('can_manage_system_settings', False))),
            'can_refresh_permissions': int(bool(p.get('can_refresh_permissions', False)))
        }
        
        # 构建动态SQL语句，包含所有权限字段
        permission_fields = list(vals.keys())
        permission_fields.remove('group_id')
        
        update_sql = f'''
            UPDATE permissions SET 
            {', '.join([f'{field} = :{field}' for field in permission_fields])}
            WHERE group_id = :group_id
        '''
        
        cursor.execute(update_sql, vals)
        
        # 如果没有更新任何行，说明没有权限记录，自动插入一条
        if cursor.rowcount == 0:
            insert_sql = f'''
                INSERT INTO permissions (
                    group_id, {', '.join(permission_fields)}
                )
                VALUES (
                    :group_id, {', '.join([':' + field for field in permission_fields])}
                )
            '''
            cursor.execute(insert_sql, vals)
        
        conn.commit()
        
        # 记录详细的操作日志
        permissions_list = []
        permission_names = {
            'can_manage_backend': '后台管理入口',
            'can_manage_users': '用户管理',
            'can_manage_users_add': '添加用户',
            'can_manage_users_edit': '编辑用户',
            'can_manage_users_delete': '删除用户',
            'can_manage_departments': '部门管理',
            'can_manage_departments_add': '添加部门',
            'can_manage_departments_edit': '编辑部门',
            'can_manage_departments_delete': '删除部门',
            'can_manage_user_groups': '用户组管理',
            'can_manage_user_groups_add': '添加用户组',
            'can_manage_user_groups_edit': '编辑用户组',
            'can_manage_user_groups_delete': '删除用户组',
            'can_manage_labels': '标签管理',
            'can_manage_labels_add': '添加标签',
            'can_manage_labels_edit': '编辑标签',
            'can_manage_labels_delete': '删除标签',
            'can_manage_announcements': '公告管理',
            'can_manage_announcements_add': '添加公告',
            'can_manage_announcements_edit': '编辑公告',
            'can_manage_announcements_delete': '删除公告',
            'can_view_feedback': '查看反馈',
            'can_reply_feedback': '回复反馈',
            'can_delete_feedback': '删除反馈',
            'can_view_audit_log': '操作日志',
            'can_clear_audit_log': '清空操作日志',
            'can_manage_barcode_prefixes': '条形码前缀管理',
            'can_manage_barcode_prefixes_add': '添加条形码前缀',
            'can_manage_barcode_prefixes_edit': '编辑条形码前缀',
            'can_manage_barcode_prefixes_delete': '删除条形码前缀',
            'can_manage_chatbot_kb': '聊天机器人词库管理',
            'can_manage_chatbot_kb_add': '添加聊天机器人词条',
            'can_manage_chatbot_kb_edit': '编辑聊天机器人词条',
            'can_manage_chatbot_kb_delete': '删除聊天机器人词条',
            'can_submit_knowledge': '知识提交权限',
            'can_review_knowledge_submissions': '知识提交审核权限',
            'can_view_problem_solve': '显示Problem Solve按钮',
            'can_reply_problem_solve': 'Problem Solve回复权限',
            'can_delete_problem_solve': 'Problem Solve删除权限',
            'can_reply_feedback': '反馈回复权限',
            'can_delete_feedback': '反馈删除权限',
            'can_print_labels': '标签打印权限',
            'can_search_labels': '标签搜索权限',
            'can_copy_labels': '标签复制权限',
            'can_view_statistics': '查看统计数据',
            'can_view_reports': '查看报表',
            'can_export_data': '导出数据',
            'can_manage_refresh_permissions': '权限刷新',
            'can_backup_restore': '备份恢复',
            'can_manage_security': '安全设置',
            'can_manage_system_settings': '系统设定',
            'can_refresh_permissions': '权限刷新'
        }
        
        for perm_key, perm_name in permission_names.items():
            if p.get(perm_key):
                permissions_list.append(perm_name)
        
        permissions_str = ', '.join(permissions_list) if permissions_list else '无权限'
        log_action = f'修改用户组 - ID: {group_id}, 名称: "{data["name"]}", 权限: {permissions_str}'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        
        conn.commit()
        return jsonify({'success': True, 'message': '用户组更新成功，相关用户需重新登录以使权限生效'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/user-groups/<int:group_id>', methods=['DELETE'])
def delete_user_group(group_id):
    """Delete a user group"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    # 添加权限验证 - 需要用户组管理权限
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_user_groups'):
        return jsonify({'error': 'Access denied'}), 403
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        # 先获取用户组信息用于日志记录
        cursor.execute('SELECT name FROM user_groups WHERE id = ?', (group_id,))
        group = cursor.fetchone()
        if not group:
            return jsonify({'error': '用户组不存在'}), 404
        
        group_name = group[0]
        
        cursor.execute('DELETE FROM permissions WHERE group_id = ?', (group_id,))
        cursor.execute('DELETE FROM user_groups WHERE id = ?', (group_id,))
        conn.commit()
        
        # 记录详细的操作日志
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], f'删除用户组 - ID: {group_id}, 名称: "{group_name}"'))
        
        conn.commit()
        
        return jsonify({'success': True, 'message': '用户组删除成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/admin/announcements/<int:announcement_id>', methods=['PUT'])
def update_announcement(announcement_id):
    """Update an announcement"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_announcements'):
        return jsonify({'error': 'Access denied'}), 403
    
    data = request.get_json()
    if not data or not data.get('content'):
        return jsonify({'error': 'Missing required fields'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        # 先获取原公告信息用于日志记录
        cursor.execute('SELECT content FROM announcements WHERE id = ?', (announcement_id,))
        old_announcement = cursor.fetchone()
        if not old_announcement:
            return jsonify({'error': '公告不存在'}), 404
        
        old_content = old_announcement[0]
        
        cursor.execute('''
            UPDATE announcements SET content = ?
            WHERE id = ?
        ''', (data['content'], announcement_id))
        
        conn.commit()
        
        # 记录详细的操作日志
        old_preview = old_content[:30] + '...' if len(old_content) > 30 else old_content
        new_preview = data['content'][:30] + '...' if len(data['content']) > 30 else data['content']
        log_action = f'修改公告 - ID: {announcement_id}, 内容: "{old_preview}" → "{new_preview}"'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        
        conn.commit()
        
        return jsonify({'success': True, 'message': '公告更新成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/admin/announcements/<int:announcement_id>', methods=['DELETE'])
def delete_announcement(announcement_id):
    """Delete an announcement"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_announcements'):
        return jsonify({'error': 'Access denied'}), 403
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        # 先获取公告信息用于日志记录
        cursor.execute('SELECT content FROM announcements WHERE id = ?', (announcement_id,))
        announcement = cursor.fetchone()
        if not announcement:
            return jsonify({'error': '公告不存在'}), 404
        
        content = announcement[0]
        
        cursor.execute('DELETE FROM announcements WHERE id = ?', (announcement_id,))
        conn.commit()
        
        # 记录详细的操作日志
        content_preview = content[:50] + '...' if len(content) > 50 else content
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], f'删除公告 - ID: {announcement_id}, 内容预览: "{content_preview}"'))
        
        conn.commit()
        
        return jsonify({'success': True, 'message': '公告删除成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/feedback/<int:feedback_id>', methods=['DELETE'])
def delete_feedback(feedback_id):
    """Delete feedback"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_view_feedback'):
        return jsonify({'error': 'Access denied'}), 403
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('DELETE FROM feedback WHERE id = ?', (feedback_id,))
        conn.commit()
        
        return jsonify({'success': True, 'message': '反馈删除成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/problem-solves/<int:problem_id>', methods=['DELETE'])
def delete_problem_solve(problem_id):
    """Delete problem solve"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_delete_problem_solve'):
        return jsonify({'error': 'Access denied'}), 403
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('DELETE FROM problem_solves WHERE id = ?', (problem_id,))
        conn.commit()
        
        return jsonify({'success': True, 'message': 'Problem Solve删除成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/barcode-prefixes/<int:prefix_id>', methods=['PUT'])
def update_barcode_prefix(prefix_id):
    """Update barcode prefix"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    data = request.get_json()
    if not data or not data.get('prefix'):
        return jsonify({'error': 'Missing required fields'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            UPDATE barcode_prefixes SET prefix = ?, print_count = ?
            WHERE id = ?
        ''', (data['prefix'], data.get('print_count', 1), prefix_id))
        
        conn.commit()
        
        return jsonify({'success': True, 'message': '条形码前缀更新成功'})
    except sqlite3.IntegrityError:
        return jsonify({'error': '前缀已存在'}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/barcode-prefixes/<int:prefix_id>', methods=['DELETE'])
def delete_barcode_prefix(prefix_id):
    """Delete barcode prefix"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('DELETE FROM barcode_prefixes WHERE id = ?', (prefix_id,))
        conn.commit()
        
        return jsonify({'success': True, 'message': '条形码前缀删除成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/chatbot-kb/<int:kb_id>', methods=['PUT'])
def update_chatbot_kb(kb_id):
    """Update chatbot knowledge base entry"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    data = request.get_json()
    if not data or not data.get('keyword') or not data.get('answer'):
        return jsonify({'error': 'Missing required fields'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            UPDATE chatbot_kb SET keyword = ?, answer = ?
            WHERE id = ?
        ''', (data['keyword'], data['answer'], kb_id))
        
        conn.commit()
        
        return jsonify({'success': True, 'message': '词条更新成功'})
    except sqlite3.IntegrityError:
        return jsonify({'error': '关键词已存在'}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/chatbot-kb/<int:kb_id>', methods=['DELETE'])
def delete_chatbot_kb(kb_id):
    """Delete chatbot knowledge base entry"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('DELETE FROM chatbot_kb WHERE id = ?', (kb_id,))
        conn.commit()
        
        return jsonify({'success': True, 'message': '词条删除成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/problem-solves', methods=['POST'])
def create_problem_solve():
    """Create a new problem solve entry"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    data = request.get_json()
    if not data or not data.get('label_id') or not data.get('content') or not data.get('title'):
        return jsonify({'error': 'Missing required fields'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT INTO problem_solves (label_id, submitted_by_id, title, content)
            VALUES (?, ?, ?, ?)
        ''', (data['label_id'], session['user_id'], data['title'], data['content']))
        
        conn.commit()
        
        # Log the action
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], f'Submitted problem solve for label ID: {data["label_id"]}, 标题: {data["title"]}'))
        
        conn.commit()
        
        return jsonify({'success': True, 'message': '问题已提交'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/problem-solve-list')
def problem_solve_list():
    """Problem solve list page"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_view_problem_solve'):
        return redirect(url_for('index'))
    
    return render_template('problem_solve_list.html', 
                         user_info=session.get('user_info'),
                         permissions=permissions)

@app.route('/api/problem-solves/<int:problem_id>/reply', methods=['POST'])
def reply_problem_solve(problem_id):
    """Reply to a problem solve submission"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_reply_problem_solve'):
        return jsonify({'error': 'Access denied'}), 403
    
    data = request.get_json()
    if not data or not data.get('reply'):
        return jsonify({'error': 'Missing reply content'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        # Check if problem exists and get submitter info
        cursor.execute('''
            SELECT ps.id, ps.title, ps.content, ps.submitted_by_id, ps.label_id, ps.created_at, ps.reply, ps.replied_by_id, ps.replied_at,
                   u.name as submitter_name, l.name as label_name
            FROM problem_solves ps
            LEFT JOIN users u ON ps.submitted_by_id = u.id
            LEFT JOIN labels l ON ps.label_id = l.id
            WHERE ps.id = ?
        ''', (problem_id,))
        problem_row = cursor.fetchone()

        if not problem_row:
            return jsonify({'error': 'Problem not found'}), 404

        # Convert to dict for easier access
        problem = {
            'id': problem_row[0],
            'title': problem_row[1],
            'content': problem_row[2],
            'submitted_by_id': problem_row[3],
            'label_id': problem_row[4],
            'created_at': problem_row[5],
            'reply': problem_row[6],
            'replied_by_id': problem_row[7],
            'replied_at': problem_row[8],
            'submitter_name': problem_row[9],
            'label_name': problem_row[10]
        }
        
        # Update with reply
        cursor.execute('''
            UPDATE problem_solves 
            SET reply = ?, replied_by_id = ?, replied_at = datetime('now')
            WHERE id = ?
        ''', (data['reply'], session['user_id'], problem_id))
        
        # Create notification for the original submitter
        # 获取用户语言设置
        cursor.execute('SELECT language FROM users WHERE id = ?', (problem['submitted_by_id'],))
        user_lang_result = cursor.fetchone()
        user_language = user_lang_result[0] if user_lang_result and user_lang_result[0] else 'en'

        # 使用国际化翻译
        notification_title = i18n_backend.t('problem_reply_notification_title', user_language)
        reply_preview = data["reply"][:100] + ("..." if len(data["reply"]) > 100 else "")
        notification_content = i18n_backend.t('problem_reply_notification_content', user_language).format(
            label=problem["label_name"],
            reply=reply_preview
        )

        cursor.execute('''
            INSERT INTO notifications (user_id, title, content, type, created_at, read_status)
            VALUES (?, ?, ?, ?, datetime('now'), 0)
        ''', (
            problem['submitted_by_id'],
            notification_title,
            notification_content,
            'problem_reply'
        ))
        
        conn.commit()
        
        # Log the action
        reply_preview = data['reply'][:50] + '...' if len(data['reply']) > 50 else data['reply']
        log_action = f'回复Problem Solve - ID: {problem_id}, 标签: "{problem["label_name"]}", 提交者: {problem["submitter_name"]}, 回复内容: "{reply_preview}"'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        
        conn.commit()
        
        return jsonify({'success': True, 'message': '回复已提交，用户将收到通知'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/notifications')
def get_notifications():
    """Get user notifications"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT id, title, content, type, created_at, read_status
            FROM notifications 
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT 10
        ''', (session['user_id'],))
        
        notifications = []
        for row in cursor.fetchall():
            notifications.append({
                'id': row[0],
                'title': row[1],
                'content': row[2],
                'type': row[3],
                'created_at': row[4],
                'read_status': bool(row[5])
            })
        
        return jsonify(notifications)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/notifications/<int:notification_id>/read', methods=['POST'])
def mark_notification_read(notification_id):
    """Mark notification as read"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        # 先检查通知是否存在且属于当前用户
        cursor.execute('''
            SELECT id, read_status FROM notifications 
            WHERE id = ? AND user_id = ?
        ''', (notification_id, session['user_id']))
        
        notification = cursor.fetchone()
        if not notification:
            return jsonify({'error': 'Notification not found'}), 404
        
        notification_id_db, current_read_status = notification
        print(f"标记通知已读 - ID: {notification_id_db}, 当前状态: {current_read_status}, 用户: {session['user_id']}")
        
        # 更新为已读状态
        cursor.execute('''
            UPDATE notifications 
            SET read_status = 1 
            WHERE id = ? AND user_id = ?
        ''', (notification_id, session['user_id']))
        
        # 验证更新是否成功
        rows_affected = cursor.rowcount
        print(f"更新影响行数: {rows_affected}")
        
        conn.commit()
        
        # 返回更新后的通知信息
        cursor.execute('''
            SELECT id, title, content, type, created_at, read_status
            FROM notifications 
            WHERE id = ? AND user_id = ?
        ''', (notification_id, session['user_id']))
        
        updated_notification = cursor.fetchone()
        if updated_notification:
            return jsonify({
                'success': True,
                'message': 'Notification marked as read',
                'notification': {
                    'id': updated_notification[0],
                    'title': updated_notification[1],
                    'content': updated_notification[2],
                    'type': updated_notification[3],
                    'created_at': updated_notification[4],
                    'read_status': bool(updated_notification[5])
                }
            })
        else:
            return jsonify({'error': 'Failed to retrieve updated notification'}), 500
            
    except Exception as e:
        print(f"标记通知已读时出错: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/labels', methods=['POST'])
def create_label():
    """Create a new label"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_labels'):
        return jsonify({'error': 'Access denied'}), 403
    
    data = request.get_json()
    if not data or not data.get('name') or not data.get('type') or not data.get('content'):
        return jsonify({'error': 'Missing required fields'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        prompt_for_quantity = 1 if data.get('should_remind', False) else 0
        department_names = data.get('department_names', '')
        v_align = data.get('v_align', 'center')
        copy_info = data.get('copy_info', '')
        print_quantity = data.get('print_quantity', 1)
        label_code_shortcut = data.get('label_code_shortcut', '')
        usage_question_enabled = 1 if data.get('usage_question_enabled', False) else 0
        usage_question_title = data.get('usage_question_title', '')

        cursor.execute('''
            INSERT INTO labels (name, type, content, department_names, prompt_for_quantity, v_align, copy_info, print_quantity, label_code_shortcut, usage_question_enabled, usage_question_title, is_hidden)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['name'],
            data['type'],
            data['content'],
            department_names,
            prompt_for_quantity,
            v_align,
            copy_info,
            print_quantity,
            label_code_shortcut,
            usage_question_enabled,
            usage_question_title,
            data.get('is_hidden', False)
        ))
        new_label_id = cursor.lastrowid
        
        # 记录详细的操作日志
        log_action = f'添加标签 - ID: {new_label_id}, 名称: "{data["name"]}", 类型: {data["type"]}, 部门: {department_names or "无"}, 复制信息: {copy_info or "无"}, 打印数量: {print_quantity}, 标签简称: {label_code_shortcut or "无"}'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        
        conn.commit()
        return jsonify({'success': True, 'id': new_label_id, 'message': '标签添加成功'})
    except Exception as e:
        print(f"Error creating label: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()





@app.route('/api/feedback/unreplied-count')
def feedback_unreplied_count():
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    permissions = session.get('permissions', {})
    if not permissions.get('can_view_feedback'):
        return jsonify({'error': 'Access denied'}), 403
    conn = db.create_connection()
    cursor = conn.cursor()
    try:
        cursor.execute("SELECT COUNT(*) FROM feedback WHERE reply IS NULL OR reply = ''")
        count = cursor.fetchone()[0]
        return jsonify({'count': count})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/labels/<int:label_id>', methods=['PUT'])
def update_label(label_id):
    """Update a label"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_labels'):
        return jsonify({'error': 'Access denied'}), 403
    
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        # 先获取原标签信息用于日志记录
        cursor.execute('SELECT name, type, department_names, copy_info, print_quantity, label_code_shortcut FROM labels WHERE id = ?', (label_id,))
        old_label = cursor.fetchone()
        if not old_label:
            return jsonify({'error': '标签不存在'}), 404
        
        old_name, old_type, old_dept, old_copy, old_qty, old_shortcut = old_label
        
        # Renamed on the frontend, map it back to the DB column
        prompt_for_quantity = data.get('should_remind', False)
        v_align = data.get('v_align', 'center')
        copy_info = data.get('copy_info', '')
        print_quantity = data.get('print_quantity', 1)
        label_code_shortcut = data.get('label_code_shortcut', '')
        usage_question_enabled = 1 if data.get('usage_question_enabled', False) else 0
        usage_question_title = data.get('usage_question_title', '')
        new_name = data.get('name')
        new_type = data.get('type', 'normal')
        new_dept = data.get('department_names', '')

        cursor.execute('''
            UPDATE labels
            SET name = ?, type = ?, content = ?, department_names = ?, prompt_for_quantity = ?, v_align = ?, copy_info = ?, print_quantity = ?, label_code_shortcut = ?, usage_question_enabled = ?, usage_question_title = ?, is_hidden = ?
            WHERE id = ?
        ''', (
            new_name,
            new_type,
            data.get('content'),
            new_dept,
            1 if prompt_for_quantity else 0,
            v_align,
            copy_info,
            print_quantity,
            label_code_shortcut,
            usage_question_enabled,
            usage_question_title,
            1 if data.get('is_hidden', False) else 0,
            label_id
        ))
        
        # 记录详细的操作日志
        changes = []
        if old_name != new_name:
            changes.append(f'名称: "{old_name}" → "{new_name}"')
        if old_type != new_type:
            changes.append(f'类型: {old_type} → {new_type}')
        if old_dept != new_dept:
            changes.append(f'部门: {old_dept or "无"} → {new_dept or "无"}')
        if old_copy != copy_info:
            changes.append(f'复制信息: {old_copy or "无"} → {copy_info or "无"}')
        if old_qty != print_quantity:
            changes.append(f'打印数量: {old_qty} → {print_quantity}')
        if old_shortcut != label_code_shortcut:
            changes.append(f'标签简称: {old_shortcut or "无"} → {label_code_shortcut or "无"}')
        
        log_action = f'修改标签 - ID: {label_id}, 名称: "{new_name}", 修改内容: {", ".join(changes) if changes else "无"}'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        
        conn.commit()
        return jsonify({'success': True, 'message': '标签更新成功'})
    except Exception as e:
        print(f"Error updating label: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/labels/<int:label_id>', methods=['DELETE'])
def delete_label(label_id):
    """Delete a label"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_labels'):
        return jsonify({'error': 'Access denied'}), 403
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        # 先获取标签信息用于日志记录
        cursor.execute('SELECT name, type, department_names, copy_info, print_quantity, label_code_shortcut FROM labels WHERE id = ?', (label_id,))
        label = cursor.fetchone()
        if not label:
            return jsonify({'error': '标签不存在'}), 404
        
        label_name, label_type, dept_names, copy_info, print_qty, shortcut = label
        
        cursor.execute('DELETE FROM labels WHERE id = ?', (label_id,))
        
        # 记录详细的操作日志
        log_action = f'删除标签 - ID: {label_id}, 名称: "{label_name}", 类型: {label_type}, 部门: {dept_names or "无"}, 复制信息: {copy_info or "无"}, 打印数量: {print_qty}, 标签简称: {shortcut or "无"}'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        
        conn.commit()
        
        return jsonify({'success': True, 'message': '标签删除成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/barcode-prefixes')
def get_barcode_prefixes():
    """Get all barcode prefixes"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('SELECT * FROM barcode_prefixes ORDER BY prefix')
        prefixes = []
        for row in cursor.fetchall():
            prefixes.append({
                'id': row[0],
                'prefix': row[1],
                'print_count': row[2]
            })
        return jsonify(prefixes)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/barcode-prefixes', methods=['POST'])
def create_barcode_prefix():
    """Create a new barcode prefix"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    data = request.get_json()
    if not data or not data.get('prefix'):
        return jsonify({'error': 'Missing required fields'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT INTO barcode_prefixes (prefix, print_count)
            VALUES (?, ?)
        ''', (data['prefix'], data.get('print_count', 1)))
        
        new_prefix_id = cursor.lastrowid
        conn.commit()
        
        # 记录详细的操作日志
        log_action = f'添加条形码前缀 - ID: {new_prefix_id}, 前缀: "{data["prefix"]}", 打印数量: {data.get("print_count", 1)}'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        
        conn.commit()
        
        return jsonify({'success': True, 'message': '条形码前缀创建成功'})
    except sqlite3.IntegrityError:
        return jsonify({'error': '前缀已存在'}), 400
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/chatbot-kb')
def get_chatbot_kb():
    """Get all chatbot knowledge base entries"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    # 检查机器人聊天权限
    permissions = session.get('permissions', {})
    if not permissions.get('can_chat_robot'):
        return jsonify(i18n_backend.get_error_response('no_robot_chat_permission', 403))
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('SELECT * FROM chatbot_kb ORDER BY keyword')
        kb_entries = []
        for row in cursor.fetchall():
            kb_entries.append({
                'id': row[0],
                'keyword': row[1],
                'answer': row[2]
            })
        return jsonify(kb_entries)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/chatbot-kb', methods=['POST'])
def create_chatbot_kb():
    """Create a new chatbot knowledge base entry"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    data = request.get_json()
    if not data or not data.get('keyword') or not data.get('answer'):
        return jsonify({'error': 'Missing required fields'}), 400
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT INTO chatbot_kb (keyword, answer)
            VALUES (?, ?)
        ''', (data['keyword'], data['answer']))
        
        new_kb_id = cursor.lastrowid
        conn.commit()
        
        # 记录详细的操作日志
        answer_preview = data['answer'][:50] + '...' if len(data['answer']) > 50 else data['answer']
        log_action = f'添加聊天机器人词条 - ID: {new_kb_id}, 关键词: "{data["keyword"]}", 回答预览: "{answer_preview}"'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        
        conn.commit()
        
        return jsonify(i18n_backend.get_success_response('chatbot_kb_created_success'))
    except sqlite3.IntegrityError:
        return jsonify(i18n_backend.get_error_response('keyword_exists', 400))
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/audit-log')
def get_audit_log():
    """Get audit log"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    # 添加权限验证 - 需要查看操作日志权限
    permissions = session.get('permissions', {})
    if not permissions.get('can_view_audit_log'):
        return jsonify({'error': 'Access denied'}), 403

    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT al.*, u.name as user_name
            FROM audit_log al
            LEFT JOIN users u ON al.user_id = u.id
            ORDER BY al.timestamp DESC
            LIMIT 100
        ''')
        log_entries = []
        for row in cursor.fetchall():
            log_entries.append({
                'id': row[0],
                'user_id': row[1],
                'action': row[2],
                'timestamp': row[3],
                'user_name': row[4]
            })
        return jsonify(log_entries)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/audit-log', methods=['DELETE'])
def clear_audit_log():
    """Clear all audit log entries"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    
    # 添加权限验证 - 需要清空操作日志权限
    permissions = session.get('permissions', {})
    if not permissions.get('can_clear_audit_log'):
        return jsonify({'error': 'Access denied'}), 403
    
    conn = db.create_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute('DELETE FROM audit_log')
        conn.commit()
        
        return jsonify({'success': True, 'message': '操作日志清空成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/problem-solves', methods=['GET'])
def get_problem_solves():
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    permissions = session.get('permissions', {})
    if not permissions.get('can_view_problem_solve'):
        return jsonify({'error': 'Access denied'}), 403

    conn = db.create_connection()
    cursor = conn.cursor()
    try:
        cursor.execute('''
            SELECT ps.*, u.code as submitted_by_code, u.name as submitted_by_name, l.name as label_name
            FROM problem_solves ps
            LEFT JOIN users u ON ps.submitted_by_id = u.id
            LEFT JOIN labels l ON ps.label_id = l.id
            ORDER BY ps.created_at DESC
        ''')
        rows = cursor.fetchall()
        problems = []
        for row in rows:
            problems.append(dict(zip([col[0] for col in cursor.description], row)))
        return jsonify(problems)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/refresh-permissions', methods=['POST'])
def refresh_permissions():
    """刷新当前用户的权限并返回最新权限"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    user_id = session['user_id']
    conn = db.create_connection()
    user_query = '''
        SELECT u.*, d.name as department_name, p.* FROM users u
        LEFT JOIN departments d ON u.department_id = d.id
        LEFT JOIN permissions p ON u.group_id = p.group_id
        WHERE u.id = ?
    '''
    user = conn.execute(user_query, (user_id,)).fetchone()
    conn.close()
    if not user:
        return jsonify({'error': 'User not found'}), 404
    permissions = {key: user[key] for key in user.keys() if key not in ['id', 'name', 'card_id', 'code', 'department_id', 'group_id', 'department_name']}
    # 自动设置后台管理权限
    backend_permissions = [
        'can_manage_users', 'can_manage_users_add', 'can_manage_users_edit', 'can_manage_users_delete',
        'can_manage_departments', 'can_manage_departments_add', 'can_manage_departments_edit', 'can_manage_departments_delete',
        'can_manage_user_groups', 'can_manage_user_groups_add', 'can_manage_user_groups_edit', 'can_manage_user_groups_delete',
        'can_manage_labels', 'can_manage_labels_add', 'can_manage_labels_edit', 'can_manage_labels_delete',
        'can_manage_announcements', 'can_manage_announcements_add', 'can_manage_announcements_edit', 'can_manage_announcements_delete',
        'can_view_feedback', 'can_reply_feedback', 'can_delete_feedback',
        'can_view_audit_log',
        'can_manage_barcode_prefixes', 'can_manage_barcode_prefixes_add', 'can_manage_barcode_prefixes_edit', 'can_manage_barcode_prefixes_delete',
        'can_manage_chatbot_kb', 'can_manage_chatbot_kb_add', 'can_manage_chatbot_kb_edit', 'can_manage_chatbot_kb_delete',
        'can_manage_system_settings'
    ]
    has_backend_permission = any(permissions.get(perm, False) for perm in backend_permissions)
    permissions['can_manage_backend'] = has_backend_permission

    # 如果有后台管理权限，自动给予Problem Solve相关权限
    if has_backend_permission:
        permissions['can_view_problem_solve'] = True
        permissions['can_reply_problem_solve'] = True
        permissions['can_delete_problem_solve'] = True
        # 管理员也应该有词库审核权限
        permissions['can_review_knowledge_submissions'] = True
        permissions['can_review_knowledge'] = True

    # 默认给所有用户知识提交权限
    permissions['can_submit_knowledge'] = True

    session['permissions'] = permissions
    return jsonify({'success': True, 'permissions': permissions})

@app.route('/api/users/check-unique')
def check_user_unique():
    """检查员工卡ID和代码是否唯一"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_users'):
        return jsonify({'error': 'Access denied'}), 403
    card_id = request.args.get('card_id')
    code = request.args.get('code')
    conn = db.create_connection()
    cursor = conn.cursor()
    result = {'card_id_exists': False, 'code_exists': False}
    try:
        if card_id:
            cursor.execute('SELECT 1 FROM users WHERE card_id = ?', (card_id,))
            if cursor.fetchone():
                result['card_id_exists'] = True
        if code:
            cursor.execute('SELECT 1 FROM users WHERE code = ?', (code,))
            if cursor.fetchone():
                result['code_exists'] = True
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/public-chat-messages', methods=['GET'])
def get_public_chat_messages():
    print("=== 获取公众聊天消息API被调用 ===")  # 调试信息
    if 'user_id' not in session:
        return jsonify(i18n_backend.get_error_response('not_authenticated', 401))

    # 检查公众聊天权限
    permissions = session.get('permissions', {})
    if not permissions.get('can_chat_public'):
        return jsonify(i18n_backend.get_error_response('no_public_chat_permission', 403))

    after = request.args.get('after')
    current_user_id = session['user_id']

    # 检查用户是否是真正的管理员（用户组为admin）
    conn = db.create_connection()
    cursor = conn.cursor()
    cursor.execute('''
        SELECT ug.name FROM users u
        LEFT JOIN user_groups ug ON u.group_id = ug.id
        WHERE u.id = ?
    ''', (current_user_id,))
    user_group = cursor.fetchone()

    is_admin = user_group and user_group[0].lower() == 'admin'
    print(f"获取聊天消息 - 用户ID: {current_user_id}, 用户组: {user_group[0] if user_group else 'None'}, 是否管理员: {is_admin}")  # 调试信息
    try:
        if after:
            if is_admin:
                # 管理员可以看到所有消息
                cursor.execute('''
                    SELECT pcm.*, u.name as user_name, d.name as department_name
                    FROM public_chat_messages pcm
                    LEFT JOIN users u ON pcm.user_id = u.id
                    LEFT JOIN departments d ON u.department_id = d.id
                    WHERE pcm.created_at > ?
                    ORDER BY pcm.created_at ASC LIMIT 100
                ''', (after,))
            else:
                # 普通用户只能看到：1. 非私密@消息 2. @自己的消息 3. 自己发送的@消息
                cursor.execute('''
                    SELECT pcm.*, u.name as user_name, d.name as department_name
                    FROM public_chat_messages pcm
                    LEFT JOIN users u ON pcm.user_id = u.id
                    LEFT JOIN departments d ON u.department_id = d.id
                    WHERE pcm.created_at > ? AND (
                        pcm.is_private_mention = 0 OR
                        pcm.mentioned_user_id = ? OR
                        (pcm.user_id = ? AND pcm.is_private_mention = 1)
                    )
                    ORDER BY pcm.created_at ASC LIMIT 100
                ''', (after, current_user_id, current_user_id))
        else:
            if is_admin:
                # 管理员可以看到所有消息
                cursor.execute('''
                    SELECT pcm.*, u.name as user_name, d.name as department_name
                    FROM public_chat_messages pcm
                    LEFT JOIN users u ON pcm.user_id = u.id
                    LEFT JOIN departments d ON u.department_id = d.id
                    ORDER BY pcm.created_at DESC LIMIT 50
                ''')
            else:
                # 普通用户只能看到：1. 非私密@消息 2. @自己的消息 3. 自己发送的@消息
                cursor.execute('''
                    SELECT pcm.*, u.name as user_name, d.name as department_name
                    FROM public_chat_messages pcm
                    LEFT JOIN users u ON pcm.user_id = u.id
                    LEFT JOIN departments d ON u.department_id = d.id
                    WHERE (
                        pcm.is_private_mention = 0 OR
                        pcm.mentioned_user_id = ? OR
                        (pcm.user_id = ? AND pcm.is_private_mention = 1)
                    )
                    ORDER BY pcm.created_at DESC LIMIT 50
                ''', (current_user_id, current_user_id))

        rows = cursor.fetchall()
        print(f"查询返回 {len(rows)} 条消息")  # 调试信息
        # 倒序时前端需反转
        messages = [dict(zip([col[0] for col in cursor.description], row)) for row in rows]
        print(f"返回给前端 {len(messages)} 条消息")  # 调试信息
        return jsonify(messages[::-1] if not after else messages)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/users/search')
def search_users():
    """搜索用户（用于@功能）"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    query = request.args.get('q', '').strip()
    if not query:
        return jsonify([])

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        # 搜索用户名或用户代码
        cursor.execute('''
            SELECT id, name, code FROM users
            WHERE name LIKE ? OR code LIKE ?
            ORDER BY name
            LIMIT 10
        ''', (f'%{query}%', f'%{query}%'))

        users = []
        for row in cursor.fetchall():
            users.append({
                'id': row[0],
                'name': row[1],
                'code': row[2]
            })

        return jsonify(users)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

def parse_mentions(content):
    """解析消息中的@用户，返回被@的用户ID"""
    import re

    # 匹配@用户名或@用户代码
    mention_pattern = r'@([^\s]+)'
    mentions = re.findall(mention_pattern, content)

    if not mentions:
        return None

    # 查找被@的用户
    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        for mention in mentions:
            # 先按用户代码查找，再按用户名查找（不区分大小写）
            cursor.execute('''
                SELECT id FROM users
                WHERE LOWER(code) = LOWER(?) OR LOWER(name) = LOWER(?)
                LIMIT 1
            ''', (mention, mention))

            user = cursor.fetchone()
            if user:
                print(f"找到被@用户: {mention} -> 用户ID: {user[0]}")  # 调试信息
                return user[0]  # 返回第一个找到的用户ID

        print(f"未找到被@用户: {mentions}")  # 调试信息
        return None
    except Exception as e:
        print(f"解析@用户时出错: {e}")
        return None
    finally:
        conn.close()

@app.route('/api/public-chat-messages', methods=['POST'])
def post_public_chat_message():
    if 'user_id' not in session:
        return jsonify(i18n_backend.get_error_response('not_authenticated', 401))

    # 检查公众聊天权限
    permissions = session.get('permissions', {})
    if not permissions.get('can_chat_public'):
        return jsonify(i18n_backend.get_error_response('no_public_chat_permission', 403))

    data = request.get_json()
    content = data.get('content', '').strip()
    if not content:
        return jsonify(i18n_backend.get_error_response('message_empty', 400))

    user_id = session['user_id']
    user_name = session.get('user_name', '')

    # 解析@用户
    mentioned_user_id = parse_mentions(content)
    is_private_mention = mentioned_user_id is not None

    conn = db.create_connection()
    cursor = conn.cursor()
    try:
        cursor.execute('''
            INSERT INTO public_chat_messages (user_id, user_name, content, mentioned_user_id, is_private_mention)
            VALUES (?, ?, ?, ?, ?)
        ''', (user_id, user_name, content, mentioned_user_id, is_private_mention))
        conn.commit()
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/chat-mention-status')
def get_chat_mention_status():
    """检查用户是否有未读的@消息"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    current_user_id = session['user_id']

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        # 检查是否有@当前用户的未读消息
        cursor.execute('''
            SELECT COUNT(*) FROM public_chat_messages
            WHERE mentioned_user_id = ?
            AND is_private_mention = 1
            AND user_id != ?
            AND created_at > COALESCE(
                (SELECT last_read_at FROM user_chat_read_status WHERE user_id = ?),
                '1970-01-01 00:00:00'
            )
        ''', (current_user_id, current_user_id, current_user_id))

        mention_count = cursor.fetchone()[0]

        # 获取第一条未读@消息的ID（用于定位）
        first_mention_id = None
        if mention_count > 0:
            cursor.execute('''
                SELECT id FROM public_chat_messages
                WHERE mentioned_user_id = ?
                AND is_private_mention = 1
                AND user_id != ?
                AND created_at > COALESCE(
                    (SELECT last_read_at FROM user_chat_read_status WHERE user_id = ?),
                    '1970-01-01 00:00:00'
                )
                ORDER BY created_at ASC
                LIMIT 1
            ''', (current_user_id, current_user_id, current_user_id))

            result = cursor.fetchone()
            if result:
                first_mention_id = result[0]

        # 检查是否有普通的未读公开消息
        cursor.execute('''
            SELECT COUNT(*) FROM public_chat_messages
            WHERE (mentioned_user_id IS NULL OR mentioned_user_id != ? OR is_private_mention = 0)
            AND user_id != ?
            AND created_at > COALESCE(
                (SELECT last_read_at FROM user_chat_read_status WHERE user_id = ?),
                '1970-01-01 00:00:00'
            )
        ''', (current_user_id, current_user_id, current_user_id))

        public_count = cursor.fetchone()[0]

        return jsonify({
            'has_mentions': mention_count > 0,
            'mention_count': mention_count,
            'public_count': public_count,
            'total_unread': mention_count + public_count,
            'first_mention_id': first_mention_id
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/chat-read-status', methods=['POST'])
def update_chat_read_status():
    """更新用户的聊天阅读状态"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    current_user_id = session['user_id']

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        # 更新或插入用户的最后阅读时间
        cursor.execute('''
            INSERT OR REPLACE INTO user_chat_read_status (user_id, last_read_at)
            VALUES (?, CURRENT_TIMESTAMP)
        ''', (current_user_id,))

        conn.commit()
        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/system-settings', methods=['GET'])
def get_system_settings():
    """Get system settings"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    # 检查权限 - 需要系统设定管理权限
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_system_settings'):
        return jsonify({'error': 'Access denied'}), 403

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT setting_key, setting_value FROM system_settings')
        settings = {}
        for row in cursor.fetchall():
            settings[row[0]] = row[1]
        return jsonify(settings)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/system-settings', methods=['POST'])
def update_system_settings():
    """Update system settings"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    # 检查权限 - 需要系统设定管理权限
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_system_settings'):
        return jsonify({'error': 'Access denied'}), 403

    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        # 更新聊天记录保存时间设置
        if 'chat_retention_days' in data:
            cursor.execute('''
                UPDATE system_settings
                SET setting_value = ?, updated_at = datetime('now'), updated_by = ?
                WHERE setting_key = 'chat_retention_days'
            ''', (str(data['chat_retention_days']), session['user_id']))

        if 'chat_retention_hours' in data:
            cursor.execute('''
                UPDATE system_settings
                SET setting_value = ?, updated_at = datetime('now'), updated_by = ?
                WHERE setting_key = 'chat_retention_hours'
            ''', (str(data['chat_retention_hours']), session['user_id']))

        if 'chat_retention_minutes' in data:
            cursor.execute('''
                UPDATE system_settings
                SET setting_value = ?, updated_at = datetime('now'), updated_by = ?
                WHERE setting_key = 'chat_retention_minutes'
            ''', (str(data['chat_retention_minutes']), session['user_id']))

        conn.commit()

        # 记录操作日志
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], f'更新系统设定 - 聊天记录保存时间: {data.get("chat_retention_days", 0)}天{data.get("chat_retention_hours", 0)}小时{data.get("chat_retention_minutes", 0)}分钟'))

        conn.commit()

        return jsonify({'success': True, 'message': '系统设定更新成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/cleanup-chat-records', methods=['POST'])
def cleanup_chat_records():
    """Cleanup old chat records based on system settings"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    # 检查权限 - 需要系统设定管理权限
    permissions = session.get('permissions', {})
    if not permissions.get('can_manage_system_settings'):
        return jsonify({'error': 'Access denied'}), 403

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        # 获取系统设定
        cursor.execute('SELECT setting_key, setting_value FROM system_settings WHERE setting_key LIKE "chat_retention_%"')
        settings = {}
        for row in cursor.fetchall():
            settings[row[0]] = int(row[1])

        days = settings.get('chat_retention_days', 7)
        hours = settings.get('chat_retention_hours', 0)
        minutes = settings.get('chat_retention_minutes', 0)

        # 计算总分钟数
        total_minutes = days * 24 * 60 + hours * 60 + minutes

        # 删除过期的聊天记录
        cursor.execute('''
            DELETE FROM public_chat_messages
            WHERE created_at < datetime('now', '-{} minutes')
        '''.format(total_minutes))

        deleted_count = cursor.rowcount
        conn.commit()

        # 记录操作日志
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], f'清理过期聊天记录 - 删除了{deleted_count}条记录'))

        conn.commit()

        return jsonify({'success': True, 'message': f'成功清理了{deleted_count}条过期聊天记录'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/user-language', methods=['GET'])
def get_user_language():
    """获取用户语言设置"""
    if 'user_id' not in session:
        error_response, status_code = i18n_backend.get_error_response('not_authenticated', 401)
        return jsonify(error_response), status_code

    # 从数据库获取用户语言设置
    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT language FROM users WHERE id = ?', (session['user_id'],))
        result = cursor.fetchone()

        if result and result[0]:
            language = result[0]
        else:
            language = 'en'  # 默认英文

        # 同步到session
        session['language'] = language

        return jsonify({'language': language})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/user-language', methods=['POST'])
def set_user_language():
    """设置用户语言"""
    if 'user_id' not in session:
        return jsonify(i18n_backend.get_error_response('not_authenticated', 401))

    data = request.get_json()
    language = data.get('language', 'en')

    # 验证语言代码
    valid_languages = ['en', 'es', 'zh']
    if language not in valid_languages:
        return jsonify(i18n_backend.get_error_response('invalid_language', 400))

    # 保存到数据库
    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('UPDATE users SET language = ? WHERE id = ?', (language, session['user_id']))
        conn.commit()

        # 同时保存到session
        session['language'] = language

        # 记录操作日志
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], f'更改语言设置为: {language}'))

        conn.commit()

        return jsonify({'success': True, 'language': language})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# Knowledge Submissions API Routes
@app.route('/api/knowledge-submissions', methods=['POST'])
def submit_knowledge():
    """Submit knowledge for review"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    data = request.get_json()
    if not data or not data.get('question') or not data.get('answer'):
        return jsonify({'error': 'Missing required fields'}), 400

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('''
            INSERT INTO knowledge_submissions (user_id, question, answer, created_at)
            VALUES (?, ?, ?, datetime('now'))
        ''', (session['user_id'], data['question'], data['answer']))

        conn.commit()

        # Log the action
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], f'Submitted knowledge: {data["question"][:50]}...'))

        conn.commit()

        return jsonify({'success': True, 'message': 'Knowledge submitted for review'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/knowledge-submissions', methods=['GET'])
def get_knowledge_submissions():
    """Get pending knowledge submissions for review"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    permissions = session.get('permissions', {})
    if not permissions.get('can_review_knowledge_submissions'):
        return jsonify({'error': 'Access denied'}), 403

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('''
            SELECT ks.id, ks.question, ks.answer, ks.created_at, u.name as user_name
            FROM knowledge_submissions ks
            LEFT JOIN users u ON ks.user_id = u.id
            WHERE ks.status = 'pending'
            ORDER BY ks.created_at ASC
        ''')

        submissions = []
        for row in cursor.fetchall():
            submissions.append({
                'id': row[0],
                'question': row[1],
                'answer': row[2],
                'created_at': row[3],
                'user_name': row[4]
            })

        return jsonify(submissions)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/knowledge-submissions/pending')
def get_pending_submissions():
    """Get pending knowledge submissions for review"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    permissions = session.get('permissions', {})
    if not permissions.get('can_review_knowledge_submissions'):
        return jsonify({'error': 'Access denied'}), 403

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('''
            SELECT ks.id, ks.question, ks.answer, ks.created_at, u.name as user_name
            FROM knowledge_submissions ks
            LEFT JOIN users u ON ks.user_id = u.id
            WHERE ks.status = 'pending'
            ORDER BY ks.created_at DESC
        ''')

        submissions = []
        for row in cursor.fetchall():
            submissions.append({
                'id': row[0],
                'question': row[1],
                'answer': row[2],
                'created_at': row[3],
                'user_name': row[4]
            })

        return jsonify(submissions)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/knowledge-submissions/pending-count')
def get_pending_submissions_count():
    """Get count of pending knowledge submissions"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    permissions = session.get('permissions', {})
    if not permissions.get('can_review_knowledge_submissions'):
        return jsonify({'count': 0})

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT COUNT(*) FROM knowledge_submissions WHERE status = "pending"')
        count = cursor.fetchone()[0]
        return jsonify({'count': count})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/knowledge-submissions/<int:submission_id>/review', methods=['POST'])
def review_knowledge_submission(submission_id):
    """Review a knowledge submission (approve/reject)"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    permissions = session.get('permissions', {})
    if not permissions.get('can_review_knowledge_submissions'):
        return jsonify({'error': 'Access denied'}), 403

    data = request.get_json()
    if not data or data.get('action') not in ['approved', 'rejected']:
        return jsonify({'error': 'Invalid action'}), 400

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        # Get submission details
        cursor.execute('''
            SELECT ks.*, u.name as user_name, u.language
            FROM knowledge_submissions ks
            LEFT JOIN users u ON ks.user_id = u.id
            WHERE ks.id = ? AND ks.status = 'pending'
        ''', (submission_id,))

        submission = cursor.fetchone()
        if not submission:
            return jsonify({'error': 'Submission not found or already reviewed'}), 404

        action = data['action']

        # Update submission status
        cursor.execute('''
            UPDATE knowledge_submissions
            SET status = ?, reviewed_by_id = ?, reviewed_at = datetime('now')
            WHERE id = ?
        ''', (action, session['user_id'], submission_id))

        # If approved, add to chatbot knowledge base
        if action == 'approved':
            cursor.execute('''
                INSERT INTO chatbot_kb (keyword, answer)
                VALUES (?, ?)
            ''', (submission[2], submission[3]))  # question as keyword, answer

        # Create notification for submitter
        user_language = submission[7] if submission[7] else 'en'
        if action == 'approved':
            title = 'Knowledge Submission Approved' if user_language == 'en' else \
                   'Envío de Conocimiento Aprobado' if user_language == 'es' else \
                   '知识提交已通过审核'
            content = f'Your knowledge submission "{submission[2]}" has been approved and added to the knowledge base.' if user_language == 'en' else \
                     f'Su envío de conocimiento "{submission[2]}" ha sido aprobado y agregado a la base de conocimientos.' if user_language == 'es' else \
                     f'您提交的知识"{submission[2]}"已通过审核并添加到知识库中。'
        else:
            title = 'Knowledge Submission Rejected' if user_language == 'en' else \
                   'Envío de Conocimiento Rechazado' if user_language == 'es' else \
                   '知识提交未通过审核'
            content = f'Your knowledge submission "{submission[2]}" has been rejected.' if user_language == 'en' else \
                     f'Su envío de conocimiento "{submission[2]}" ha sido rechazado.' if user_language == 'es' else \
                     f'您提交的知识"{submission[2]}"未通过审核。'

        cursor.execute('''
            INSERT INTO notifications (user_id, title, content, type, created_at, read_status)
            VALUES (?, ?, ?, ?, datetime('now'), 0)
        ''', (submission[1], title, content, 'knowledge_review'))

        conn.commit()

        # Log the action
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], f'Reviewed knowledge submission: {action} - {submission[2][:50]}...'))

        conn.commit()

        return jsonify({'success': True, 'message': f'Submission {action} successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/departments/public')
def get_departments_public():
    """Get list of departments for registration (no auth required)"""
    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT id, name FROM departments ORDER BY name')
        departments = [{'id': row[0], 'name': row[1]} for row in cursor.fetchall()]
        return jsonify(departments)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/registration-enabled')
def check_registration_enabled():
    """Check if user registration is enabled"""
    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT setting_value FROM system_settings WHERE setting_key = ?', ('open_registration',))
        result = cursor.fetchone()
        enabled = result and result[0].lower() == 'true'
        return jsonify({'enabled': enabled})
    except Exception as e:
        return jsonify({'enabled': False, 'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/system-settings/registration', methods=['POST'])
def save_registration_settings():
    """Save registration settings"""
    # Check authentication
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    # Check permission
    if not check_permission('can_manage_system_settings'):
        return jsonify({'error': 'Access denied'}), 403

    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    enabled = data.get('open_registration', False)

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('''
            INSERT OR REPLACE INTO system_settings (setting_key, setting_value, description)
            VALUES (?, ?, ?)
        ''', ('open_registration', 'true' if enabled else 'false', 'Allow public user registration'))

        conn.commit()
        return jsonify({'success': True, 'message': 'Registration settings saved successfully'})
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/change-password', methods=['POST'])
def change_password():
    """Change user password"""
    # Check authentication
    if 'user_id' not in session:
        return jsonify({'error': 'Not authenticated'}), 401

    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    new_password = data.get('new_password', '').strip()
    user_id = session.get('user_id')

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        # Get user's language preference
        cursor.execute('SELECT language FROM users WHERE id = ?', (user_id,))
        user_lang_result = cursor.fetchone()
        user_language = user_lang_result[0] if user_lang_result and user_lang_result[0] else 'en'

        # Update password (empty string means no password)
        cursor.execute('UPDATE users SET password = ? WHERE id = ?', (new_password, user_id))
        conn.commit()

        if new_password:
            message = i18n_backend.t('password_changed_success', user_language)
        else:
            message = i18n_backend.t('password_removed_success', user_language)

        return jsonify({'success': True, 'message': message})
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/user-department/<card_id>', methods=['GET'])
def get_user_department(card_id):
    """Get user's department information for password reset"""
    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        # Get user's department
        cursor.execute('''
            SELECT u.department_id, d.name as department_name
            FROM users u
            LEFT JOIN departments d ON u.department_id = d.id
            WHERE u.card_id = ?
        ''', (card_id,))

        result = cursor.fetchone()
        if result:
            return jsonify({
                'department_id': result[0],
                'department_name': result[1] or 'Unknown Department'
            })
        else:
            return jsonify({'error': 'User not found'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/reset-password', methods=['POST'])
def reset_password():
    """Reset user password after verification"""
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    card_id = data.get('card_id', '').strip()
    employee_code = data.get('employee_code', '').strip()
    department_id = data.get('department_id', '').strip()
    new_password = data.get('new_password', '').strip()

    # Debug logging
    print(f"Reset password request - Card ID: {card_id}, Employee Code: '{employee_code}', Department ID: {department_id}")
    print(f"Employee code length: {len(employee_code) if employee_code else 'None'}")
    print(f"New password provided: {'Yes' if new_password else 'No (will clear password)'}")

    if not employee_code:
        print("Employee code is empty, returning error")
        return jsonify({'error': 'Login is required'}), 400

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        # Debug: Check what we're searching for
        print(f"Searching for user with card_id='{card_id}' and code='{employee_code}'")

        # First, let's see all users to debug
        cursor.execute('SELECT card_id, code, name FROM users')
        all_users = cursor.fetchall()
        print("All users in database:")
        for user_data in all_users:
            print(f"  card_id='{user_data[0]}', code='{user_data[1]}', name='{user_data[2]}'")

        # Verify user information matches exactly (only card_id and code)
        cursor.execute('''
            SELECT id, department_id FROM users
            WHERE card_id = ? AND code = ?
        ''', (card_id, employee_code))

        user = cursor.fetchone()
        print(f"Query result: {user}")

        if not user:
            print("No user found with the provided credentials")
            # Use default language for error message since user is not found
            return jsonify({'error': i18n_backend.t('verification_failed', 'en')}), 400

        # Get user's language preference
        cursor.execute('SELECT language FROM users WHERE id = ?', (user[0],))
        user_lang_result = cursor.fetchone()
        user_language = user_lang_result[0] if user_lang_result and user_lang_result[0] else 'en'

        # Update password based on user input
        if new_password:
            # Set new password
            cursor.execute('UPDATE users SET password = ? WHERE id = ?', (new_password, user[0]))
            message = i18n_backend.t('password_reset_updated_success', user_language)
        else:
            # Clear the password
            cursor.execute('UPDATE users SET password = ? WHERE id = ?', ('', user[0]))
            message = i18n_backend.t('password_reset_success', user_language)

        conn.commit()

        return jsonify({
            'success': True,
            'message': message,
            'redirect': True
        })
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/check-badge', methods=['POST'])
def check_badge():
    """Check if badge ID already exists"""
    data = request.get_json()
    if not data or 'badge_id' not in data:
        return jsonify({'error': 'Badge ID required'}), 400

    badge_id = data['badge_id'].strip()
    if not badge_id:
        return jsonify({'error': 'Badge ID required'}), 400

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT id FROM users WHERE card_id = ?', (badge_id,))
        exists = cursor.fetchone() is not None
        return jsonify({'exists': exists})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/check-login', methods=['POST'])
def check_login():
    """Check if login code already exists"""
    data = request.get_json()
    if not data or 'login_code' not in data:
        return jsonify({'error': 'Login code required'}), 400

    login_code = data['login_code'].strip()
    if not login_code:
        return jsonify({'error': 'Login code required'}), 400

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT id FROM users WHERE code = ?', (login_code,))
        exists = cursor.fetchone() is not None
        return jsonify({'exists': exists})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/register', methods=['POST'])
def register_user():
    """Register a new user"""
    # Check if registration is enabled
    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT setting_value FROM system_settings WHERE setting_key = ?', ('open_registration',))
        result = cursor.fetchone()
        if not result or result[0].lower() != 'true':
            return jsonify({'error': 'Registration is not enabled'}), 403
    except Exception as e:
        return jsonify({'error': 'Failed to check registration status'}), 500
    finally:
        conn.close()

    data = request.get_json()

    if not data:
        return jsonify({'error': 'No data provided'}), 400

    # Validate required fields
    required_fields = ['badge_id', 'full_name', 'login_code', 'department']
    for field in required_fields:
        if not data.get(field):
            return jsonify({'error': f'Missing required field: {field}'}), 400

    # Validate badge ID format
    badge_id = data['badge_id'].strip()
    if not badge_id.isdigit() or len(badge_id) < 5:
        return jsonify({'error': 'Badge ID must be at least 5 digits'}), 400

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        # Check if badge ID already exists
        cursor.execute('SELECT id FROM users WHERE card_id = ?', (badge_id,))
        if cursor.fetchone():
            return jsonify({'error': 'Badge ID already exists'}), 400

        # Check if login code already exists
        login_code = data['login_code'].strip()
        cursor.execute('SELECT id FROM users WHERE code = ?', (login_code,))
        if cursor.fetchone():
            return jsonify({'error': 'Login code already exists'}), 400

        # Get department ID
        cursor.execute('SELECT id FROM departments WHERE id = ?', (data['department'],))
        dept_row = cursor.fetchone()
        if not dept_row:
            return jsonify({'error': 'Invalid department'}), 400

        # Get default user group ID (User group)
        cursor.execute('SELECT id FROM user_groups WHERE name = ?', ('User',))
        user_group_row = cursor.fetchone()
        default_group_id = user_group_row[0] if user_group_row else 5  # Fallback to ID 5

        # Insert new user with default group
        password = data.get('password', '').strip() or ''  # Keep empty string for consistency
        cursor.execute('''
            INSERT INTO users (card_id, name, code, department_id, group_id, password)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (badge_id, data['full_name'].strip(), login_code, data['department'], default_group_id, password))

        conn.commit()
        return jsonify({'success': True, 'message': 'User registered successfully'})

    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/check-user', methods=['POST'])
def check_user():
    """Check if user exists and needs password"""
    data = request.get_json()
    card_id = data.get('card_id', '').strip()

    if not card_id:
        return jsonify({'error': 'Card ID required'}), 400

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT id, name, password FROM users WHERE card_id = ?', (card_id,))
        user = cursor.fetchone()

        if user:
            has_password = user[2] is not None and user[2].strip() != ''
            return jsonify({
                'exists': True,
                'name': user[1],
                'needs_password': has_password
            })
        else:
            return jsonify({'exists': False})

    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# 分类代码管理API
@app.route('/api/category-codes', methods=['GET'])
@login_required
def get_category_codes():
    if not check_permission('can_manage_labels'):
        return jsonify({'error': 'Insufficient permissions'}), 403

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('''
            SELECT id, name, copy_content, department_names, created_at
            FROM category_codes
            ORDER BY name
        ''')

        codes = []
        for row in cursor.fetchall():
            codes.append({
                'id': row[0],
                'name': row[1],
                'copy_content': row[2],
                'department_names': row[3] or '',
                'created_at': row[4]
            })

        return jsonify(codes)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/category-codes', methods=['POST'])
@login_required
def create_category_code():
    if not check_permission('can_manage_labels'):
        return jsonify({'error': 'Insufficient permissions'}), 403

    data = request.get_json()

    if not data.get('name') or not data.get('copy_content'):
        return jsonify({'error': 'Name and copy content are required'}), 400

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        department_names = ', '.join(data.get('department_names', []))

        cursor.execute('''
            INSERT INTO category_codes (name, copy_content, department_names)
            VALUES (?, ?, ?)
        ''', (data['name'], data['copy_content'], department_names))

        new_id = cursor.lastrowid
        conn.commit()

        # 记录操作日志
        log_action = f'添加分类代码 - ID: {new_id}, 名称: "{data["name"]}", 部门: {department_names or "无"}'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        conn.commit()

        return jsonify({'success': True, 'id': new_id, 'message': '分类代码添加成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/category-codes/<int:code_id>', methods=['PUT'])
@login_required
def update_category_code(code_id):
    if not check_permission('can_manage_labels'):
        return jsonify({'error': 'Insufficient permissions'}), 403

    data = request.get_json()

    if not data.get('name') or not data.get('copy_content'):
        return jsonify({'error': 'Name and copy content are required'}), 400

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        department_names = ', '.join(data.get('department_names', []))

        cursor.execute('''
            UPDATE category_codes
            SET name = ?, copy_content = ?, department_names = ?
            WHERE id = ?
        ''', (data['name'], data['copy_content'], department_names, code_id))

        conn.commit()

        # 记录操作日志
        log_action = f'编辑分类代码 - ID: {code_id}, 名称: "{data["name"]}", 部门: {department_names or "无"}'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        conn.commit()

        return jsonify({'success': True, 'message': '分类代码更新成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@app.route('/api/category-codes/<int:code_id>', methods=['DELETE'])
@login_required
def delete_category_code(code_id):
    if not check_permission('can_manage_labels'):
        return jsonify({'error': 'Insufficient permissions'}), 403

    conn = db.create_connection()
    cursor = conn.cursor()

    try:
        # 获取分类代码信息用于日志
        cursor.execute('SELECT name FROM category_codes WHERE id = ?', (code_id,))
        code_info = cursor.fetchone()

        if not code_info:
            return jsonify({'error': 'Category code not found'}), 404

        cursor.execute('DELETE FROM category_codes WHERE id = ?', (code_id,))
        conn.commit()

        # 记录操作日志
        log_action = f'删除分类代码 - ID: {code_id}, 名称: "{code_info[0]}"'
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, timestamp)
            VALUES (?, ?, datetime('now'))
        ''', (session['user_id'], log_action))
        conn.commit()

        return jsonify({'success': True, 'message': '分类代码删除成功'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

if __name__ == '__main__':
    # init_db() will be called by @app.before_request, no need to call it twice.
    print("Starting Flask server...")

    # 启动聊天记录自动清理线程
    start_cleanup_thread()

    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    except Exception as e:
        print("Flask 启动异常：", e)
    finally:
        # 确保清理线程停止
        stop_cleanup_thread()
