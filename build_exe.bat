@echo off
chcp 65001 >nul
echo ========================================
echo Destination Labels - 独立exe构建工具
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在检查主程序文件...
if not exist "label_printer_app.py" (
    echo ❌ 错误: 未找到主程序文件 label_printer_app.py
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo ✅ 主程序文件检查通过
echo.

echo 开始构建独立exe文件...
echo 这可能需要几分钟时间，请耐心等待...
echo.

python build_standalone_exe.py

if errorlevel 1 (
    echo.
    echo ❌ 构建过程出现错误
    pause
    exit /b 1
)

echo.
echo 🎉 构建完成！
echo.
echo 生成的文件位于 dist 目录中:
echo - DestinationLabels.exe (主程序)
echo - 使用说明.txt (使用指南)
echo.
echo 用户现在可以直接运行exe文件，无需安装任何依赖！
echo.

pause
