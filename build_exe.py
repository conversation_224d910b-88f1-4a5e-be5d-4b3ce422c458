#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建Destination Labels桌面应用程序为exe文件
使用PyInstaller打包
"""

import os
import sys
import subprocess
import shutil

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("PyInstaller安装失败")
        return False

def build_exe():
    """构建exe文件"""
    print("正在构建exe文件...")
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",  # 打包成单个exe文件
        "--windowed",  # 不显示控制台窗口
        "--name=DestinationLabels",  # exe文件名
        "--icon=icon.ico",  # 图标文件（如果存在）
        "--add-data=requirements_desktop.txt;.",  # 包含依赖文件
        "label_printer_app.py"  # 主程序文件
    ]
    
    # 如果没有图标文件，移除图标参数
    if not os.path.exists("icon.ico"):
        cmd.remove("--icon=icon.ico")
    
    try:
        subprocess.check_call(cmd)
        print("exe文件构建成功！")
        print("生成的文件位于: dist/DestinationLabels.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        return False

def clean_build_files():
    """清理构建过程中的临时文件"""
    print("正在清理临时文件...")
    
    dirs_to_remove = ["build", "__pycache__"]
    files_to_remove = ["DestinationLabels.spec"]
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已删除目录: {dir_name}")
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"已删除文件: {file_name}")

def main():
    """主函数"""
    print("========================================")
    print("Destination Labels - 构建exe工具")
    print("========================================")
    print()
    
    # 检查主程序文件是否存在
    if not os.path.exists("label_printer_app.py"):
        print("错误: 未找到主程序文件 label_printer_app.py")
        return False
    
    # 安装PyInstaller
    if not install_pyinstaller():
        return False
    
    print()
    
    # 构建exe
    if not build_exe():
        return False
    
    print()
    
    # 清理临时文件
    clean_build_files()
    
    print()
    print("构建完成！")
    print("您可以在 dist 目录中找到 DestinationLabels.exe 文件")
    print()
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("按任意键退出...")
        sys.exit(1)
    else:
        input("按任意键退出...")
