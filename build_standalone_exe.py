#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建独立的Destination Labels exe文件
确保用户无需安装任何依赖即可使用
"""

import os
import sys
import subprocess
import shutil
import tempfile
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        return False
    print(f"Python版本检查通过: {sys.version}")
    return True

def install_dependencies():
    """安装所需的依赖包"""
    print("正在安装依赖包...")
    
    dependencies = [
        "pyinstaller>=5.0",
        "pywebview>=4.0.0", 
        "pywin32>=306",
        "requests>=2.28.0"
    ]
    
    for dep in dependencies:
        print(f"安装 {dep}...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", dep, "--upgrade"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✅ {dep} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {dep} 安装失败")
            return False
    
    return True

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['label_printer_app.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'win32api',
        'win32print', 
        'win32gui',
        'win32con',
        'pywintypes',
        'webview',
        'webview.platforms.winforms',
        'webview.platforms.cef',
        'webview.platforms.mshtml',
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'threading',
        'json',
        'datetime',
        'time',
        're'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='DestinationLabels',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
    version_file='version_info.txt' if os.path.exists('version_info.txt') else None
)
'''
    
    with open('DestinationLabels.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content.strip())
    
    print("✅ PyInstaller规格文件创建成功")

def create_version_info():
    """创建版本信息文件"""
    version_info = '''
# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'LIN, LIHUI'),
        StringStruct(u'FileDescription', u'Destination Labels - 标签打印客户端'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'DestinationLabels'),
        StringStruct(u'LegalCopyright', u'©2025 LIN, LIHUI'),
        StringStruct(u'OriginalFilename', u'DestinationLabels.exe'),
        StringStruct(u'ProductName', u'Destination Labels'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info.strip())
    
    print("✅ 版本信息文件创建成功")

def create_icon():
    """创建简单的图标文件（如果不存在）"""
    if not os.path.exists('icon.ico'):
        print("⚠️  未找到icon.ico文件，将使用默认图标")
        # 这里可以创建一个简单的图标，或者跳过
        return False
    else:
        print("✅ 找到图标文件")
        return True

def build_exe():
    """构建exe文件"""
    print("开始构建exe文件...")
    print("这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 使用spec文件构建
        cmd = [
            "pyinstaller",
            "--clean",  # 清理之前的构建
            "DestinationLabels.spec"
        ]
        
        print("执行构建命令...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ exe文件构建成功！")
            
            # 检查生成的文件
            exe_path = os.path.join("dist", "DestinationLabels.exe")
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"生成的文件: {exe_path}")
                print(f"文件大小: {file_size:.1f} MB")
                return True
            else:
                print("❌ 未找到生成的exe文件")
                return False
        else:
            print("❌ 构建失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出错: {e}")
        return False

def test_exe():
    """测试生成的exe文件"""
    exe_path = os.path.join("dist", "DestinationLabels.exe")
    
    if not os.path.exists(exe_path):
        print("❌ exe文件不存在，无法测试")
        return False
    
    print("正在测试exe文件...")
    
    try:
        # 简单的启动测试（不等待完成）
        process = subprocess.Popen([exe_path], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待2秒看是否能正常启动
        import time
        time.sleep(2)
        
        if process.poll() is None:
            # 进程仍在运行，说明启动成功
            print("✅ exe文件测试通过")
            process.terminate()  # 终止测试进程
            return True
        else:
            # 进程已退出，可能有错误
            stdout, stderr = process.communicate()
            print("❌ exe文件测试失败")
            if stderr:
                print(f"错误信息: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ 测试exe文件时出错: {e}")
        return False

def clean_build_files():
    """清理构建过程中的临时文件"""
    print("正在清理临时文件...")
    
    files_to_remove = [
        "DestinationLabels.spec",
        "version_info.txt"
    ]
    
    dirs_to_remove = [
        "build",
        "__pycache__"
    ]
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"已删除文件: {file_name}")
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已删除目录: {dir_name}")

def create_readme():
    """创建exe使用说明"""
    readme_content = """
========================================
Destination Labels - 独立exe版本
========================================

版本: 1.0.0
作者: LIN, LIHUI
版权: ©2025 LIN, LIHUI

========================================
使用说明
========================================

1. 双击 DestinationLabels.exe 启动应用
2. 无需安装任何依赖或运行时
3. 确保标签打印系统在 localhost:5000 运行
4. 连接好标签打印机

========================================
系统要求
========================================

- Windows 10/11 (64位)
- 已安装标签打印机驱动
- 标签打印系统运行在本地5000端口

========================================
故障排除
========================================

如果exe无法启动:
1. 检查Windows Defender是否阻止
2. 确保有管理员权限
3. 检查打印机驱动是否正常
4. 联系技术支持

技术支持: LIN, LIHUI
更新日期: 2025年1月27日
"""
    
    with open(os.path.join("dist", "使用说明.txt"), 'w', encoding='utf-8') as f:
        f.write(readme_content.strip())
    
    print("✅ 使用说明文件创建成功")

def main():
    """主函数"""
    print("========================================")
    print("Destination Labels - 独立exe构建工具")
    print("========================================")
    print()
    
    # 检查Python版本
    if not check_python_version():
        input("按任意键退出...")
        return False
    
    print()
    
    # 检查主程序文件
    if not os.path.exists("label_printer_app.py"):
        print("❌ 未找到主程序文件 label_printer_app.py")
        input("按任意键退出...")
        return False
    
    print("✅ 主程序文件检查通过")
    print()
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        input("按任意键退出...")
        return False
    
    print()
    
    # 创建构建文件
    create_spec_file()
    create_version_info()
    create_icon()
    print()
    
    # 构建exe
    if not build_exe():
        print("❌ 构建失败")
        input("按任意键退出...")
        return False
    
    print()
    
    # 测试exe
    if not test_exe():
        print("⚠️  exe测试失败，但文件已生成")
    
    print()
    
    # 创建说明文件
    create_readme()
    
    # 清理临时文件
    clean_build_files()
    
    print()
    print("========================================")
    print("构建完成！")
    print("========================================")
    print()
    print("生成的文件:")
    print("📁 dist/DestinationLabels.exe - 主程序")
    print("📄 dist/使用说明.txt - 使用指南")
    print()
    print("🎉 用户现在可以直接运行exe文件，无需安装任何依赖！")
    print()
    
    input("按任意键退出...")
    return True

if __name__ == "__main__":
    main()
