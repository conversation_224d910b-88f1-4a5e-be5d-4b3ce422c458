#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终的exe构建脚本 - 确保生成完全独立的exe文件
"""

import os
import sys
import subprocess
import shutil

def main():
    print("========================================")
    print("Destination Labels - 最终exe构建")
    print("========================================")
    print()
    
    # 检查必要文件
    if not os.path.exists("label_printer_app.py"):
        print("❌ 错误: 未找到 label_printer_app.py")
        input("按任意键退出...")
        return
    
    print("✅ 主程序文件检查通过")
    
    # 安装PyInstaller
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller", "--upgrade"])
        print("✅ PyInstaller安装成功")
    except:
        print("❌ PyInstaller安装失败")
        input("按任意键退出...")
        return
    
    # 安装其他依赖
    print("正在安装依赖包...")
    dependencies = ["pywebview", "pywin32", "requests"]
    for dep in dependencies:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep, "--upgrade"])
            print(f"✅ {dep} 安装成功")
        except:
            print(f"⚠️  {dep} 安装失败，继续...")
    
    # 清理之前的构建
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    if os.path.exists("build"):
        shutil.rmtree("build")
    
    print()
    print("开始构建exe文件...")
    print("这可能需要几分钟时间...")
    
    # 检查图标文件
    icon_file = None
    if os.path.exists("ico.ico"):
        icon_file = "ico.ico"
        print("✅ 找到ico.ico文件")
    elif os.path.exists("icon.ico"):
        icon_file = "icon.ico"
        print("✅ 找到icon.ico文件")
    else:
        print("⚠️  未找到图标文件")

    # PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 单文件
        "--windowed",                   # 无控制台
        "--name=DestinationLabels",     # 文件名
        "--clean",                      # 清理缓存
        "--noconfirm",                  # 不确认覆盖

        # 添加隐藏导入
        "--hidden-import=win32api",
        "--hidden-import=win32print",
        "--hidden-import=win32gui",
        "--hidden-import=pywintypes",
        "--hidden-import=webview",
        "--hidden-import=webview.platforms.winforms",
        "--hidden-import=tkinter",
        "--hidden-import=tkinter.ttk",
        "--hidden-import=tkinter.messagebox",

        # 排除不需要的模块
        "--exclude-module=matplotlib",
        "--exclude-module=numpy",
        "--exclude-module=pandas",

        "label_printer_app.py"
    ]

    # 添加图标参数
    if icon_file:
        cmd.insert(-1, f"--icon={icon_file}")

    # 添加数据文件
    if os.path.exists("ico.ico"):
        cmd.insert(-1, "--add-data=ico.ico;.")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ exe构建成功！")
            
            # 检查文件
            exe_path = os.path.join("dist", "DestinationLabels.exe")
            if os.path.exists(exe_path):
                size_mb = os.path.getsize(exe_path) / (1024 * 1024)
                print(f"文件位置: {exe_path}")
                print(f"文件大小: {size_mb:.1f} MB")
                
                # 创建使用说明
                readme_path = os.path.join("dist", "使用说明.txt")
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write("""
Destination Labels - 独立exe版本

使用方法:
1. 双击 DestinationLabels.exe 启动
2. 确保标签打印系统在 localhost:5000 运行
3. 连接好标签打印机
4. 按照界面提示操作

系统要求:
- Windows 10/11
- 已安装打印机驱动

版权: ©2025 LIN, LIHUI
""".strip())
                
                print("✅ 使用说明创建成功")
                
            else:
                print("❌ 未找到生成的exe文件")
                
        else:
            print("❌ 构建失败")
            print("错误信息:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
    
    # 清理临时文件
    for item in ["build", "DestinationLabels.spec", "__pycache__"]:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
            else:
                os.remove(item)
                print(f"已删除临时文件: {item}")
    
    print()
    print("========================================")
    print("构建完成！")
    print("========================================")
    print()
    
    if os.path.exists(os.path.join("dist", "DestinationLabels.exe")):
        print("🎉 成功生成独立exe文件！")
        print("📁 文件位置: dist/DestinationLabels.exe")
        print("📄 使用说明: dist/使用说明.txt")
        print()
        print("用户现在可以直接运行exe文件，无需安装任何依赖！")
    else:
        print("❌ 构建失败，请检查错误信息")
    
    print()
    input("按任意键退出...")

if __name__ == "__main__":
    main()
