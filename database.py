import sqlite3
import os

# Get the absolute path for the database file inside the 'label-printer' directory
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DB_PATH = os.path.join(BASE_DIR, 'printer.db')

def create_connection():
    conn = sqlite3.connect(DB_PATH, check_same_thread=False)
    conn.row_factory = sqlite3.Row
    return conn

def create_tables():
    conn = create_connection()
    c = conn.cursor()

    # User Groups Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS user_groups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL
        )
    ''')

    # Permissions Table - linking permissions to groups
    c.execute('''
        CREATE TABLE IF NOT EXISTS permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            group_id INTEGER NOT NULL,
            can_manage_backend BOOLEAN NOT NULL DEFAULT 0,
            can_view_problem_solve BOOLEAN NOT NULL DEFAULT 0,
            can_manage_users BOOLEAN NOT NULL DEFAULT 0,
            can_manage_departments BOOLEAN NOT NULL DEFAULT 0,
            can_manage_labels BOOLEAN NOT NULL DEFAULT 0,
            can_manage_announcements BOOLEAN NOT NULL DEFAULT 0,
            can_view_feedback BOOLEAN NOT NULL DEFAULT 0,
            FOREIGN KEY (group_id) REFERENCES user_groups (id)
        )
    ''')

    # 添加缺失的权限字段（如果不存在）
    try:
        c.execute('ALTER TABLE permissions ADD COLUMN can_submit_knowledge BOOLEAN NOT NULL DEFAULT 1')
        print("Added can_submit_knowledge column to permissions table")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e).lower():
            print("can_submit_knowledge column already exists")
        else:
            print(f"Error adding can_submit_knowledge column: {e}")

    try:
        c.execute('ALTER TABLE permissions ADD COLUMN can_review_knowledge_submissions BOOLEAN NOT NULL DEFAULT 0')
        print("Added can_review_knowledge_submissions column to permissions table")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e).lower():
            print("can_review_knowledge_submissions column already exists")
        else:
            print(f"Error adding can_review_knowledge_submissions column: {e}")

    try:
        c.execute('ALTER TABLE permissions ADD COLUMN can_review_knowledge BOOLEAN NOT NULL DEFAULT 0')
        print("Added can_review_knowledge column to permissions table")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e).lower():
            print("can_review_knowledge column already exists")
        else:
            print(f"Error adding can_review_knowledge column: {e}")

    # 添加分类中心权限
    try:
        c.execute('ALTER TABLE permissions ADD COLUMN can_manage_category_codes BOOLEAN NOT NULL DEFAULT 0')
        print("Added can_manage_category_codes column to permissions table")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e).lower():
            print("can_manage_category_codes column already exists")
        else:
            print(f"Error adding can_manage_category_codes column: {e}")

    try:
        c.execute('ALTER TABLE permissions ADD COLUMN can_manage_category_codes_add BOOLEAN NOT NULL DEFAULT 0')
        print("Added can_manage_category_codes_add column to permissions table")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e).lower():
            print("can_manage_category_codes_add column already exists")
        else:
            print(f"Error adding can_manage_category_codes_add column: {e}")

    try:
        c.execute('ALTER TABLE permissions ADD COLUMN can_manage_category_codes_edit BOOLEAN NOT NULL DEFAULT 0')
        print("Added can_manage_category_codes_edit column to permissions table")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e).lower():
            print("can_manage_category_codes_edit column already exists")
        else:
            print(f"Error adding can_manage_category_codes_edit column: {e}")

    try:
        c.execute('ALTER TABLE permissions ADD COLUMN can_manage_category_codes_delete BOOLEAN NOT NULL DEFAULT 0')
        print("Added can_manage_category_codes_delete column to permissions table")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e).lower():
            print("can_manage_category_codes_delete column already exists")
        else:
            print(f"Error adding can_manage_category_codes_delete column: {e}")

    # Departments Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS departments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            code TEXT,
            head TEXT
        )
    ''')

    # Users Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            card_id TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            code TEXT,
            department_id INTEGER,
            group_id INTEGER,
            password TEXT,
            FOREIGN KEY (department_id) REFERENCES departments (id),
            FOREIGN KEY (group_id) REFERENCES user_groups (id)
        )
    ''')

    # Labels Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS labels (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT NOT NULL, -- 'normal', 'advanced', 'special', 'global'
            content TEXT NOT NULL, -- For normal, it's text. For others, JSON with properties
            abbreviations TEXT, -- JSON array of strings
            print_count INTEGER NOT NULL DEFAULT 1,
            prompt_for_quantity BOOLEAN NOT NULL DEFAULT 0,
            form_enabled BOOLEAN NOT NULL DEFAULT 0,
            form_title TEXT,
            problem_solve BOOLEAN NOT NULL DEFAULT 0,
            department_id INTEGER,
            copy_info TEXT, -- 复制信息
            print_quantity INTEGER NOT NULL DEFAULT 1, -- 打印数量
            label_code_shortcut TEXT, -- 标签代码简写
            v_align TEXT DEFAULT 'center', -- 垂直对齐方式
            print_prompt BOOLEAN NOT NULL DEFAULT 0, -- 打印数量提醒
            department_names TEXT, -- 所属部门名称
            usage_question_enabled BOOLEAN NOT NULL DEFAULT 0, -- 使用问题功能启用
            usage_question_title TEXT, -- 使用问题标题
            FOREIGN KEY (department_id) REFERENCES departments (id)
        )
    ''')

    # Announcements Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS announcements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            content TEXT NOT NULL,
            author_id INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (author_id) REFERENCES users (id)
        )
    ''')

    # Feedback Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS feedback (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            user_id INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            reply TEXT,
            replied_by_id INTEGER,
            is_read_by_user BOOLEAN NOT NULL DEFAULT 0,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (replied_by_id) REFERENCES users (id)
        )
    ''')

    # Problem Solve Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS problem_solves (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            label_id INTEGER NOT NULL,
            submitted_by_id INTEGER NOT NULL,
            content TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            reply TEXT,
            replied_by_id INTEGER,
            replied_at DATETIME,
            is_read_by_user BOOLEAN NOT NULL DEFAULT 0,
            FOREIGN KEY (label_id) REFERENCES labels (id),
            FOREIGN KEY (submitted_by_id) REFERENCES users (id),
            FOREIGN KEY (replied_by_id) REFERENCES users (id)
        )
    ''')
    
    # Barcode Prefixes Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS barcode_prefixes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            prefix TEXT UNIQUE NOT NULL,
            print_count INTEGER NOT NULL DEFAULT 1
        )
    ''')

    # Chatbot KB Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS chatbot_kb (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            keyword TEXT UNIQUE NOT NULL,
            answer TEXT NOT NULL
        )
    ''')

    # Audit Log Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS audit_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            action TEXT NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    # Print Logs Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS print_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            label_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL DEFAULT 1,
            printed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (label_id) REFERENCES labels (id)
        )
    ''')

    # Notifications Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS notifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            type TEXT NOT NULL, -- 'problem_reply', 'system', etc.
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            read_status BOOLEAN NOT NULL DEFAULT 0,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    # Category Codes Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS category_codes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            copy_content TEXT NOT NULL,
            department_id INTEGER,
            department_names TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (department_id) REFERENCES departments (id)
        )
    ''')

    # System Settings Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS system_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key TEXT UNIQUE NOT NULL,
            setting_value TEXT NOT NULL,
            description TEXT,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_by INTEGER,
            FOREIGN KEY (updated_by) REFERENCES users (id)
        )
    ''')

    # Public Chat Messages Table (if not exists)
    c.execute('''
        CREATE TABLE IF NOT EXISTS public_chat_messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            user_name TEXT,
            content TEXT NOT NULL,
            mentioned_user_id INTEGER,
            is_private_mention BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (mentioned_user_id) REFERENCES users (id)
        )
    ''')

    # User Chat Read Status Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS user_chat_read_status (
            user_id INTEGER PRIMARY KEY,
            last_read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    # Knowledge Share Submissions Table
    c.execute('''
        CREATE TABLE IF NOT EXISTS knowledge_submissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            question TEXT NOT NULL,
            answer TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            reviewed_by_id INTEGER,
            reviewed_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (reviewed_by_id) REFERENCES users (id)
        )
    ''')

    # Insert default system settings
    c.execute('''
        INSERT OR IGNORE INTO system_settings (setting_key, setting_value, description)
        VALUES
        ('chat_retention_days', '7', '聊天记录保存天数'),
        ('chat_retention_hours', '0', '聊天记录保存小时数'),
        ('chat_retention_minutes', '0', '聊天记录保存分钟数')
    ''')

    # 添加新字段的迁移（如果不存在）
    try:
        c.execute("ALTER TABLE labels ADD COLUMN usage_question_enabled BOOLEAN NOT NULL DEFAULT 0")
        print("添加 usage_question_enabled 字段")
    except sqlite3.OperationalError:
        pass  # 字段已存在

    try:
        c.execute("ALTER TABLE labels ADD COLUMN usage_question_title TEXT")
        print("添加 usage_question_title 字段")
    except sqlite3.OperationalError:
        pass  # 字段已存在

    try:
        c.execute("ALTER TABLE labels ADD COLUMN is_hidden BOOLEAN NOT NULL DEFAULT 0")
        print("添加 is_hidden 字段")
    except sqlite3.OperationalError:
        pass  # 字段已存在

    conn.commit()
    conn.close()

if __name__ == '__main__':
    create_tables()
    print("Database tables created successfully.")
