@echo off
echo ========================================
echo Destination Labels Desktop App
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python 3.8+
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在安装依赖包...
pip install -r requirements_desktop.txt

if errorlevel 1 (
    echo 警告: 部分依赖包安装失败，尝试继续运行...
    echo.
)

echo.
echo 正在启动Destination Labels...
echo.

python label_printer_app.py

if errorlevel 1 (
    echo.
    echo 应用程序运行出错
    pause
)
