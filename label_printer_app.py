#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Destination Labels - 标签打印客户端
Windows桌面应用程序，用于连接本地打印机并提供Web界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import sys
import os
import json
import re
from datetime import datetime

# 导入Windows API相关模块
try:
    import win32print
    import win32api
    import pywintypes
except ImportError as e:
    print(f"警告: 无法导入Windows API模块: {e}")
    print("请安装pywin32: pip install pywin32")

# 导入webview模块
try:
    import webview
except ImportError as e:
    print(f"警告: 无法导入webview模块: {e}")
    print("请安装pywebview: pip install pywebview")

# 设置应用程序路径（支持打包后的exe）
if getattr(sys, 'frozen', False):
    # 如果是打包后的exe
    APPLICATION_PATH = os.path.dirname(sys.executable)
else:
    # 如果是Python脚本
    APPLICATION_PATH = os.path.dirname(os.path.abspath(__file__))

class PrinterManager:
    """打印机管理类"""

    def __init__(self):
        self.selected_printer = None

    def log_message(self, message):
        """记录日志消息（仅输出到控制台）"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            # 仅输出到控制台，不写入文件
            print(f"[{timestamp}] {message}")
        except Exception as e:
            print(f"日志记录失败: {e}")
        
    def get_printers(self):
        """获取本地打印机列表"""
        printers = []

        try:
            self.log_message("开始检测本地打印机...")

            # 检查win32print模块是否可用
            if 'win32print' not in globals():
                self.log_message("错误: win32print模块不可用")
                return printers

            # 获取所有打印机
            printer_list = win32print.EnumPrinters(
                win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS
            )

            self.log_message(f"发现 {len(printer_list)} 台打印机")

            for printer in printer_list:
                printer_name = printer[2]  # 打印机名称
                self.log_message(f"检查打印机: {printer_name}")

                # 检查打印机状态
                try:
                    handle = win32print.OpenPrinter(printer_name)
                    printer_info = win32print.GetPrinter(handle, 2)
                    status = printer_info['Status']

                    # 判断打印机是否在线
                    is_online = status == 0  # 0表示正常状态
                    status_text = "Ready" if is_online else "Offline"

                    win32print.ClosePrinter(handle)

                    printer_data = {
                        'name': printer_name,
                        'status': status_text,
                        'is_online': is_online,
                        'is_zebra': printer_name.lower().startswith('zebra')
                    }

                    printers.append(printer_data)
                    self.log_message(f"  状态: {status_text}, Zebra: {printer_data['is_zebra']}")

                except Exception as e:
                    # 如果无法获取状态，标记为离线
                    printer_data = {
                        'name': printer_name,
                        'status': "Offline",
                        'is_online': False,
                        'is_zebra': printer_name.lower().startswith('zebra')
                    }

                    printers.append(printer_data)
                    self.log_message(f"  无法获取状态，标记为离线: {e}")

        except Exception as e:
            self.log_message(f"获取打印机列表失败: {e}")

        self.log_message(f"打印机检测完成，共找到 {len(printers)} 台打印机")
        return printers
    
    def set_selected_printer(self, printer_name):
        """设置选中的打印机"""
        self.selected_printer = printer_name
        
    def print_to_printer(self, data):
        """发送数据到打印机进行静默打印"""
        if not self.selected_printer:
            self.log_message("错误: 未选择打印机")
            return False

        try:
            self.log_message(f"开始静默打印到打印机: {self.selected_printer}")

            # 检查win32print模块是否可用
            if 'win32print' not in globals():
                self.log_message("错误: win32print模块不可用")
                return False

            # 格式化打印数据
            print_data = self.format_print_data(data)
            self.log_message(f"打印数据长度: {len(print_data)} 字符")

            # 打开打印机
            printer_handle = win32print.OpenPrinter(self.selected_printer)
            self.log_message(f"打印机句柄已打开: {self.selected_printer}")

            try:
                # 创建打印作业
                job_info = ("Destination Labels Silent Print", None, "RAW")
                job_id = win32print.StartDocPrinter(printer_handle, 1, job_info)
                self.log_message(f"打印作业已创建，作业ID: {job_id}")

                try:
                    # 开始页面
                    win32print.StartPagePrinter(printer_handle)
                    self.log_message("开始打印页面")

                    # 发送打印数据
                    if isinstance(print_data, str):
                        # 如果是字符串，编码为字节
                        data_bytes = print_data.encode('utf-8')
                    else:
                        # 如果已经是字节，直接使用
                        data_bytes = print_data

                    bytes_written = win32print.WritePrinter(printer_handle, data_bytes)
                    self.log_message(f"已写入 {bytes_written} 字节到打印机")

                    # 结束页面
                    win32print.EndPagePrinter(printer_handle)
                    self.log_message("打印页面结束")

                    self.log_message("✅ 静默打印数据发送成功")
                    return True

                finally:
                    # 结束打印作业
                    win32print.EndDocPrinter(printer_handle)
                    self.log_message("打印作业已结束")

            finally:
                # 关闭打印机
                win32print.ClosePrinter(printer_handle)
                self.log_message("打印机句柄已关闭")

        except Exception as e:
            error_msg = f"静默打印失败: {e}"
            self.log_message(error_msg)
            return False

    def format_print_data(self, html_data):
        """将HTML数据格式化为打印机可识别的格式"""
        try:
            self.log_message("开始格式化打印数据...")

            # 提取文本内容
            text_content = self.extract_text_from_html(html_data)
            self.log_message(f"提取的文本内容: {text_content[:100]}...")

            # 根据打印机类型生成相应格式
            if self.selected_printer and 'zebra' in self.selected_printer.lower():
                self.log_message("检测到Zebra打印机，生成ZPL格式")
                return self.generate_zpl_format(text_content)
            else:
                self.log_message("使用通用文本格式")
                return self.generate_text_format(text_content)

        except Exception as e:
            self.log_message(f"格式化打印数据时出错: {e}")
            return "打印数据格式化失败"

    def extract_text_from_html(self, html_data):
        """从HTML中提取纯文本内容"""
        try:
            # 移除HTML标签
            text_content = re.sub(r'<[^>]+>', '', html_data)

            # 处理HTML实体
            text_content = text_content.replace('&nbsp;', ' ')
            text_content = text_content.replace('&lt;', '<')
            text_content = text_content.replace('&gt;', '>')
            text_content = text_content.replace('&amp;', '&')

            # 清理多余的空白字符
            text_content = re.sub(r'\s+', ' ', text_content)
            text_content = text_content.strip()

            # 如果内容为空，提供默认内容
            if not text_content:
                text_content = "标签内容"

            return text_content

        except Exception as e:
            self.log_message(f"提取文本内容时出错: {e}")
            return "标签内容"

    def generate_text_format(self, text_content):
        """生成通用文本格式"""
        try:
            # 为通用打印机生成简单的文本格式
            formatted_text = f"""
{text_content}

---
打印时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
打印机: {self.selected_printer}
"""
            return formatted_text.strip()

        except Exception as e:
            self.log_message(f"生成文本格式时出错: {e}")
            return text_content

    def generate_zpl_format(self, text_content):
        """生成ZPL格式的打印指令（适用于Zebra打印机）"""
        try:
            # 限制文本长度，避免超出标签范围
            if len(text_content) > 100:
                text_content = text_content[:97] + "..."

            # 替换可能导致ZPL问题的字符
            text_content = text_content.replace('^', '')
            text_content = text_content.replace('~', '')

            # 生成ZPL指令
            zpl_template = f"""^XA
^MMT
^PW812
^LL0203
^LS0
^FT50,100^A0N,40,40^FD{text_content}^FS
^FT50,150^A0N,20,20^FD{datetime.now().strftime('%Y-%m-%d %H:%M')}^FS
^PQ1,0,1,Y
^XZ"""

            self.log_message(f"生成的ZPL指令长度: {len(zpl_template)} 字符")
            return zpl_template

        except Exception as e:
            self.log_message(f"生成ZPL格式时出错: {e}")
            # 返回最简单的ZPL格式
            return f"^XA^FO50,50^A0N,30,30^FD{text_content[:50]}^FS^XZ"

class PrinterSelectionWindow:
    """打印机选择窗口"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.printer_manager = PrinterManager()
        self.selected_printer = None
        self.setup_window()
        self.setup_widgets()
        self.refresh_printers()
        
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("Destination Labels")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # 居中显示窗口
        self.center_window()
        
        # 设置窗口图标
        try:
            # 优先使用ico.ico文件
            if os.path.exists("ico.ico"):
                self.root.iconbitmap("ico.ico")
            elif os.path.exists("icon.ico"):
                self.root.iconbitmap("icon.ico")
        except Exception as e:
            print(f"设置图标失败: {e}")
            pass
            
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def setup_widgets(self):
        """设置界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题 - 居中
        title_label = ttk.Label(main_frame, text="Select Printer",
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 30), anchor=tk.CENTER)

        # 打印机选择框架 - 居中
        printer_frame = ttk.Frame(main_frame)
        printer_frame.pack(pady=(0, 30), anchor=tk.CENTER)

        # 直接显示打印机下拉框，不显示标签文字
        self.printer_var = tk.StringVar()
        self.printer_combo = ttk.Combobox(printer_frame, textvariable=self.printer_var,
                                         state="readonly", width=60, font=("Arial", 10))
        self.printer_combo.pack(pady=(0, 10), anchor=tk.CENTER)

        # 按钮框架 - 居中
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(0, 30), anchor=tk.CENTER)

        # 刷新按钮
        self.refresh_btn = ttk.Button(button_frame, text="Refresh",
                                     command=self.refresh_printers, width=12)
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 15))

        # 确认按钮
        self.confirm_btn = ttk.Button(button_frame, text="Confirm",
                                     command=self.confirm_selection, width=12)
        self.confirm_btn.pack(side=tk.LEFT, padx=(0, 15))

        # 关闭按钮
        self.close_btn = ttk.Button(button_frame, text="Close",
                                   command=self.close_application, width=12)
        self.close_btn.pack(side=tk.LEFT)

        # 版权信息 - 居中
        copyright_label = ttk.Label(main_frame, text="©2025 LIN, LIHUI",
                                   font=("Arial", 10), foreground="gray")
        copyright_label.pack(side=tk.BOTTOM, pady=(20, 0), anchor=tk.CENTER)
        
    def refresh_printers(self):
        """刷新打印机列表"""
        try:
            printers = self.printer_manager.get_printers()
            
            # 清空当前列表
            self.printer_combo['values'] = []
            
            # 构建显示列表
            printer_options = []
            zebra_ready_printers = []
            
            for printer in printers:
                display_name = printer['name']
                if not printer['is_online']:
                    display_name += " (Offline)"
                    
                printer_options.append(display_name)
                
                # 优先选择Zebra且Ready的打印机
                if printer['is_zebra'] and printer['is_online']:
                    zebra_ready_printers.append(display_name)
            
            # 设置下拉框选项
            self.printer_combo['values'] = printer_options
            
            # 设置默认选择
            if zebra_ready_printers:
                self.printer_var.set(zebra_ready_printers[0])
            elif printer_options:
                self.printer_var.set(printer_options[0])
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh printer list: {e}")

    def confirm_selection(self):
        """确认选择打印机"""
        selected = self.printer_var.get()
        if not selected:
            messagebox.showwarning("Warning", "Please select a printer")
            return
            
        # 提取打印机名称（去掉状态信息）
        printer_name = selected.split(" (")[0]
        self.selected_printer = printer_name
        
        # 设置打印机管理器的选中打印机
        self.printer_manager.set_selected_printer(printer_name)
        
        # 关闭当前窗口并打开Web界面
        self.root.destroy()
        self.open_web_interface()
        
    def close_application(self):
        """关闭应用程序"""
        self.root.quit()
        sys.exit()
        
    def open_web_interface(self):
        """打开Web界面"""
        web_window = WebInterfaceWindow(self.selected_printer, self.printer_manager)
        web_window.show()
        
    def run(self):
        """运行应用程序"""
        self.root.mainloop()

class WebInterfaceWindow:
    """Web界面窗口"""
    
    def __init__(self, printer_name, printer_manager):
        self.printer_name = printer_name
        self.printer_manager = printer_manager
        self.web_url = "http://localhost:5000"
        
    def show_printer_notification(self):
        """显示打印机连接通知"""
        # 这个函数将在Web页面加载后调用
        js_code = f"""
        (function() {{
            var notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                z-index: 10000;
                font-family: Arial, sans-serif;
                font-size: 14px;
            `;
            notification.textContent = 'Connected Printer: {self.printer_name}';
            document.body.appendChild(notification);

            setTimeout(function() {{
                notification.remove();
            }}, 3000);
        }})();
        """
        return js_code
        
    def intercept_print(self):
        """拦截window.print调用和标签点击的JavaScript代码"""
        js_code = """
        (function() {
            console.log('开始安装静默打印拦截器...');

            // 保存原始的print函数
            var originalPrint = window.print;

            // 完全重写window.print函数，实现真正的静默打印
            window.print = function() {
                console.log('拦截到window.print()调用，执行静默打印');

                // 阻止默认的打印对话框
                event.preventDefault && event.preventDefault();
                event.stopPropagation && event.stopPropagation();

                // 直接调用静默打印函数
                performSilentPrint();

                // 不调用原始的print函数，完全阻止浏览器打印对话框
                return false;
            };

            // 静默打印执行函数
            function performSilentPrint() {
                try {
                    console.log('执行静默打印...');

                    // 获取打印内容
                    var printContent = extractPrintContent();

                    // 创建打印数据
                    var printData = {
                        content: printContent,
                        timestamp: new Date().toISOString(),
                        url: window.location.href,
                        title: document.title,
                        action: 'silent_print'
                    };

                    // 发送到Python后端进行静默打印
                    if (window.pywebview && window.pywebview.api) {
                        console.log('发送静默打印请求到Python后端');

                        // 调用Python API
                        window.pywebview.api.handle_print_request(JSON.stringify(printData))
                            .then(function(result) {
                                console.log('静默打印请求已发送，结果:', result);
                                showPrintNotification('打印已发送到打印机', 'success');
                            })
                            .catch(function(error) {
                                console.error('静默打印失败:', error);
                                showPrintNotification('打印失败', 'error');
                            });
                    } else {
                        console.error('无法连接到Python后端');
                        showPrintNotification('无法连接到打印服务', 'error');
                    }

                } catch (error) {
                    console.error('静默打印过程出错:', error);
                    showPrintNotification('打印过程出错', 'error');
                }
            }

            // 提取打印内容
            function extractPrintContent() {
                var printContent = '';

                // 优先查找标签内容容器
                var selectors = [
                    '.label-print-content',
                    '.label-content',
                    '.print-content',
                    '[class*="label"]',
                    '[id*="label"]'
                ];

                for (var i = 0; i < selectors.length; i++) {
                    var elements = document.querySelectorAll(selectors[i]);
                    if (elements.length > 0) {
                        printContent = elements[0].outerHTML;
                        console.log('找到打印内容，使用选择器:', selectors[i]);
                        break;
                    }
                }

                // 如果没找到特定内容，使用整个body
                if (!printContent) {
                    printContent = document.body.innerHTML;
                    console.log('使用整个页面内容作为打印内容');
                }

                return printContent;
            }

            // 显示打印通知
            function showPrintNotification(message, type) {
                var notification = document.createElement('div');
                var bgColor = type === 'success' ? '#28a745' : '#dc3545';

                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${bgColor};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 5px;
                    z-index: 10000;
                    font-size: 14px;
                    font-family: Arial, sans-serif;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                `;
                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(function() {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 3000);
            }

            // 监听标签点击事件，直接触发静默打印
            document.addEventListener('click', function(event) {
                var target = event.target;
                var labelElement = null;

                // 检查是否点击了标签相关的元素
                if (target.classList.contains('label-item') ||
                    target.classList.contains('label-card') ||
                    target.classList.contains('label-button')) {
                    labelElement = target;
                } else {
                    // 检查父元素
                    labelElement = target.closest('.label-item, .label-card, .label-button, [class*="label"]');
                }

                if (labelElement) {
                    console.log('检测到标签点击事件，准备静默打印');

                    // 阻止默认行为
                    event.preventDefault();
                    event.stopPropagation();

                    // 延迟一点执行，确保页面状态稳定
                    setTimeout(function() {
                        performSilentPrint();
                    }, 100);

                    return false;
                }
            }, true); // 使用捕获阶段，确保能拦截到事件

            // 拦截所有可能触发打印的事件
            ['beforeprint', 'print'].forEach(function(eventType) {
                window.addEventListener(eventType, function(event) {
                    console.log('拦截到打印事件:', eventType);
                    event.preventDefault();
                    event.stopImmediatePropagation();

                    if (eventType === 'beforeprint') {
                        performSilentPrint();
                    }

                    return false;
                }, true);
            });

            console.log('✅ 静默打印拦截器安装完成');

        })();
        """
        return js_code
        
    def show(self):
        """显示Web界面窗口"""
        # 创建API类用于处理JavaScript调用
        class WebAPI:
            def __init__(self, printer_manager):
                self.printer_manager = printer_manager

            def handle_print_request(self, content):
                """处理静默打印请求"""
                try:
                    self.printer_manager.log_message(f"收到静默打印请求，内容长度: {len(content)}")

                    # 解析打印数据
                    try:
                        print_data = json.loads(content)
                        actual_content = print_data.get('content', content)
                        timestamp = print_data.get('timestamp', '')
                        action = print_data.get('action', 'print')

                        self.printer_manager.log_message(f"打印动作: {action}, 时间: {timestamp}")
                    except json.JSONDecodeError:
                        # 如果不是JSON，直接使用原始内容
                        actual_content = content
                        action = 'print'

                    # 检查是否有选中的打印机
                    if not self.printer_manager.selected_printer:
                        self.printer_manager.log_message("错误: 未选择打印机")
                        return {'success': False, 'message': '未选择打印机'}

                    self.printer_manager.log_message(f"开始静默打印到: {self.printer_manager.selected_printer}")

                    # 执行静默打印
                    success = self.printer_manager.print_to_printer(actual_content)

                    if success:
                        self.printer_manager.log_message("✅ 静默打印成功")
                        self.log_print_success(actual_content)
                        return {'success': True, 'message': '打印成功'}
                    else:
                        self.printer_manager.log_message("❌ 静默打印失败")
                        self.log_print_failure(actual_content)
                        return {'success': False, 'message': '打印失败'}

                except Exception as e:
                    error_msg = f"处理静默打印请求时出错: {e}"
                    self.printer_manager.log_message(error_msg)
                    return {'success': False, 'message': error_msg}

            def log_print_success(self, content):
                """记录打印成功日志"""
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"[{timestamp}] 打印成功 - 内容长度: {len(content)}")

            def log_print_failure(self, content):
                """记录打印失败日志"""
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"[{timestamp}] 打印失败 - 内容长度: {len(content)}")

            def get_printer_status(self):
                """获取当前打印机状态"""
                if self.printer_manager.selected_printer:
                    return {
                        'printer_name': self.printer_manager.selected_printer,
                        'status': 'connected'
                    }
                else:
                    return {
                        'printer_name': None,
                        'status': 'not_connected'
                    }
        
        # 创建webview窗口
        api = WebAPI(self.printer_manager)
        
        def on_window_loaded():
            """窗口加载完成后的回调"""
            time.sleep(1)  # 等待页面完全加载

            # 注入打印机通知
            webview.evaluate_js(self.show_printer_notification())

        # 在新线程中延迟执行回调
        def delayed_callback():
            time.sleep(2)
            on_window_loaded()

        threading.Thread(target=delayed_callback, daemon=True).start()
        
        # 设置webview窗口图标路径
        icon_path = None
        if os.path.exists("ico.ico"):
            icon_path = "ico.ico"
        elif os.path.exists("icon.ico"):
            icon_path = "icon.ico"

        # 创建并显示webview窗口
        if icon_path:
            webview.create_window(
                title="Destination Labels ©2025 LIN, LIHUI",
                url=self.web_url,
                width=1200,
                height=800,
                resizable=True,
                js_api=api,
                icon=icon_path
            )
        else:
            webview.create_window(
                title="Destination Labels ©2025 LIN, LIHUI",
                url=self.web_url,
                width=1200,
                height=800,
                resizable=True,
                js_api=api
            )
        
        webview.start(debug=False)

def main():
    """主函数"""
    try:
        # 创建并运行打印机选择窗口
        app = PrinterSelectionWindow()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"应用程序启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
