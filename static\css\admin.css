/* Admin Page Styles */
.admin-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.admin-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.admin-header h1 {
    margin: 0;
    color: #333;
    font-size: 2.5em;
    font-weight: 700;
    text-align: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.admin-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
    justify-content: center;
}

.admin-nav button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.admin-nav button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.admin-nav button.active {
    background: linear-gradient(135deg, #764ba2, #667eea);
    box-shadow: 0 4px 15px rgba(118, 75, 162, 0.4);
}

.admin-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: none;
}

.admin-section.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

.admin-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.admin-section-header h2 {
    margin: 0;
}

.admin-section-header .admin-search-input {
    width: 220px;
    padding: 6px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 15px;
}

.add-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.add-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.admin-table th {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 10px 15px;
    text-align: left;
    font-weight: 600;
    font-size: 0.95em;
}

.admin-table td {
    padding: 0 15px; /* Remove vertical padding, control height with tr */
    border-bottom: none; /* Remove bottom border */
    vertical-align: middle;
}

.admin-table tr {
    height: 90px; /* Adjusted row height for a bit more space */
    border-bottom: 1px solid #f0f0f0; /* Use border on tr for clean separation */
}

.admin-table tr:last-child {
    border-bottom: none;
}

.admin-table tr:nth-child(even) {
    background-color: #f9f9f9; /* Zebra striping for readability */
}

.admin-table tr:hover {
    background-color: #f1f1f1; /* Keep hover effect */
}

.admin-table button {
    background: #007bff;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    margin-right: 4px;
    transition: background-color 0.2s ease;
}

.admin-table button:hover {
    background: #0056b3;
}

.admin-table button:last-child {
    margin-right: 0;
}

/* 确保操作列的按钮并排显示 */
.admin-table td:last-child {
    white-space: nowrap;
    text-align: center;
}

.admin-table button.btn-delete,
.admin-table button[onclick*="delete"] {
    background: #dc3545;
}

.admin-table button.btn-delete:hover,
.admin-table button[onclick*="delete"]:hover {
    background: #c82333;
}

.admin-table button.btn-edit,
.admin-table button[onclick*="edit"] {
    background: #28a745;
}

.admin-table button.btn-edit:hover,
.admin-table button[onclick*="edit"]:hover {
    background: #218838;
}

/* 标签管理表格特定样式 */
#labels-table th {
    text-align: center;
}

#labels-table th:nth-child(5), /* 打印提示列 */
#labels-table td:nth-child(5) {
    width: 70px;
    text-align: center;
    padding: 0 8px;
}

#labels-table th:nth-child(6), /* 打印数量列 */
#labels-table td:nth-child(6) {
    width: 70px;
    text-align: center;
    padding: 0 8px;
}

#labels-table th:nth-child(7), /* 复制内容列 */
#labels-table td:nth-child(7) {
    width: 120px;
    text-align: center;
}

#labels-table th:nth-child(8), /* 部门列 */
#labels-table td:nth-child(8) {
    text-align: center;
}

#labels-table th:nth-child(9), /* 操作列 */
#labels-table td:nth-child(9) {
    width: 100px;
    text-align: center;
}

.empty-state {
    font-weight: bold;
    text-align: center;
    color: #888;
}

.admin-table .content-cell-preview {
    padding: 0;
    text-align: center;
}

.table-preview-wrapper {
    width: 122px; /* Aspect ratio of 1.5 (270/180) to match label-preview */
    height: 82px;
    position: relative;
    overflow: hidden;
    display: inline-block;
    vertical-align: middle;
    border: 1px solid #ddd;
    background-color: #f0f0f0; /* Add a background to better see the frame */
}

.table-preview-content {
    /* --- Base dimensions from .label-preview --- */
    width: 270px;
    height: 180px;
    
    /* --- Styling from .label-preview (the container) --- */
    border: 2px dashed #aaa;
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Clip content that overflows the label dimensions */

    /* --- Styling from .label-preview-content (the content flow) --- */
    padding: 3px;
    word-break: break-all;
    white-space: pre-wrap;
    word-wrap: break-word;

    /* --- Scaling and positioning to fit wrapper --- */
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.444);
    transform-origin: center center;
    
    /* --- Ensure proper text rendering --- */
    font-family: 'Microsoft YaHei', sans-serif;
    font-size: 12px;
    line-height: 1.4;
}

.label-preview-content > div {
    width: 100%;
    box-sizing: border-box;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
    padding: 20px;
    box-sizing: border-box;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 98%;
    margin: 5px;
    max-width: none;
    max-height: 95vh;
    height: auto;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.3s ease;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 15px 18px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.modal-title {
    margin: 0;
    font-size: 1.3em;
    font-weight: 600;
    color: #333;
}

.close {
    background: none;
    border: none;
    font-size: 1.5em;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close:hover {
    background: #f8f9fa;
    color: #333;
}

.modal-body {
    padding: 10px 15px;
    overflow-y: auto;
    flex-grow: 1;
}

.modal-actions {
    padding: 12px 18px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    flex-shrink: 0;
}

.btn-cancel, .btn-save {
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn-save {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-save:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Form Styles */
.form-group {
    margin-bottom: 10px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1em;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Permissions Container for two-column layout */
.permissions-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 0;
}

.permissions-container .form-group {
    margin-bottom: 0;
}

.permissions-container .form-group > label {
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
    color: #333;
    font-size: 0.95em;
    padding: 8px 12px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.permissions-container .form-group small {
    font-weight: 400;
    color: #666;
}

/* Permissions Grid (for items within a column) */
.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 8px;
    margin-top: 8px;
    padding: 0 12px;
}

.permissions-grid label,
.permission-item,
.sub-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding-left: 18px !important;
    margin-left: 0 !important;
    text-align: left;
}

.permissions-grid input[type="checkbox"] {
    margin-right: 8px;
}

.permissions-grid label:hover {
    background: #e9ecef;
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.permissions-grid label::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: #667eea;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.permissions-grid label:hover::before {
    opacity: 1;
}

.permissions-grid input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin: 0;
    flex-shrink: 0;
    cursor: pointer;
    accent-color: #667eea;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    position: relative;
    top: 0;
    transition: all 0.2s ease;
}

.permissions-grid input[type="checkbox"]:checked {
    background-color: #667eea;
    border-color: #667eea;
    transform: scale(1.05);
}

.permissions-grid input[type="checkbox"]:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* 树形菜单样式 */
.permission-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 10px;
    cursor: pointer;
    font-weight: 500;
    margin: 0;
    border: 1px solid #e9ecef;
    font-size: 0.9em;
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.permission-item:hover {
    background: linear-gradient(135deg, #e9ecef, #f8f9fa);
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.permission-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #667eea, #a8b1ff);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.permission-item:hover::before {
    opacity: 1;
}

.permission-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
    flex-shrink: 0;
    cursor: pointer;
    accent-color: #667eea;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    position: relative;
    top: 0;
    transition: all 0.3s ease;
}

.permission-item input[type="checkbox"]:checked {
    background-color: #667eea;
    border-color: #667eea;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.permission-item input[type="checkbox"]:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

.sub-permissions {
    margin-left: 25px;
    margin-top: 6px;
    margin-bottom: 10px;
    padding-left: 20px;
    border-left: 3px solid #667eea;
    position: relative;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.sub-permissions::before {
    content: '';
    position: absolute;
    left: -3px;
    top: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(to bottom, #667eea, #a8b1ff, #e9ecef);
    border-radius: 2px;
}

.sub-item {
    font-weight: 400;
    color: #495057;
    padding: 8px 15px;
    margin: 3px 0;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.sub-item:hover {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-color: #667eea;
    transform: translateX(3px);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

.sub-item::before {
    content: '';
    position: absolute;
    left: -18px;
    top: 50%;
    width: 10px;
    height: 2px;
    background: linear-gradient(to right, #667eea, #a8b1ff);
    transform: translateY(-50%);
    border-radius: 1px;
}

.sub-item::after {
    content: '';
    position: absolute;
    left: -18px;
    top: 50%;
    width: 6px;
    height: 6px;
    background: #667eea;
    border-radius: 50%;
    transform: translateY(-50%);
    opacity: 0.3;
}

/* 主权限勾选时子权限的样式 */
.sub-permissions .sub-permission:disabled + span {
    color: #999;
    opacity: 0.7;
}

.sub-permissions .sub-permission:disabled ~ .sub-item {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-color: #dee2e6;
    color: #6c757d;
    opacity: 0.7;
    transform: none;
}

.sub-permissions .sub-permission:disabled ~ .sub-item:hover {
    transform: none;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 权限分组标题样式 */
.permissions-container .form-group > label {
    font-weight: 600;
    margin-bottom: 12px;
    display: block;
    color: #333;
    font-size: 1em;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    border-left: 5px solid #667eea;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.permissions-container .form-group > label::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 30px;
    height: 100%;
    background: linear-gradient(135deg, transparent, rgba(102, 126, 234, 0.1));
    transform: skewX(-15deg);
}

.permissions-container .form-group small {
    font-weight: 400;
    color: #666;
    font-size: 0.85em;
}

/* 权限分组样式 */
.form-group .permissions-grid {
    margin-bottom: 12px;
}

.form-group:last-child .permissions-grid {
    margin-bottom: 0;
}

/* Message Styles */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 1001;
    animation: slideIn 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.message-success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.message-error {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.message-info {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-container {
        padding: 10px;
    }
    
    .admin-header h1 {
        font-size: 2em;
    }
    
    .admin-nav {
        flex-direction: column;
        align-items: center;
    }
    
    .admin-nav button {
        width: 100%;
        max-width: 300px;
    }
    
    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .admin-table {
        font-size: 0.9em;
    }
    
    .admin-table th,
    .admin-table td {
        padding: 8px 10px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .permissions-grid {
        grid-template-columns: 1fr;
    }
}

/* Loading States */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Indicators */
.status-active {
    color: #28a745;
    font-weight: 600;
}

.status-inactive {
    color: #6c757d;
    font-weight: 600;
}

/* Enhanced Table Styles */
.admin-table tbody tr {
    transition: all 0.3s ease;
}

.admin-table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    transform: scale(1.01);
}

/* Button Groups */
.btn-group {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

/* Enhanced Form Validation */
.form-group input:invalid,
.form-group select:invalid,
.form-group textarea:invalid {
    border-color: #dc3545;
}

.form-group input:valid,
.form-group select:valid,
.form-group textarea:valid {
    border-color: #28a745;
}

/* Tooltip Styles */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8em;
    white-space: nowrap;
    z-index: 1000;
}

/* Custom Checkbox styles, inspired by permissions grid */
.custom-checkbox {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    gap: 8px;
}

.custom-checkbox input[type="checkbox"] {
    display: none; /* Hide the original checkbox */
}

.custom-checkbox span {
    position: relative;
    padding-left: 28px; /* Space for the custom checkbox */
    font-size: 1rem;
    line-height: 1.4;
}

/* The custom checkbox box */
.custom-checkbox span::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    background: #fff;
    transition: all 0.2s ease;
}

/* The checkmark style */
.custom-checkbox span::after {
    content: '';
    position: absolute;
    left: 7px;
    top: 50%;
    width: 6px;
    height: 12px;
    border: solid white;
    border-width: 0 3px 3px 0;
    transform: translateY(-50%) rotate(45deg) scale(0); /* Hidden by default */
    transition: transform 0.2s ease;
}

/* Style when checked */
.custom-checkbox input[type="checkbox"]:checked + span::before {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-color: #667eea;
}

.custom-checkbox input[type="checkbox"]:checked + span::after {
    transform: translateY(-50%) rotate(45deg) scale(1); /* Show checkmark */
}

.form-group.editor-wrapper {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
}

.editor-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px; /* Add a small gap between label, toolbar, and editor */
}

.text-editor-toolbar {
    margin-bottom: 8px;
}

.text-editor-toolbar .toolbar-row-top {
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
}

.text-editor-toolbar .toolbar-row-top select.toolbar-select {
    min-width: 65px;
    max-width: 65px;
    margin-right: 0;
}

.text-editor-toolbar .toolbar-row-top select.font-family-select {
    min-width: 120px;
    max-width: 220px;
}

.toolbar-group {
    display: flex;
    gap: 4px;
    padding: 0 8px;
    border-right: 1px solid #ddd;
    align-items: center; /* Vertically center all items in the group */
}

.toolbar-group:last-child {
    border-right: none;
}

.toolbar-separator {
    display: inline-block;
    width: 1px;
    height: 18px;
    background-color: #ccc;
    margin: 0 6px;
    vertical-align: middle;
}

.toolbar-btn {
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 2px;
    padding: 3px 5px;
    cursor: pointer;
    font-weight: bold;
    min-width: 24px;
    font-size: 11px;
    line-height: 1;
    transition: all 0.2s ease;
}

.toolbar-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.toolbar-btn.active {
    background-color: #007bff;
    color: white;
    border-color: #0056b3;
}

.toolbar-select {
    border: 1px solid #ccc;
    border-radius: 2px;
    padding: 2px 4px;
    background: #fff;
    font-size: 11px;
    min-width: 70px;
}

#label-content {
    width: 100%;
    min-height: 80px;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 8px;
    font-size: 12px;
    font-family: 'Microsoft YaHei', sans-serif;
    line-height: 1.4;
    resize: vertical;
    overflow-y: auto;
    background: #fff;
}

.label-preview-container {
    flex-shrink: 0;
    width: 270px; /* 3 inches at 90 DPI */
}

.label-preview-container h4 {
    margin: 0 0 6px 0;
    text-align: center;
    color: #666;
    font-weight: 600;
    font-size: 13px;
}

.label-preview {
    width: 270px;
    height: 180px;
    border: 2px dashed #aaa;
    background-color: #fff;
    padding: 3px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: stretch;
    border-radius: 3px;
    overflow: hidden;
}

.label-preview-content {
    width: 100%;
    height: 100%;
    display: block;
    word-break: break-all;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow: auto;
    padding: 2px;
    box-sizing: border-box;
}

.label-preview-content > div {
    width: 100%;
    box-sizing: border-box;
}

/* 优化弹窗样式 */
.modal-content {
    max-width: 740px;
    max-height: 82vh;
    overflow-y: auto;
    padding: 16px;
}

.modal-content h2 {
    margin-bottom: 12px;
    font-size: 15px;
    color: #333;
}

.form-group {
    margin-bottom: 10px;
}

.form-group label {
    display: block;
    margin-bottom: 3px;
    font-weight: 500;
    color: #333;
    font-size: 12px;
}

.form-group input[type="text"],
.form-group select {
    width: 100%;
    padding: 5px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 12px;
}

/* 部门选择器样式优化 */
.department-selector {
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 6px;
    background: #f9f9f9;
    min-height: 50px;
    position: relative;
}

.department-selector .selected-departments {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 6px;
}

.department-selector .department-tag {
    background: #007bff;
    color: white;
    padding: 1px 6px;
    border-radius: 10px;
    font-size: 11px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.department-selector .department-tag .remove-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 12px;
    padding: 0;
    margin: 0;
    line-height: 1;
}

.department-selector .department-tag .remove-btn:hover {
    color: #ffcccb;
}

.department-selector input[type="text"] {
    width: 100%;
    padding: 4px 8px;
    border: 1px solid #ccc;
    border-radius: 2px;
    font-size: 12px;
}

.department-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 3px 3px;
    max-height: 120px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dropdown-item {
    padding: 6px 10px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item:last-child {
    border-bottom: none;
}

/* 复选框样式优化 */
.checkbox-group {
    margin-top: 8px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    font-size: 13px;
}

.custom-checkbox {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    gap: 8px;
}

.custom-checkbox input[type="checkbox"] {
    display: none; /* Hide the original checkbox */
}

.custom-checkbox span {
    position: relative;
    padding-left: 28px; /* Space for the custom checkbox */
    font-size: 1rem;
    line-height: 1.4;
}

/* The custom checkbox box */
.custom-checkbox span::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    background: #fff;
    transition: all 0.2s ease;
}

/* The checkmark style */
.custom-checkbox span::after {
    content: '';
    position: absolute;
    left: 7px;
    top: 50%;
    width: 6px;
    height: 12px;
    border: solid white;
    border-width: 0 3px 3px 0;
    transform: translateY(-50%) rotate(45deg) scale(0); /* Hidden by default */
    transition: transform 0.2s ease;
}

/* Style when checked */
.custom-checkbox input[type="checkbox"]:checked + span::before {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-color: #667eea;
}

.custom-checkbox input[type="checkbox"]:checked + span::after {
    transform: translateY(-50%) rotate(45deg) scale(1); /* Show checkmark */
}

/* 按钮样式优化 */
.modal-content button {
    background: #007bff;
    color: white;
    border: none;
    padding: 6px 14px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    margin-top: 10px;
    transition: background-color 0.2s ease;
}

.modal-content button:hover {
    background: #0056b3;
}

/* 关闭按钮优化 */
.close-button {
    position: absolute;
    right: 12px;
    top: 12px;
    font-size: 18px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
    line-height: 1;
}

.close-button:hover {
    color: #333;
}

.rich-text-editor {
    border: 1px solid #ccc;
    padding: 10px;
    min-height: 150px;
    border-radius: 5px;
    background-color: #fff;
    color: #333;
    font-size: 16px;
    line-height: 1.5;
    outline: none;
    resize: vertical;
    overflow-y: auto;
    width: 100%; /* Ensure editor takes full width */
    box-sizing: border-box; /* Include padding and border in the width */
}

.rich-text-editor:empty:before {
    content: attr(placeholder);
    color: #999;
    pointer-events: none;
}

.rich-text-editor:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.admin-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.action-group-left, .action-group-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.action-group-right label {
    font-weight: 500;
    color: #333;
}

.form-control {
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #fff;
    transition: border-color 0.2s;
}

.form-control:focus {
    border-color: #667eea;
    outline: none;
}

.admin-table-container {
    overflow-x: auto;
}

/* 清空按钮样式 */
.btn-clear {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-clear:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* 表格功能样式 */
.table-buttons-group {
    display: flex;
    gap: 5px;
    align-items: center;
}

.table-buttons-group .toolbar-btn {
    font-size: 12px;
    padding: 6px 10px;
    min-width: 40px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    font-weight: 500;
}

/* 表格按钮悬停效果 */
.insert-table-btn:hover {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.insert-nested-table-btn:hover {
    background: linear-gradient(135deg, #6f42c1, #5a32a3) !important;
}

.add-row-btn:hover,
.add-col-btn:hover {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
    color: white !important;
}

.delete-row-btn:hover,
.delete-col-btn:hover {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    color: white !important;
}

/* 添加按钮的特殊样式 */
.add-row-btn,
.add-col-btn {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border: 1px solid #138496;
}

.add-row-btn:hover,
.add-col-btn:hover {
    background: linear-gradient(135deg, #138496, #117a8b) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

/* 删除按钮的特殊样式 */
.delete-row-btn,
.delete-col-btn {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border: 1px solid #c82333;
}

.delete-row-btn:hover,
.delete-col-btn:hover {
    background: linear-gradient(135deg, #c82333, #bd2130) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* 编辑器内表格样式 */
.rich-text-editor table {
    border-collapse: collapse;
    width: 100%;
    margin: 5px 0;
    border: 1px solid #000;
}

.rich-text-editor table td,
.rich-text-editor table th {
    border: 1px solid #000;
    padding: 4px 8px;
    text-align: center;
    min-width: 20px;
    min-height: 15px;
    vertical-align: middle;
    position: relative;
}

.rich-text-editor table td:focus,
.rich-text-editor table th:focus {
    outline: 2px solid #007bff;
    outline-offset: -2px;
}

.rich-text-editor table td:hover,
.rich-text-editor table th:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* 表格调整大小手柄样式 */
.resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    cursor: se-resize;
    border-radius: 2px;
    opacity: 0;
    transition: opacity 0.2s, transform 0.2s;
    z-index: 1000;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.resize-handle:hover {
    transform: scale(1.2);
    opacity: 1 !important;
}

.resize-handle-right {
    position: absolute;
    top: 0;
    right: -2px;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    cursor: col-resize;
    opacity: 0;
    transition: opacity 0.2s;
    z-index: 1000;
    border-radius: 2px;
}

.resize-handle-right:hover {
    opacity: 1 !important;
    width: 6px;
    right: -3px;
}

.resize-handle-bottom {
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    cursor: row-resize;
    opacity: 0;
    transition: opacity 0.2s;
    z-index: 1000;
    border-radius: 2px;
}

.resize-handle-bottom:hover {
    opacity: 1 !important;
    height: 6px;
    bottom: -3px;
}

/* 嵌套表格的调整手柄样式 */
.nested-table .resize-handle {
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
    width: 6px;
    height: 6px;
}

.nested-table .resize-handle-right {
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
    width: 3px;
}

.nested-table .resize-handle-bottom {
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
    height: 3px;
}

/* 内嵌表格容器样式 */
.nested-table-container {
    position: relative;
    display: inline-block;
    margin: 2px;
    border: 1px dashed transparent;
    transition: border-color 0.2s;
}

.nested-table-container:hover {
    border-color: #ff6b6b;
}

/* 普通表格容器样式 */
.table-container {
    position: relative;
    display: inline-block;
    margin: 5px;
    border: 1px dashed transparent;
    transition: border-color 0.2s;
}

.table-container:hover {
    border-color: #007bff;
}

/* 移动手柄样式 */
.move-handle {
    position: absolute;
    top: -8px;
    left: -8px;
    width: 16px;
    height: 16px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border-radius: 50%;
    cursor: move;
    opacity: 0;
    transition: opacity 0.2s, transform 0.2s;
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.move-handle:hover {
    transform: scale(1.1);
    opacity: 1 !important;
}

/* 普通表格的移动手柄样式 */
.table-container .move-handle {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

/* 调整大小时的视觉反馈 */
.rich-text-editor table td.resizing,
.rich-text-editor table th.resizing {
    background-color: rgba(0, 123, 255, 0.2) !important;
    outline: 2px solid #007bff !important;
}

/* 预览区域表格样式 */
.label-preview table {
    border-collapse: collapse;
    width: 100%;
    margin: 2px 0;
    border: 1px solid #000;
}

.label-preview table td,
.label-preview table th {
    border: 1px solid #000;
    padding: 2px 4px;
    text-align: center;
    font-size: 10px;
    line-height: 1.2;
}

/* 响应式表格按钮 */
@media (max-width: 768px) {
    .table-buttons-group {
        flex-wrap: wrap;
        gap: 3px;
    }
    
    .table-buttons-group .toolbar-btn {
        font-size: 12px;
        padding: 4px 6px;
        min-width: 28px;
        height: 28px;
    }
    
    .rich-text-editor table td,
    .rich-text-editor table th {
        padding: 2px 4px;
        min-width: 40px;
        min-height: 25px;
        font-size: 12px;
    }
}

.resizable-cell {
    position: relative;
    min-width: 5px !important;
    min-height: 5px !important;
    border: 1px solid #000;
    padding: 4px;
    text-align: center;
    vertical-align: middle;
    cursor: text;
    transition: background-color 0.2s;
}

#audit-log-table th:nth-child(3), /* 操作列 */
#audit-log-table td:nth-child(3) {
    width: 550px;
    text-align: left;
}

#user-groups-table th:nth-child(3), /* 所拥有的权限列 */
#user-groups-table td:nth-child(3) {
    width: 550px;
}

/* 统一按钮样式 */
.btn-refresh, #refresh-permissions-btn {
    background: #4caf50;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 6px 16px;
    font-size: 15px;
    cursor: pointer;
    transition: background 0.2s;
    margin-left: 8px;
}
#refresh-permissions-btn:hover, .btn-refresh:hover {
    background: #388e3c;
}

/* 气泡美化与定位 */
.admin-nav a {
    position: relative;
}
.bell-badge {
    display: none;
    position: absolute;
    top: 50%;
    right: -6px;
    transform: translateY(-50%);
    min-width: 18px;
    height: 18px;
    padding: 0 5px;
    background: #ff3b30;
    color: #fff;
    font-size: 12px;
    font-weight: bold;
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.08);
    text-align: center;
    line-height: 18px;
    z-index: 2;
    pointer-events: none;
}

/* 系统设定页面样式 */
.admin-form-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.admin-form-section {
    margin-bottom: 30px;
}

.admin-form-section h3 {
    color: #333;
    font-size: 1.5em;
    margin-bottom: 10px;
    font-weight: 600;
}

.form-description {
    color: #666;
    font-size: 0.95em;
    margin-bottom: 20px;
    line-height: 1.5;
}

.admin-form {
    max-width: 800px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 150px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95em;
}

.form-group input[type="number"] {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1em;
    transition: all 0.3s ease;
    background: white;
}

.form-group input[type="number"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 禁用的输入框样式 */
.form-group input:disabled,
.form-group input[type="text"]:disabled,
.form-group input[type="number"]:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    border-color: #dee2e6;
    cursor: not-allowed;
    opacity: 0.7;
}

.form-group input:disabled::placeholder {
    color: #6c757d;
    font-style: italic;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary, .btn-danger {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #495057;
    border: 2px solid #e1e5e9;
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.btn-danger {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

/* 权限标签样式 */
.permission-tag {
    transition: all 0.2s ease;
    cursor: default;
}

.permission-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 用户组管理表格权限列样式 */
.user-groups-table .permissions-cell {
    max-width: 450px;
    padding: 12px 8px;
    line-height: 1.8;
}

.user-groups-table .permissions-cell .permission-tag {
    margin: 2px 3px;
    animation: fadeInScale 0.3s ease;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 权限分类图例 */
.permissions-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin: 15px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9em;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid;
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn-primary, .btn-secondary, .btn-danger {
        width: 100%;
    }

    .user-groups-table .permissions-cell {
        max-width: 250px;
    }

    .permission-tag {
        font-size: 0.8em !important;
        padding: 1px 6px !important;
    }

    .permissions-legend {
        flex-direction: column;
        gap: 8px;
    }
}

/* 用户下拉菜单样式 */
.user-dropdown {
    position: relative;
    display: inline-block;
}

.user-info {
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    background: linear-gradient(45deg, #667eea, #764ba2);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    font-weight: 600;
}

.user-info:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    transform: translateY(-1px);
}

.dropdown-arrow {
    font-size: 12px;
    transition: transform 0.2s ease;
}

.user-dropdown:hover .dropdown-arrow {
    transform: rotate(180deg);
}

.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 160px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.user-dropdown:hover .user-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.language-menu-item {
    position: relative;
    padding: 12px 16px;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.2s ease;
}

.language-menu-item:hover {
    background-color: #f8f9fa;
}

.submenu-arrow {
    font-size: 12px;
    transition: transform 0.2s ease;
}

.language-menu-item:hover .submenu-arrow {
    transform: translateX(3px);
}

.language-submenu {
    position: absolute;
    top: 0;
    left: 100%;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 140px;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.language-menu-item:hover .language-submenu {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

.language-option {
    padding: 10px 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-radius: 6px;
    margin: 4px;
}

.language-option:hover {
    background-color: #e3f2fd;
}

.language-option.active {
    background-color: #2196f3;
    color: white;
}

.language-option .flag {
    font-size: 16px;
}

.language-option span:last-child {
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-dropdown-menu {
        right: 0;
        left: auto;
    }

    .language-submenu {
        left: auto;
        right: 100%;
        transform: translateX(10px);
    }

    .language-menu-item:hover .language-submenu {
        transform: translateX(0);
    }
}