/* 聊天切换按钮 */
#chat-toggle-button {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 999;
    transition: transform 0.2s ease;
}

#chat-toggle-button:hover {
    transform: scale(1.1);
}

/* 聊天窗口 */
.chat-window {
    display: none;
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 350px;
    height: 500px;
    background-color: #ffffff;
    border-radius: 15px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    flex-direction: column;
    overflow: hidden;
}

/* 聊天窗口头部 */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}
.chat-header h3 {
    margin: 0;
    font-size: 1.1rem;
}
.chat-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
}

/* 模式切换 */
.chat-mode-switcher button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 4px 8px;
    border-radius: 5px;
    cursor: pointer;
    margin-left: 5px;
    font-size: 0.8rem;
}
.chat-mode-switcher button.active {
    background: white;
    color: #667eea;
    font-weight: bold;
}

/* 消息区域 */
.chat-messages {
    flex-grow: 1;
    padding: 15px;
    overflow-y: auto;
    background-color: #f9f9f9;
    display: flex;
    flex-direction: column;
}

/* 输入区域 */
.chat-input-area {
    display: flex;
    padding: 10px;
    border-top: 1px solid #eee;
}

.chat-input-container {
    flex-grow: 1;
    position: relative;
}

#chat-input {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 0.9rem;
    outline: none;
    box-sizing: border-box;
}
#chat-input:focus {
    border-color: #667eea;
}
#chat-send-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    margin-left: 10px;
    cursor: pointer;
    font-weight: bold;
}

/* 用户建议下拉列表 */
.user-suggestions {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    margin-bottom: 5px;
}

.user-suggestion-item {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;
}

.user-suggestion-item:last-child {
    border-bottom: none;
}

.user-suggestion-item:hover,
.user-suggestion-item.selected {
    background-color: #f8f9fa;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #e8f2ff;
    color: #4a5568;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    margin-right: 10px;
    flex-shrink: 0;
}

.user-suggestions .user-info {
    flex-grow: 1;
    min-width: 0;
    background: #e8f2ff;
    padding: 8px 12px;
    border-radius: 6px;
    margin-left: 8px;
}

.user-suggestions .user-name {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-suggestions .user-code {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

/* @用户高亮样式 */
.mention {
    color: #e74c3c;
    font-weight: 600;
    background-color: rgba(231, 76, 60, 0.1);
    padding: 1px 3px;
    border-radius: 3px;
}

/* 气泡 */
.chat-notification-bubble {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #ff3b30;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 12px;
    font-weight: bold;
    border: 2px solid white;
}

/* 消息体 */
.chat-msg {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 18px;
    max-width: 80%;
    word-wrap: break-word;
}
.chat-msg-bot {
    background-color: #e5e5ea;
    color: #000;
    align-self: flex-start;
}
.chat-msg-user-self { /* 机器人模式下自己的消息 */
    background-color: #007bff;
    color: white;
    align-self: flex-end;
}
.chat-msg-user, .chat-msg-other {
    display: flex;
    flex-direction: column;
}
.chat-msg-user { /* 公众模式下自己的消息 */
    background-color: #dcf8c6;
    align-self: flex-end;
}
.chat-msg-other { /* 公众模式下别人的消息 */
    background-color: #fff;
    box-shadow: 0 1px 1px rgba(0,0,0,0.05);
    align-self: flex-start;
}
.chat-msg-username {
    font-weight: bold;
    font-size: 0.8rem;
    color: #555;
    margin-bottom: 4px;
}
.chat-msg-other .chat-msg-username {
    color: #007bff;
}

.chat-date-divider {
  text-align: center;
  color: #888;
  font-size: 13px;
  margin: 16px 0 8px 0;
  position: relative;
}
.chat-date-divider:before, .chat-date-divider:after {
  content: '';
  display: inline-block;
  width: 40px;
  height: 1px;
  background: #e0e0e0;
  vertical-align: middle;
  margin: 0 8px;
}

.chat-msg-bubble {
  display: inline-block;
  background: #f5f5f5;
  border-radius: 16px;
  padding: 6px 16px;
  margin: 0 8px 0 0;
  color: #222;
  font-size: 15px;
  min-width: 40px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.04);
}

.chat-msg-user .chat-msg-bubble {
  background: #e6f7ff;
  color: #007bff;
}

/* 移除机器人气泡和用户名特殊色，统一样式 */
/* .chat-msg-bot .chat-msg-bubble { background: #fffbe6; color: #b8860b; } */
/* .chat-msg-bot .chat-msg-username { color: #b8860b; } */

.chat-msg-meta {
  margin-bottom: 2px;
  font-size: 13px;
  color: #888;
  line-height: 1.2;
}

.chat-msg-username {
  font-weight: bold;
  color: #007bff;
}

.chat-msg-bot .chat-msg-username {
  color: #b8860b;
} 