/* 语言切换器样式 */
.language-switcher-trigger {
    position: relative;
    cursor: pointer !important;
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 6px;
}

.language-switcher-trigger:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

/* 语言菜单 */
.language-menu {
    position: fixed;
    display: none;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1000;
    min-width: 200px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.language-menu-header {
    padding: 12px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    font-size: 14px;
    text-align: center;
}

.language-options {
    padding: 8px 0;
}

.language-option {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 12px;
}

.language-option:hover {
    background: rgba(102, 126, 234, 0.1);
}

.language-option.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.language-option.active:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.language-flag {
    font-size: 18px;
    width: 24px;
    text-align: center;
}

.language-name {
    font-size: 14px;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .language-menu {
        min-width: 180px;
        right: 10px !important;
    }
    
    .language-option {
        padding: 12px 16px;
    }
    
    .language-flag {
        font-size: 16px;
        width: 20px;
    }
    
    .language-name {
        font-size: 13px;
    }
}

/* 动画效果 */
.language-menu {
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 语言切换时的过渡效果 */
[data-i18n] {
    transition: opacity 0.2s ease;
}

.language-switching [data-i18n] {
    opacity: 0.7;
}

/* 确保菜单在所有元素之上 */
.language-menu {
    z-index: 9999;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .language-menu {
        background: rgba(30, 30, 30, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
        color: white;
    }
    
    .language-option:hover {
        background: rgba(102, 126, 234, 0.2);
    }
}
