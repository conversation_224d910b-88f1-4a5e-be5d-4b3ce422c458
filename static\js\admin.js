﻿// Admin page functionality
document.addEventListener('DOMContentLoaded', function() {
    // 调试：检查权限设置
    console.log('用户权限:', window.userPermissions);
    console.log('can_reply_problem_solve权限:', window.userPermissions?.can_reply_problem_solve);
    
    // Initialize admin page
    initializeAdminPage();
    
    // 初始化后台模块
    if (window.AdminFeedbackModule) {
        window.adminFeedbackModule = new AdminFeedbackModule();
    }
    if (window.AdminAnnouncementsModule) {
        window.adminAnnouncementsModule = new AdminAnnouncementsModule();
    }
    if (window.adminUsersModule) {
        // 已在模块内自动初始化
    }
});

// 权限检查函数
function checkPermission(permissionName) {
    // 从全局变量或session中获取用户权限
    const permissions = window.userPermissions || {};
    return permissions[permissionName] || false;
}

// 根据权限生成操作按钮HTML
function generateActionButtons(actions) {
    const buttons = [];
    
    actions.forEach(action => {
        if (checkPermission(action.permission)) {
            buttons.push(`<button class="btn-${action.type}" onclick="${action.onclick}">${action.text}</button>`);
        }
    });
    
    return buttons.join('');
}

// 权限控制：隐藏/显示添加按钮
function setupPermissionControls() {
    // 标签管理权限控制
    if (!checkPermission('can_manage_labels_add')) {
        const addLabelBtn = document.querySelector('#labels .btn-add');
        if (addLabelBtn) addLabelBtn.style.display = 'none';
    }
    
    // 用户管理权限控制
    if (!checkPermission('can_manage_users_add')) {
        const addUserBtn = document.querySelector('#users .btn-add');
        if (addUserBtn) addUserBtn.style.display = 'none';
    }
    
    // 部门管理权限控制
    if (!checkPermission('can_manage_departments_add')) {
        const addDeptBtn = document.querySelector('#departments .btn-add');
        if (addDeptBtn) addDeptBtn.style.display = 'none';
    }
    
    // 用户组管理权限控制
    if (!checkPermission('can_manage_user_groups_add')) {
        const addGroupBtn = document.querySelector('#user-groups .btn-add');
        if (addGroupBtn) addGroupBtn.style.display = 'none';
    }
    
    // 公告管理权限控制
    if (!checkPermission('can_manage_announcements_add')) {
        const addAnnouncementBtn = document.querySelector('#announcements .btn-add');
        if (addAnnouncementBtn) addAnnouncementBtn.style.display = 'none';
    }
    
    // 条形码前缀管理权限控制
    if (!checkPermission('can_manage_barcode_prefixes_add')) {
        const addPrefixBtn = document.querySelector('#barcode-prefixes .btn-add');
        if (addPrefixBtn) addPrefixBtn.style.display = 'none';
    }
    
    // 聊天机器人词库管理权限控制
    if (!checkPermission('can_manage_chatbot_kb_add')) {
        const addKbBtn = document.querySelector('#chatbot-kb .btn-add');
        if (addKbBtn) addKbBtn.style.display = 'none';
    }

    // 操作日志菜单权限控制
    if (!checkPermission('can_view_audit_log')) {
        const auditLogNavItem = document.querySelector('.admin-nav a[data-target="audit-log"]');
        if (auditLogNavItem) {
            auditLogNavItem.parentElement.style.display = 'none';
        }
    }

    // 系统设定菜单权限控制
    if (!checkPermission('can_manage_system_settings')) {
        const systemSettingsNavItem = document.querySelector('.admin-nav a[data-target="system-settings"]');
        if (systemSettingsNavItem) {
            systemSettingsNavItem.parentElement.style.display = 'none';
        }
    }
}

function escapeHTML(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function initializeAdminPage() {
    // Set up navigation
    setupNavigation();
    
    // 设置权限控制
    setupPermissionControls();
    
    // Load initial data for the first section
    loadSectionData('labels');
}

function setupNavigation() {
    const navItems = document.querySelectorAll('.admin-nav a');
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
            const target = this.getAttribute('data-target');
            showSection(target);
            loadSectionData(target);
            // Force display for departments as a fallback fix
            if (target === 'departments') {
                const deptSection = document.getElementById('departments');
                if (deptSection) {
                    deptSection.style.display = 'block';
                }
            }
            // 记忆当前菜单
            localStorage.setItem('admin-active-section', target);
        });
    });
    // 页面加载时自动高亮和显示上次菜单
    const lastSection = localStorage.getItem('admin-active-section');
    if (lastSection) {
        navItems.forEach(nav => {
            if (nav.getAttribute('data-target') === lastSection) {
                nav.classList.add('active');
            } else {
                nav.classList.remove('active');
            }
        });
        showSection(lastSection);
        loadSectionData(lastSection);
    }
}

function showSection(target) {
    const sections = document.querySelectorAll('.admin-content section');
    sections.forEach(section => {
        section.style.display = section.id === target ? 'block' : 'none';
    });
}

function loadSectionData(section) {
    switch(section) {
        case 'labels':
            loadLabels();
            break;
        case 'users':
            if (window.adminUsersModule) {
                window.adminUsersModule.loadUsers();
            }
            break;
        case 'departments':
            if (window.adminDepartmentsModule) {
                window.adminDepartmentsModule.loadDepartments();
            }
            break;
        case 'user-groups':
            if (window.adminUserGroupsModule) {
                window.adminUserGroupsModule.loadUserGroups();
            }
            break;
        case 'announcements':
            function tryLoadAnnouncements(retry = 0) {
                if (window.adminAnnouncementsModule && typeof window.adminAnnouncementsModule.loadAnnouncements === 'function') {
                    window.adminAnnouncementsModule.loadAnnouncements();
                } else if (retry < 10) {
                    setTimeout(() => tryLoadAnnouncements(retry + 1), 100);
                }
            }
            tryLoadAnnouncements();
            break;
        case 'feedback':
            function tryLoadFeedback(retry = 0) {
                if (window.adminFeedbackModule && typeof window.adminFeedbackModule.loadFeedback === 'function') {
                    window.adminFeedbackModule.loadFeedback();
                } else if (retry < 10) {
                    setTimeout(() => tryLoadFeedback(retry + 1), 100);
                }
            }
            tryLoadFeedback();
            break;
        case 'problem-solves':
            // 兜底处理，确保模块加载后再调用
            function tryLoadProblemSolves(retry = 0) {
                if (window.adminProblemSolvesModule && typeof window.adminProblemSolvesModule.loadProblemSolves === 'function') {
                    window.adminProblemSolvesModule.loadProblemSolves();
                } else if (retry < 10) {
                    setTimeout(() => tryLoadProblemSolves(retry + 1), 100);
                }
            }
            tryLoadProblemSolves();
            break;
        case 'barcode-prefixes': {
            function tryLoadBarcodePrefixes(retry = 0) {
                if (window.adminBarcodePrefixesModule && typeof window.adminBarcodePrefixesModule.loadBarcodePrefixes === 'function') {
                    window.adminBarcodePrefixesModule.loadBarcodePrefixes();
                } else if (retry < 10) {
                    setTimeout(() => tryLoadBarcodePrefixes(retry + 1), 100);
                }
            }
            tryLoadBarcodePrefixes();
            break;
        }
        case 'chatbot-kb': {
            function tryLoadChatbotKB(retry = 0) {
                if (window.adminChatbotKBModule && typeof window.adminChatbotKBModule.loadChatbotKB === 'function') {
                    window.adminChatbotKBModule.loadChatbotKB();
                } else if (retry < 10) {
                    setTimeout(() => tryLoadChatbotKB(retry + 1), 100);
                }
            }
            tryLoadChatbotKB();
            break;
        }
        case 'audit-log': {
            function tryLoadAuditLog(retry = 0) {
                if (window.adminAuditLogModule && typeof window.adminAuditLogModule.loadAuditLog === 'function') {
                    window.adminAuditLogModule.loadAuditLog();
                } else if (retry < 10) {
                    setTimeout(() => tryLoadAuditLog(retry + 1), 100);
                }
            }
            tryLoadAuditLog();
            break;
        }
        case 'system-settings': {
            function tryLoadSystemSettings(retry = 0) {
                if (window.adminSystemSettingsModule && typeof window.adminSystemSettingsModule.loadSettings === 'function') {
                    window.adminSystemSettingsModule.loadSettings();
                } else if (retry < 10) {
                    setTimeout(() => tryLoadSystemSettings(retry + 1), 100);
                }
            }
            tryLoadSystemSettings();
            break;
        }
    }
}

function renderLabelContent(html, vAlign) {
    if (!html) return '';
    const tempContainer = document.createElement('div');
    tempContainer.innerHTML = html;
    const date = new Date();
    const userCode = 'ADM001';
    function replacePlaceholdersInNode(node) {
        if (node.nodeType === Node.TEXT_NODE) {
            let text = node.textContent;
            const replacements = {
                '\\[date\\]': `${String(date.getMonth() + 1).padStart(2, '0')}/${String(date.getDate()).padStart(2, '0')}/${date.getFullYear()}`,
                '\\[week\\]': ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][date.getDay()],
                '\\[user\\]': userCode
            };
            for (const [placeholder, value] of Object.entries(replacements)) {
                text = text.replace(new RegExp(placeholder, 'g'), value);
            }
            if (text !== node.textContent) {
                node.textContent = text;
            }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            for (let i = 0; i < node.childNodes.length; i++) {
                replacePlaceholdersInNode(node.childNodes[i]);
            }
        }
    }
    replacePlaceholdersInNode(tempContainer);
    const vAlignMap = { top: 'flex-start', center: 'center', bottom: 'flex-end' };
    const align = vAlignMap[vAlign] || vAlignMap['center'];
    const wrapper = document.createElement('div');
    wrapper.style.cssText = `
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: ${align};
        align-items: stretch;
        overflow: hidden;
    `;
    // 将tempContainer的所有子节点移动到wrapper中，保留内联样式
    while (tempContainer.firstChild) {
        wrapper.appendChild(tempContainer.firstChild);
    }
    return wrapper.outerHTML;
}

// Labels Management
function loadLabels() {
    if (!checkPermission('can_manage_labels')) {
        const labelManagementText = window.t ? window.t('label_management') : '标签管理';
        showNoPermission('labels', 3, labelManagementText);
        return;
    }
    const tableBody = document.getElementById('labels-table-body');
    if (!tableBody) return;
    const loadingText = window.t ? window.t('loading') : '正在加载...';
    tableBody.innerHTML = `<tr><td colspan="9" class="empty-state">${loadingText}</td></tr>`;
    const filter = document.getElementById('label-type-filter');
    const selectedType = filter ? filter.value : 'all';
    fetch('/api/labels')
        .then(response => response.json())
        .then(data => {
            const filteredData = selectedType === 'all' 
                ? data 
                : data.filter(label => label.type === selectedType);
            if (filteredData.length === 0) {
                const noLabelsText = window.t ? window.t('no_labels_found') : '没有找到符合条件的标签。';
                tableBody.innerHTML = `<tr><td colspan="9" class="empty-state">${noLabelsText}</td></tr>`;
                return;
            }
            tableBody.innerHTML = '';
            filteredData.forEach(label => {
                const row = document.createElement('tr');
                const renderedContent = renderLabelContent(label.content || '', label.v_align || 'center');
                
                // 根据权限生成操作按钮
                const actionButtons = [];
                if (checkPermission('can_manage_labels_edit')) {
                    const editText = window.t ? window.t('edit') : '编辑';
                    actionButtons.push(`<button onclick="editLabel(${label.id})">${editText}</button>`);
                }
                if (checkPermission('can_manage_labels_delete')) {
                    const deleteText = window.t ? window.t('delete') : '删除';
                    actionButtons.push(`<button onclick="deleteLabel(${label.id})">${deleteText}</button>`);
                }
                
                row.innerHTML = `
                    <td>${label.id}</td>
                    <td>${escapeHTML(label.name)}</td>
                    <td>${escapeHTML(label.type)}</td>
                    <td class="content-cell-preview">
                        <div class="table-preview-wrapper">
                            <div class="table-preview-content">
                                ${renderedContent}
                            </div>
                        </div>
                    </td>
                    <td>${label.print_prompt ? (window.t ? window.t('yes') : '是') : (window.t ? window.t('no') : '否')}</td>
                    <td>${label.print_quantity || 1}</td>
                    <td>${escapeHTML(label.copy_info || '')}</td>
                    <td>${escapeHTML(label.department_names || 'N/A')}</td>
                    <td>
                        ${actionButtons.join('')}
                    </td>
                `;
                tableBody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('Error fetching labels:', error);
            const loadingFailedText = window.t ? window.t('loading_labels_failed') : '加载标签失败。';
            tableBody.innerHTML = `<tr><td colspan="9" class="empty-state">${loadingFailedText}</td></tr>`;
        });
}

// Add event listener for the filter dropdown once the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const filter = document.getElementById('label-type-filter');
    if (filter) {
        filter.addEventListener('change', loadLabels);
    }
});

function getLabelTypeName(type) {
    const types = {
        'normal': '普通标签',
        'advanced': '高级标签',
        'special': '特殊标签',
        'global': '全局标签'
    };
    return types[type] || type;
}

// 保留唯一正确的getEditorToolbarHTML函数定义，放在setupRichTextEditor等相关函数之前：
function getEditorToolbarHTML(toolbarId) {
    const fontSizes = [6, 8, 10, 12, 14, 16, 18, 20, 24, 28, 32, 36, 48, 54, 60, 66, 72, 84, 96, 120, 150, 200];
    const fontSizeOptions = fontSizes.map(s => `<option value=\"${s}px\">${s}</option>`).join('');
    const letterSpacings = [-1, -0.5, 0, 0.5, 1, 1.5, 2, 3, 4, 5];
    const letterSpacingOptions = letterSpacings.map(s => `<option value=\"${s}px\">${s}</option>`).join('');
    const lineHeights = [0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.5, 1.8, 2.0, 2.5, 3.0, 3.5, 4.0];
    const lineHeightOptions = lineHeights.map(h => `<option value=\"${h}\">${h.toFixed(1)}</option>`).join('');
    return `
        <div class=\"text-editor-toolbar\" id=\"${toolbarId}\">\n            <div class=\"toolbar-row toolbar-row-top\">\n                <select class=\"font-family-select toolbar-select\"></select>\n                <select class=\"font-size-select toolbar-select\" style=\"width: 65px;\">\n                    <option value=\"auto\">自动</option>\n                    ${fontSizeOptions}\n                </select>\n                <select class=\"letter-spacing-select toolbar-select\" style=\"width: 65px;\">\n                    <option value=\"\">字间距</option>\n                    ${letterSpacingOptions}\n                </select>\n                <select class=\"line-height-select toolbar-select\" style=\"width: 65px;\">\n                    <option value=\"\">行距</option>\n                    ${lineHeightOptions}\n                </select>\n                <select class=\"writing-mode-select toolbar-select\" title=\"文字排行\" style=\"width: 65px;\">\n                    <option value=\"horizontal\">横排</option>\n                    <option value=\"vertical\">竖排</option>\n                    <option value=\"rotate\">旋转...</option>\n                </select>\n            </div>\n            <div class=\"toolbar-row toolbar-row-bottom\">\n                <button type=\"button\" class=\"toolbar-btn bold-btn\" title=\"加粗\">B</button>\n                <button type=\"button\" class=\"toolbar-btn italic-btn\" title=\"斜体\" style=\"font-style: italic;\">I</button>\n                <button type=\"button\" class=\"toolbar-btn underline-btn\" title=\"下划线\" style=\"text-decoration: underline;\">U</button>\n                <span class=\"toolbar-separator\"></span>\n                <button type=\"button\" class=\"toolbar-btn h-align-btn\" title=\"左对齐\" data-h-align=\"justifyLeft\">L</button>\n                <button type=\"button\" class=\"toolbar-btn h-align-btn\" title=\"居中\" data-h-align=\"justifyCenter\">C</button>\n                <button type=\"button\" class=\"toolbar-btn h-align-btn\" title=\"右对齐\" data-h-align=\"justifyRight\">R</button>\n                <span class=\"toolbar-separator\"></span>\n                <button type=\"button\" class=\"toolbar-btn v-align-btn\" title=\"顶对齐\" data-v-align=\"top\">T</button>\n                <button type=\"button\" class=\"toolbar-btn v-align-btn\" title=\"垂直居中\" data-v-align=\"center\">M</button>\n                <button type=\"button\" class=\"toolbar-btn v-align-btn\" title=\"底对齐\" data-v-align=\"bottom\">B</button>\n                <button type=\"button\" class=\"toolbar-btn insert-table-btn\" title=\"插入表格\">📊</button>\n                <button type=\"button\" class=\"toolbar-btn insert-nested-table-btn\" title=\"插入嵌套表格\">📋</button>\n                <span class=\"toolbar-separator\"></span>\n                <button type=\"button\" class=\"toolbar-btn insert-date-btn\" title=\"插入日期\">📅</button>\n                <button type=\"button\" class=\"toolbar-btn insert-day-btn\" title=\"插入星期\">🗓️</button>\n                <button type=\"button\" class=\"toolbar-btn insert-user-btn\" title=\"插入用户名\">👤</button>\n            </div>\n        </div>\n    `;
}

async function populateFontSelector(selectElement) {
    if (!selectElement) return;

    // Default fonts as a fallback
    const defaultFonts = ['Microsoft YaHei', 'SimSun', 'SimHei', 'KaiTi', 'FangSong', 'Arial', 'Courier New', 'Georgia', 'Times New Roman', 'Verdana'];

    // Feature detection for the Local Font Access API
    if ('queryLocalFonts' in window) {
        try {
            // Request permission and get the list of fonts
            const availableFonts = await window.queryLocalFonts();
            const fontFamilies = new Set(availableFonts.map(font => font.family));
            
            // Clear existing options and populate with system fonts
            selectElement.innerHTML = '';
            fontFamilies.forEach(font => {
                const option = document.createElement('option');
                option.value = option.textContent = font;
                selectElement.appendChild(option);
            });
            // Set a sensible default if possible
            if (fontFamilies.has('Microsoft YaHei')) {
                selectElement.value = 'Microsoft YaHei';
            }
        } catch (err) {
            console.error('Font access denied or error fetching fonts:', err);
            // Fallback to default list if permission is denied or an error occurs
            selectElement.innerHTML = '';
            defaultFonts.forEach(font => {
                const option = document.createElement('option');
                option.value = option.textContent = font;
                selectElement.appendChild(option);
            });
        }
    } else {
        // Fallback for browsers that do not support the API
        console.warn('Local Font Access API not supported. Using default font list.');
        selectElement.innerHTML = '';
        defaultFonts.forEach(font => {
            const option = document.createElement('option');
            option.value = option.textContent = font;
            selectElement.appendChild(option);
        });
    }
}

function setupRichTextEditor(options) {
    const { editorSelector, previewSelector, toolbarSelector, initialContent, initialVAlign, onVAlignChange } = options;

    const toolbar = document.querySelector(toolbarSelector);
    const contentEditor = document.querySelector(editorSelector);
    const previewContent = document.querySelector(previewSelector);

    if (!toolbar || !contentEditor || !previewContent) {
        console.error('Editor setup failed: One or more required elements not found.', options);
        return;
    }
    
    contentEditor.innerHTML = initialContent || '';
    
    // 为现有表格添加调整大小功能
    setTimeout(() => {
        const existingTables = contentEditor.querySelectorAll('table');
        existingTables.forEach(table => {
            initializeTableResize(table);
        });
        
        // 为现有嵌套表格添加移动功能
        const existingNestedContainers = contentEditor.querySelectorAll('.nested-table-container');
        existingNestedContainers.forEach(container => {
            container.classList.add('move-initialized');
            initializeNestedTableMove(container);
        });
        
        // 为现有普通表格添加移动功能
        const existingTableContainers = contentEditor.querySelectorAll('.table-container');
        existingTableContainers.forEach(container => {
            container.classList.add('move-initialized');
            initializeTableMove(container);
        });
        
        // 为没有容器的现有表格添加容器和移动功能
        const standaloneTables = contentEditor.querySelectorAll('table:not(.resize-initialized)');
        standaloneTables.forEach(table => {
            if (!table.closest('.table-container') && !table.closest('.nested-table-container')) {
                // 为独立表格添加容器
                const container = document.createElement('div');
                container.className = 'table-container';
                container.style.cssText = 'position: relative; display: inline-block; margin: 5px;';
                
                // 创建移动手柄
                const moveHandle = document.createElement('div');
                moveHandle.className = 'move-handle';
                moveHandle.style.cssText = 'position: absolute; top: -8px; left: -8px; width: 16px; height: 16px; background: #007bff; border-radius: 50%; cursor: move; opacity: 0; transition: opacity 0.2s; z-index: 1001; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;';
                moveHandle.innerHTML = '↕';
                
                // 包装表格
                table.parentNode.insertBefore(container, table);
                container.appendChild(table);
                container.appendChild(moveHandle);
                
                // 初始化移动功能
                initializeTableMove(container);
            }
        });
    }, 100);
    
    const fontSelect = toolbar.querySelector('.font-family-select');
    const sizeSelect = toolbar.querySelector('.font-size-select');
    const letterSpacingSelect = toolbar.querySelector('.letter-spacing-select');
    const lineHeightSelect = toolbar.querySelector('.line-height-select');
    const boldBtn = toolbar.querySelector('.bold-btn');
    const italicBtn = toolbar.querySelector('.italic-btn');
    const underlineBtn = toolbar.querySelector('.underline-btn');
    const insertDateBtn = toolbar.querySelector('.insert-date-btn');
    const insertDayBtn = toolbar.querySelector('.insert-day-btn');
    const insertUserBtn = toolbar.querySelector('.insert-user-btn');
    const insertTableBtn = toolbar.querySelector('.insert-table-btn');
    const insertNestedTableBtn = toolbar.querySelector('.insert-nested-table-btn');
    const addRowBtn = toolbar.querySelector('.add-row-btn');
    const addColBtn = toolbar.querySelector('.add-col-btn');
    const deleteRowBtn = toolbar.querySelector('.delete-row-btn');
    const deleteColBtn = toolbar.querySelector('.delete-col-btn');

    let styleState = {
        fontFamily: 'Microsoft YaHei',
        fontSize: 'auto',
        vAlign: initialVAlign || 'center'
    };
    
    // 设置初始vAlign按钮状态
    if (initialVAlign) {
        setTimeout(() => {
            toolbar.querySelectorAll('.v-align-btn').forEach(b => b.classList.remove('active'));
            const btn = toolbar.querySelector(`[data-v-align='${initialVAlign}']`);
            if (btn) btn.classList.add('active');
        }, 100);
    }
    
    // 表格功能函数
    function insertTable(rows = 3, cols = 3) {
        const rowsPrompt = window.t ? window.t('insert_table_rows') : '请输入表格行数:';
        const colsPrompt = window.t ? window.t('insert_table_cols') : '请输入表格列数:';
        const rowsInput = prompt(rowsPrompt, rows);
        const colsInput = prompt(colsPrompt, cols);

        if (!rowsInput || !colsInput) return;

        const rowCount = parseInt(rowsInput);
        const colCount = parseInt(colsInput);

        if (isNaN(rowCount) || isNaN(colCount) || rowCount < 1 || colCount < 1) {
            const validNumbersText = window.t ? window.t('please_enter_valid_numbers') : '请输入有效的行数和列数';
            alert(validNumbersText);
            return;
        }
        
        // 使用默认尺寸：宽度30px，高度20px
        const tableWidth = 30;
        const tableHeight = 20;
        
        // 计算每个单元格的尺寸
        const cellWidth = Math.floor(tableWidth / colCount);
        const cellHeight = Math.floor(tableHeight / rowCount);
        
        let tableHTML = '<div style="position: relative; display: inline-block; margin: 5px;" class="table-container">';
        tableHTML += `<table style="border-collapse: collapse; border: 1px solid #000; table-layout: fixed; width: ${tableWidth}px; height: ${tableHeight}px;" class="resizable-table">`;
        
        for (let i = 0; i < rowCount; i++) {
            tableHTML += '<tr>';
            for (let j = 0; j < colCount; j++) {
                tableHTML += `<td style="border: 1px solid #000; padding: 4px; text-align: center; vertical-align: middle; min-width: 5px; min-height: 5px; position: relative; width: ${cellWidth}px; height: ${cellHeight}px;" class="resizable-cell">&nbsp;</td>`;
            }
            tableHTML += '</tr>';
        }
        
        tableHTML += '</table>';
        tableHTML += '<div class="move-handle" style="position: absolute; top: -8px; left: -8px; width: 16px; height: 16px; background: #007bff; border-radius: 50%; cursor: move; opacity: 0; transition: opacity 0.2s; z-index: 1001; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">↕</div>';
        tableHTML += '</div>';
        
        contentEditor.focus();
        document.execCommand('insertHTML', false, tableHTML);
        
        // 为新插入的表格添加调整大小和移动功能
        setTimeout(() => {
            const newTable = contentEditor.querySelector('table:not(.resize-initialized)');
            if (newTable) {
                initializeTableResize(newTable);
                initializeTableMove(newTable.closest('.table-container'));
            }
        }, 100);
        
        updatePreview();
    }
    
    function insertNestedTable(rows = 2, cols = 2) {
        const rowsPrompt = window.t ? window.t('insert_nested_table_rows') : '请输入嵌套表格行数:';
        const colsPrompt = window.t ? window.t('insert_nested_table_cols') : '请输入嵌套表格列数:';
        const rowsInput = prompt(rowsPrompt, rows);
        const colsInput = prompt(colsPrompt, cols);

        if (!rowsInput || !colsInput) return;

        const rowCount = parseInt(rowsInput);
        const colCount = parseInt(colsInput);

        if (isNaN(rowCount) || isNaN(colCount) || rowCount < 1 || colCount < 1) {
            const validNumbersText = window.t ? window.t('please_enter_valid_numbers') : '请输入有效的行数和列数';
            alert(validNumbersText);
            return;
        }
        
        // 使用默认尺寸：宽度30px，高度20px
        const tableWidth = 30;
        const tableHeight = 20;
        
        // 计算每个单元格的尺寸
        const cellWidth = Math.floor(tableWidth / colCount);
        const cellHeight = Math.floor(tableHeight / rowCount);
        
        let tableHTML = '<div style="position: relative; display: inline-block; margin: 2px;" class="table-container">';
        tableHTML += `<table style="border-collapse: collapse; border: 1px solid #666; table-layout: fixed; width: ${tableWidth}px; height: ${tableHeight}px;" class="resizable-table nested-table">`;
        
        for (let i = 0; i < rowCount; i++) {
            tableHTML += '<tr>';
            for (let j = 0; j < colCount; j++) {
                tableHTML += `<td style="border: 1px solid #666; padding: 2px; text-align: center; vertical-align: middle; min-width: 5px; min-height: 5px; position: relative; width: ${cellWidth}px; height: ${cellHeight}px;" class="resizable-cell">&nbsp;</td>`;
            }
            tableHTML += '</tr>';
        }
        
        tableHTML += '</table>';
        tableHTML += '<div class="move-handle" style="position: absolute; top: -6px; left: -6px; width: 12px; height: 12px; background: #28a745; border-radius: 50%; cursor: move; opacity: 0; transition: opacity 0.2s; z-index: 1001; display: flex; align-items: center; justify-content: center; color: white; font-size: 8px;">↕</div>';
        tableHTML += '</div>';
        
        contentEditor.focus();
        document.execCommand('insertHTML', false, tableHTML);
        
        // 为新插入的嵌套表格添加调整大小和移动功能
        setTimeout(() => {
            const newTable = contentEditor.querySelector('table.nested-table:not(.resize-initialized)');
            if (newTable) {
                initializeTableResize(newTable);
                initializeTableMove(newTable.closest('.table-container'));
            }
        }, 100);
        
        updatePreview();
    }
    
    function initializeNestedTableMove(container) {
        const moveHandle = container.querySelector('.move-handle');
        const table = container.querySelector('table');
        
        // 鼠标悬停显示移动手柄
        container.addEventListener('mouseenter', () => {
            moveHandle.style.opacity = '1';
        });
        
        container.addEventListener('mouseleave', () => {
            moveHandle.style.opacity = '0';
        });
        
        // 移动功能
        moveHandle.addEventListener('mousedown', (e) => {
            e.preventDefault();
            e.stopPropagation();
            startMove(container, e);
        });
    }
    
    function initializeTableMove(container) {
        const moveHandle = container.querySelector('.move-handle');
        const table = container.querySelector('table');
        
        // 鼠标悬停显示移动手柄
        container.addEventListener('mouseenter', () => {
            moveHandle.style.opacity = '1';
        });
        
        container.addEventListener('mouseleave', () => {
            moveHandle.style.opacity = '0';
        });
        
        // 移动功能
        moveHandle.addEventListener('mousedown', (e) => {
            e.preventDefault();
            e.stopPropagation();
            startMove(container, e);
        });
    }
    
    function startMove(container, e) {
        const startX = e.clientX;
        const startY = e.clientY;
        const startLeft = parseInt(container.style.left) || 0;
        const startTop = parseInt(container.style.top) || 0;
        
        function onMouseMove(e) {
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            const newLeft = startLeft + deltaX;
            const newTop = startTop + deltaY;
            
            container.style.left = newLeft + 'px';
            container.style.top = newTop + 'px';
            
            updatePreview();
        }
        
        function onMouseUp() {
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
        }
        
        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
    }
    
    function initializeTableResize(table) {
        if (table.classList.contains('resize-initialized')) return;
        table.classList.add('resize-initialized');
        
        // 为每个单元格添加调整大小的手柄
        const cells = table.querySelectorAll('td, th');
        cells.forEach(cell => {
            // 在添加新句柄之前，先移除任何可能存在的旧句柄，以确保幂等性
            cell.querySelectorAll('.resize-handle, .resize-handle-right, .resize-handle-bottom').forEach(handle => handle.remove());

            // 添加右下角调整手柄
            const resizeHandle = document.createElement('div');
            resizeHandle.className = 'resize-handle';
            resizeHandle.style.cssText = `
                position: absolute;
                bottom: 0;
                right: 0;
                width: 8px;
                height: 8px;
                background: #007bff;
                cursor: se-resize;
                border-radius: 2px;
                opacity: 0;
                transition: opacity 0.2s;
                z-index: 1000;
            `;
            cell.appendChild(resizeHandle);
            
            // 添加右边框调整手柄
            const rightHandle = document.createElement('div');
            rightHandle.className = 'resize-handle-right';
            rightHandle.style.cssText = `
                position: absolute;
                top: 0;
                right: -2px;
                width: 4px;
                height: 100%;
                background: #007bff;
                cursor: col-resize;
                opacity: 0;
                transition: opacity 0.2s;
                z-index: 1000;
            `;
            cell.appendChild(rightHandle);
            
            // 添加下边框调整手柄
            const bottomHandle = document.createElement('div');
            bottomHandle.className = 'resize-handle-bottom';
            bottomHandle.style.cssText = `
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 100%;
                height: 4px;
                background: #007bff;
                cursor: row-resize;
                opacity: 0;
                transition: opacity 0.2s;
                z-index: 1000;
            `;
            cell.appendChild(bottomHandle);
            
            // 鼠标悬停显示调整手柄
            cell.addEventListener('mouseenter', () => {
                resizeHandle.style.opacity = '1';
                rightHandle.style.opacity = '1';
                bottomHandle.style.opacity = '1';
            });
            
            cell.addEventListener('mouseleave', () => {
                resizeHandle.style.opacity = '0';
                rightHandle.style.opacity = '0';
                bottomHandle.style.opacity = '0';
            });
            
            // 右下角调整手柄事件
            resizeHandle.addEventListener('mousedown', (e) => {
                e.preventDefault();
                e.stopPropagation();
                startResize(cell, 'both', e);
            });
            
            // 右边框调整手柄事件
            rightHandle.addEventListener('mousedown', (e) => {
                e.preventDefault();
                e.stopPropagation();
                startResize(cell, 'width', e);
            });
            
            // 下边框调整手柄事件
            bottomHandle.addEventListener('mousedown', (e) => {
                e.preventDefault();
                e.stopPropagation();
                startResize(cell, 'height', e);
            });
            
            // 添加右键菜单
            addContextMenu(cell);
        });
    }
    
    function showTableContextMenu(e, cell) {
        // 移除现有的右键菜单
        const existingMenu = document.querySelector('.table-context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }
        const table = cell.closest('table');
        const menu = document.createElement('div');
        menu.className = 'table-context-menu';
        menu.style.cssText = `
            position: fixed;
            top: ${e.clientY}px;
            left: ${e.clientX}px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            min-width: 180px;
            font-size: 14px;
        `;
        const menuItems = [
            { text: '📏 调整单元格大小', action: () => startResize(cell, 'both', e) },
            { text: '➕ 添加行', action: () => addRowFromCell(cell) },
            { text: '➕ 添加列', action: () => addColumnFromCell(cell) },
            { text: '➖ 删除行', action: () => deleteRowFromCell(cell) },
            { text: '➖ 删除列', action: () => deleteColumnFromCell(cell) },
            { text: '📋 插入嵌套表格', action: () => insertNestedTableFromCell(cell) }
        ];
        // 新增：边框厚度子菜单
        const borderMenu = document.createElement('div');
        borderMenu.style.padding = '8px 12px';
        borderMenu.style.cursor = 'pointer';
        borderMenu.style.position = 'relative';
        borderMenu.textContent = '🖊️ 边框厚度';
        const borderSubMenu = document.createElement('div');
        borderSubMenu.style.position = 'absolute';
        borderSubMenu.style.left = '100%';
        borderSubMenu.style.top = '0';
        borderSubMenu.style.background = 'white';
        borderSubMenu.style.border = '1px solid #ddd';
        borderSubMenu.style.borderRadius = '6px';
        borderSubMenu.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
        borderSubMenu.style.zIndex = '10001';
        borderSubMenu.style.display = 'none';
        borderSubMenu.style.minWidth = '90px';
        for (let i = 1; i <= 5; i++) {
            const subItem = document.createElement('div');
            subItem.textContent = `${i}px`;
            subItem.style.padding = '8px 12px';
            subItem.style.cursor = 'pointer';
            subItem.addEventListener('mouseenter', () => subItem.style.backgroundColor = '#f8f9fa');
            subItem.addEventListener('mouseleave', () => subItem.style.backgroundColor = 'white');
            subItem.addEventListener('click', () => {
                const newWidth = `${i}px`;
                // 应用到整个表格的所有单元格和表格本身
                const allElements = [table, ...table.querySelectorAll('td, th')];
                allElements.forEach(el => {
                    // 重建完整的border属性，以确保样式自包含
                    el.style.border = `${newWidth} solid black`;
                });
                
                menu.remove();
                updatePreview();
            });
            borderSubMenu.appendChild(subItem);
        }
        borderMenu.appendChild(borderSubMenu);
        borderMenu.addEventListener('mouseenter', () => borderSubMenu.style.display = 'block');
        borderMenu.addEventListener('mouseleave', () => borderSubMenu.style.display = 'none');
        menu.appendChild(borderMenu);
        // 其余菜单项
        menuItems.forEach(item => {
            const menuItem = document.createElement('div');
            menuItem.style.cssText = `
                padding: 8px 12px;
                cursor: pointer;
                border-bottom: 1px solid #f0f0f0;
                transition: background-color 0.2s;
            `;
            menuItem.textContent = item.text;
            menuItem.addEventListener('mouseenter', () => {
                menuItem.style.backgroundColor = '#f8f9fa';
            });
            menuItem.addEventListener('mouseleave', () => {
                menuItem.style.backgroundColor = 'white';
            });
            menuItem.addEventListener('click', () => {
                item.action();
                menu.remove();
            });
            menu.appendChild(menuItem);
        });
        // 移除最后一项的边框
        const lastItem = menu.lastElementChild;
        if (lastItem) {
            lastItem.style.borderBottom = 'none';
        }
        document.body.appendChild(menu);
        // 点击其他地方关闭菜单
        const closeMenu = (e) => {
            if (!menu.contains(e.target)) {
                menu.remove();
                document.removeEventListener('click', closeMenu);
            }
        };
        setTimeout(() => {
            document.addEventListener('click', closeMenu);
        }, 100);
    }
    
    // 从指定单元格添加行
    function addRowFromCell(cell) {
        const table = cell.closest('table');
        const currentRow = cell.parentNode;
        const newRow = currentRow.cloneNode(true);
        
        // 清空新行的内容并重新初始化调整大小功能
        newRow.querySelectorAll('td, th').forEach(cell => {
            cell.innerHTML = '&nbsp;';
            // 移除旧的调整手柄
            cell.querySelectorAll('.resize-handle, .resize-handle-right, .resize-handle-bottom').forEach(handle => {
                handle.remove();
            });
        });
        
        currentRow.parentNode.insertBefore(newRow, currentRow.nextSibling);
        
        // 为新行添加调整大小功能和右键菜单
        setTimeout(() => {
            newRow.querySelectorAll('td, th').forEach(cell => {
                addResizeHandles(cell);
                addContextMenu(cell);
            });
        }, 50);
        
        updatePreview();
    }
    
    // 从指定单元格添加列
    function addColumnFromCell(cell) {
        const table = cell.closest('table');
        const rows = table.querySelectorAll('tr');
        
        rows.forEach(row => {
            const newCell = document.createElement('td');
            newCell.style.cssText = 'border: 1px solid #000; padding: 4px; text-align: center; vertical-align: middle; min-width: 20px; min-height: 15px; position: relative; width: 40px;';
            newCell.innerHTML = '&nbsp;';
            row.appendChild(newCell);
            
            // 为新单元格添加调整大小功能和右键菜单
            setTimeout(() => {
                addResizeHandles(newCell);
                addContextMenu(newCell);
            }, 50);
        });
        
        updatePreview();
    }
    
    // 从指定单元格删除行
    function deleteRowFromCell(cell) {
        const currentRow = cell.parentNode;
        const table = cell.closest('table');
        
        if (table.querySelectorAll('tr').length <= 1) {
            alert('表格至少需要保留一行');
            return;
        }
        
        currentRow.remove();
        updatePreview();
    }
    
    // 从指定单元格删除列
    function deleteColumnFromCell(cell) {
        const table = cell.closest('table');
        const rows = table.querySelectorAll('tr');
        const cellIndex = Array.from(cell.parentNode.children).indexOf(cell);
        
        if (rows[0].children.length <= 1) {
            alert('表格至少需要保留一列');
            return;
        }
        
        rows.forEach(row => {
            const cell = row.children[cellIndex];
            if (cell) cell.remove();
        });
        
        updatePreview();
    }
    
    // 从指定单元格插入嵌套表格
    function insertNestedTableFromCell(cell) {
        // 默认宽高
        const tableWidth = 30;
        const tableHeight = 20;
        const rowCount = 2;
        const colCount = 2;
        // 计算每个单元格的尺寸
        const cellWidth = Math.floor(tableWidth / colCount);
        const cellHeight = Math.floor(tableHeight / rowCount);
        let nestedTableHTML = '<div style="position: relative; display: inline-block; margin: 2px;" class="nested-table-container">';
        nestedTableHTML += `<table style="border-collapse: collapse; border: 1px solid #666; margin: 0; table-layout: fixed; width: ${tableWidth}px; height: ${tableHeight}px;" class="resizable-table nested-table">`;
        for (let i = 0; i < rowCount; i++) {
            nestedTableHTML += '<tr>';
            for (let j = 0; j < colCount; j++) {
                nestedTableHTML += `<td style="border: 1px solid #666; padding: 2px; text-align: center; vertical-align: middle; font-size: 0.9em; position: relative; min-width: 5px; min-height: 5px; width: ${cellWidth}px; height: ${cellHeight}px;" class="resizable-cell">&nbsp;</td>`;
            }
            nestedTableHTML += '</tr>';
        }
        nestedTableHTML += '</table>';
        nestedTableHTML += '<div class="move-handle" style="position: absolute; top: -8px; left: -8px; width: 16px; height: 16px; background: #ff6b6b; border-radius: 50%; cursor: move; opacity: 0; transition: opacity 0.2s; z-index: 1001; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px;">↕</div>';
        nestedTableHTML += '</div>';
        cell.innerHTML = nestedTableHTML;
        setTimeout(() => {
            const newTable = cell.querySelector('table:not(.resize-initialized)');
            if (newTable) {
                initializeTableResize(newTable);
                initializeNestedTableMove(cell.querySelector('.nested-table-container'));
            }
        }, 100);
        updatePreview();
    }
    
    function startResize(cell, type, e) {
        const startX = e.clientX;
        const startY = e.clientY;
        const startWidth = cell.offsetWidth;
        const startHeight = cell.offsetHeight;
        
        const table = cell.closest('table');
        const cellIndex = Array.from(cell.parentNode.children).indexOf(cell);
        const rowIndex = Array.from(cell.parentNode.parentNode.children).indexOf(cell.parentNode);
        
        // 检查是否是嵌套表格
        const isNestedTable = table.classList.contains('nested-table');
        
        // 添加调整中的视觉反馈
        cell.classList.add('resizing');
        
        function onMouseMove(e) {
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            if (type === 'width' || type === 'both') {
                // 嵌套表格允许更小的尺寸
                const minWidth = isNestedTable ? 10 : 20;
                const newWidth = Math.max(minWidth, startWidth + deltaX);
                cell.style.width = newWidth + 'px';
                
                // 调整同一列的其他单元格
                const rows = table.querySelectorAll('tr');
                rows.forEach(row => {
                    const sameColCell = row.children[cellIndex];
                    if (sameColCell && sameColCell !== cell) {
                        sameColCell.style.width = newWidth + 'px';
                    }
                });
            }
            
            if (type === 'height' || type === 'both') {
                // 嵌套表格允许更小的尺寸
                const minHeight = isNestedTable ? 8 : 15;
                const newHeight = Math.max(minHeight, startHeight + deltaY);
                cell.style.height = newHeight + 'px';
                
                // 调整同一行的其他单元格
                const row = cell.parentNode;
                const cells = row.children;
                for (let i = 0; i < cells.length; i++) {
                    if (cells[i] !== cell) {
                        cells[i].style.height = newHeight + 'px';
                    }
                }
            }
            
            updatePreview();
        }
        
        function onMouseUp() {
            // 移除调整中的视觉反馈
            cell.classList.remove('resizing');
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
        }
        
        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
    }
    
    function getCurrentTableCell() {
        const selection = window.getSelection();
        if (!selection.rangeCount) return null;
        
        let node = selection.getRangeAt(0).commonAncestorContainer;
        while (node && node.nodeType !== 1) {
            node = node.parentNode;
        }
        
        while (node && node.tagName !== 'TD' && node.tagName !== 'TH') {
            if (node === contentEditor) return null;
            node = node.parentNode;
        }
        
        return node;
    }
    
    function addRow() {
        const currentCell = getCurrentTableCell();
        if (!currentCell) {
            alert('请先点击表格中的某个单元格');
            return;
        }
        
        const table = currentCell.closest('table');
        const currentRow = currentCell.parentNode;
        const newRow = currentRow.cloneNode(true);
        
        // 清空新行的内容并重新初始化调整大小功能
        newRow.querySelectorAll('td, th').forEach(cell => {
            cell.innerHTML = '&nbsp;';
            // 移除旧的调整手柄
            cell.querySelectorAll('.resize-handle, .resize-handle-right, .resize-handle-bottom').forEach(handle => {
                handle.remove();
            });
        });
        
        currentRow.parentNode.insertBefore(newRow, currentRow.nextSibling);
        
        // 为新行添加调整大小功能
        setTimeout(() => {
            newRow.querySelectorAll('td, th').forEach(cell => {
                addResizeHandles(cell);
            });
        }, 50);
        
        updatePreview();
    }
    
    function addColumn() {
        const currentCell = getCurrentTableCell();
        if (!currentCell) {
            alert('请先点击表格中的某个单元格');
            return;
        }
        
        const table = currentCell.closest('table');
        const rows = table.querySelectorAll('tr');
        
        rows.forEach(row => {
            const newCell = document.createElement('td');
            newCell.style.cssText = 'border: 1px solid #000; padding: 4px; text-align: center; vertical-align: middle; min-width: 20px; min-height: 15px; position: relative; width: 40px;';
            newCell.innerHTML = '&nbsp;';
            row.appendChild(newCell);
            
            // 为新单元格添加调整大小功能
            setTimeout(() => {
                addResizeHandles(newCell);
            }, 50);
        });
        
        updatePreview();
    }
    
    function addResizeHandles(cell) {
        // 添加右下角调整手柄
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'resize-handle';
        resizeHandle.style.cssText = `
            position: absolute;
            bottom: 0;
            right: 0;
            width: 8px;
            height: 8px;
            background: #007bff;
            cursor: se-resize;
            border-radius: 2px;
            opacity: 0;
            transition: opacity 0.2s;
            z-index: 1000;
        `;
        cell.appendChild(resizeHandle);
        
        // 添加右边框调整手柄
        const rightHandle = document.createElement('div');
        rightHandle.className = 'resize-handle-right';
        rightHandle.style.cssText = `
            position: absolute;
            top: 0;
            right: -2px;
            width: 4px;
            height: 100%;
            background: #007bff;
            cursor: col-resize;
            opacity: 0;
            transition: opacity 0.2s;
            z-index: 1000;
        `;
        cell.appendChild(rightHandle);
        
        // 添加下边框调整手柄
        const bottomHandle = document.createElement('div');
        bottomHandle.className = 'resize-handle-bottom';
        bottomHandle.style.cssText = `
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 4px;
            background: #007bff;
            cursor: row-resize;
            opacity: 0;
            transition: opacity 0.2s;
            z-index: 1000;
        `;
        cell.appendChild(bottomHandle);
        
        // 鼠标悬停显示调整手柄
        cell.addEventListener('mouseenter', () => {
            resizeHandle.style.opacity = '1';
            rightHandle.style.opacity = '1';
            bottomHandle.style.opacity = '1';
        });
        
        cell.addEventListener('mouseleave', () => {
            resizeHandle.style.opacity = '0';
            rightHandle.style.opacity = '0';
            bottomHandle.style.opacity = '0';
        });
        
        // 右下角调整手柄事件
        resizeHandle.addEventListener('mousedown', (e) => {
            e.preventDefault();
            e.stopPropagation();
            startResize(cell, 'both', e);
        });
        
        // 右边框调整手柄事件
        rightHandle.addEventListener('mousedown', (e) => {
            e.preventDefault();
            e.stopPropagation();
            startResize(cell, 'width', e);
        });
        
        // 下边框调整手柄事件
        bottomHandle.addEventListener('mousedown', (e) => {
            e.preventDefault();
            e.stopPropagation();
            startResize(cell, 'height', e);
        });
    }
    
    function addContextMenu(cell) {
        // 添加右键菜单
        cell.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            showTableContextMenu(e, cell);
        });
    }
    
    function deleteRow() {
        const currentCell = getCurrentTableCell();
        if (!currentCell) {
            alert('请先点击表格中的某个单元格');
            return;
        }
        
        const currentRow = currentCell.parentNode;
        const table = currentCell.closest('table');
        
        if (table.querySelectorAll('tr').length <= 1) {
            alert('表格至少需要保留一行');
            return;
        }
        
        currentRow.remove();
        updatePreview();
    }
    
    function deleteColumn() {
        const currentCell = getCurrentTableCell();
        if (!currentCell) {
            alert('请先点击表格中的某个单元格');
            return;
        }
        
        const table = currentCell.closest('table');
        const rows = table.querySelectorAll('tr');
        const cellIndex = Array.from(currentCell.parentNode.children).indexOf(currentCell);
        
        if (rows[0].children.length <= 1) {
            alert('表格至少需要保留一列');
            return;
        }
        
        rows.forEach(row => {
            const cell = row.children[cellIndex];
            if (cell) cell.remove();
        });
        
        updatePreview();
    }
    
    function execCommand(command, value = null) {
        contentEditor.focus();
        document.execCommand(command, false, value);
        updatePreview();
    }

    function insertTextAtCursor(text) {
        execCommand('insertText', text);
    }
    
    // 兜底：直接用JS包span加style（BIU）
    function applyStyleToSelection(styleProperty, value) {
        const selection = window.getSelection();
        if (!selection.rangeCount) {
            console.log('没有选区');
            return;
        }
        const range = selection.getRangeAt(0);
        if (selection.isCollapsed) {
            console.log('选区已折叠');
            return;
        }
        console.log('应用样式:', styleProperty, '=', value);
        
        // 使用更可靠的方法获取选区内容
        const contents = range.extractContents();
        const span = document.createElement('span');
            span.style[styleProperty] = value;
        span.appendChild(contents);
        range.insertNode(span);
        
        // 清理空的文本节点
        const walker = document.createTreeWalker(span, NodeFilter.SHOW_TEXT, {
            acceptNode: node => node.textContent.trim() === '' ? NodeFilter.FILTER_REJECT : NodeFilter.FILTER_ACCEPT
        });
        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }
        console.log('处理后的文本节点数量:', textNodes.length);
        
        updatePreview();
    }

    // 设置行高
    function setLineHeight(value) {
        if (hasSelection()) {
            applyStyleToSelection('lineHeight', value);
        } else {
            // 如果没有选中文字，应用到整个编辑器
            contentEditor.style.lineHeight = value;
            updatePreview();
        }
    }

    // 兜底：直接用JS对div/p加textAlign（LCR）
    function setTextAlignForSelection(align) {
        console.log('设置文字对齐:', align);
        const selection = window.getSelection();
        if (!selection.rangeCount) {
            console.log('没有选区');
            return;
        }
        const range = selection.getRangeAt(0);
        
        // 获取选区内的所有文本节点
        const textNodes = [];
        const walker = document.createTreeWalker(range.commonAncestorContainer, NodeFilter.SHOW_TEXT, {
            acceptNode: node => {
                if (range.intersectsNode(node)) {
                    return NodeFilter.FILTER_ACCEPT;
                }
                return NodeFilter.FILTER_REJECT;
            }
        });
        
        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }
        
        console.log('找到文本节点数量:', textNodes.length);
        
        if (textNodes.length === 0) {
            // 如果没有文本节点，尝试包装选区
            console.log('没有文本节点，包装选区');
            const wrapper = document.createElement('span');
            try {
                range.surroundContents(wrapper);
                wrapper.style.textAlign = align;
                console.log('成功包装选区并设置对齐:', align);
            } catch (err) {
                console.log('包装选区失败，使用手动插入:', err);
                const frag = range.extractContents();
                wrapper.appendChild(frag);
                wrapper.style.textAlign = align;
                range.insertNode(wrapper);
            }
        } else {
            // 为每个文本节点找到或创建合适的容器
            textNodes.forEach((textNode, index) => {
                let container = textNode.parentElement;
                console.log(`处理文本节点 ${index + 1}:`, textNode.textContent.substring(0, 20));
                
                // 检查容器是否已经有writing-mode设置
                const computedStyle = getComputedStyle(container);
                const isVertical = computedStyle.writingMode === 'vertical-rl' || 
                                 computedStyle.writingMode === 'vertical-lr' ||
                                 container.style.writingMode === 'vertical-rl' ||
                                 container.style.writingMode === 'vertical-lr';
                
                console.log('容器是否为竖排:', isVertical);
                
                if (isVertical) {
                    // 竖排文字的对齐处理
                    if (align === 'left') {
                        // 竖排左对齐 = 顶部对齐
                        container.style.textAlign = 'start';
                        container.style.textOrientation = 'mixed';
                        // 添加容器样式确保对齐效果明显
                        container.style.display = 'inline-block';
                        container.style.verticalAlign = 'top';
                        container.style.lineHeight = '1.2';
                        // 添加临时背景色和边框，让对齐效果更明显
                        container.style.backgroundColor = '#ffeb3b';
                        container.style.border = '1px solid #ff9800';
                        container.style.padding = '4px';
                        console.log('设置竖排顶部对齐');
                        console.log('应用的样式:', {
                            textAlign: container.style.textAlign,
                            textOrientation: container.style.textOrientation,
                            display: container.style.display,
                            verticalAlign: container.style.verticalAlign,
                            lineHeight: container.style.lineHeight
                        });
                        // 3秒后移除临时样式
                        setTimeout(() => {
                            container.style.backgroundColor = '';
                            container.style.border = '';
                            container.style.padding = '';
                        }, 3000);
                    } else if (align === 'center') {
                        // 竖排居中对齐
                        container.style.textAlign = 'center';
                        container.style.textOrientation = 'mixed';
                        // 添加容器样式确保对齐效果明显
                        container.style.display = 'inline-block';
                        container.style.verticalAlign = 'middle';
                        container.style.lineHeight = '1.2';
                        // 添加临时背景色和边框，让对齐效果更明显
                        container.style.backgroundColor = '#4caf50';
                        container.style.border = '1px solid #2e7d32';
                        container.style.padding = '4px';
                        console.log('设置竖排居中对齐');
                        console.log('应用的样式:', {
                            textAlign: container.style.textAlign,
                            textOrientation: container.style.textOrientation,
                            display: container.style.display,
                            verticalAlign: container.style.verticalAlign,
                            lineHeight: container.style.lineHeight
                        });
                        // 3秒后移除临时样式
                        setTimeout(() => {
                            container.style.backgroundColor = '';
                            container.style.border = '';
                            container.style.padding = '';
                        }, 3000);
                    } else if (align === 'right') {
                        // 竖排右对齐 = 底部对齐
                        container.style.textAlign = 'end';
                        container.style.textOrientation = 'mixed';
                        // 添加容器样式确保对齐效果明显
                        container.style.display = 'inline-block';
                        container.style.verticalAlign = 'bottom';
                        container.style.lineHeight = '1.2';
                        // 添加临时背景色和边框，让对齐效果更明显
                        container.style.backgroundColor = '#f44336';
                        container.style.border = '1px solid #c62828';
                        container.style.padding = '4px';
                        console.log('设置竖排底部对齐');
                        console.log('应用的样式:', {
                            textAlign: container.style.textAlign,
                            textOrientation: container.style.textOrientation,
                            display: container.style.display,
                            verticalAlign: container.style.verticalAlign,
                            lineHeight: container.style.lineHeight
                        });
                        // 3秒后移除临时样式
                        setTimeout(() => {
                            container.style.backgroundColor = '';
                            container.style.border = '';
                            container.style.padding = '';
                        }, 3000);
                    }
        } else {
                    // 横排文字的正常对齐
                    container.style.textAlign = align;
                    console.log('设置横排对齐:', align);
                }
            });
        }
        
        updatePreview();
    }

    function hasSelection() {
        const selection = window.getSelection();
        return selection && contentEditor.contains(selection.anchorNode) && selection.rangeCount > 0 && !selection.isCollapsed;
    }
    
    // Use the new async function to populate fonts
    populateFontSelector(fontSelect);

    function getAutoFontSize(html) {
        if (!html.trim() || !previewContent.clientWidth || !previewContent.clientHeight) return '12px';
        const containerWidth = previewContent.clientWidth;
        const containerHeight = previewContent.clientHeight;
        const temp = document.createElement('div');
        Object.assign(temp.style, {
            position: 'absolute', visibility: 'hidden', width: `${containerWidth}px`,
            height: 'auto', wordBreak: 'break-all', whiteSpace: 'pre-wrap',
            padding: '2px', boxSizing: 'border-box',
        });
        temp.innerHTML = html;
        document.body.appendChild(temp);
        let minSize = 6, maxSize = 200, bestSize = minSize;
        while (minSize <= maxSize) {
            const midSize = Math.floor((minSize + maxSize) / 2);
            temp.style.fontSize = `${midSize}px`;
            const isOverflowing = temp.scrollHeight > containerHeight || temp.scrollWidth > containerWidth;
            if (!isOverflowing) {
                bestSize = midSize;
                minSize = midSize + 1;
            } else {
                maxSize = midSize - 1;
            }
        }
        document.body.removeChild(temp);
        return `${Math.max(6, bestSize)}px`;
    }

    function updatePreview() {
        if (!previewContent) return;
        let html = contentEditor.innerHTML;
        const date = new Date();
        const userCode = document.querySelector('.user-info')?.dataset.userCode || '用户代码';
        const replacements = {
            '\\[date\\]': `${String(date.getMonth() + 1).padStart(2, '0')}/${String(date.getDate()).padStart(2, '0')}/${date.getFullYear()}`,
            '\\[week\\]': ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][date.getDay()],
            '\\[user\\]': userCode
        };
        for (const [placeholder, value] of Object.entries(replacements)) {
            html = html.replace(new RegExp(placeholder, 'g'), value);
        }
        previewContent.innerHTML = html;
        const vAlignMap = { top: 'flex-start', center: 'center', bottom: 'flex-end' };
        const previewBox = previewContent.parentElement;
        const vAlign = styleState.vAlign;
        if (previewBox) {
            previewBox.style.justifyContent = vAlignMap[vAlign] || 'center';
        }
        if (styleState.fontSize === 'auto') {
            previewContent.style.fontSize = getAutoFontSize(html);
        } else {
            previewContent.style.fontSize = ''; 
        }
    }
    
    // Event Listeners
    contentEditor.addEventListener('input', updatePreview);
    if (fontSelect) fontSelect.addEventListener('change', (e) => applyStyleToSelection('fontFamily', e.target.value));
    if (sizeSelect) sizeSelect.addEventListener('change', (e) => {
        const size = e.target.value;
        if (hasSelection()) {
            applyStyleToSelection('fontSize', size === 'auto' ? '' : size);
        } else {
            styleState.fontSize = size;
            updatePreview();
        }
    });
    if (letterSpacingSelect) letterSpacingSelect.addEventListener('change', (e) => applyStyleToSelection('letterSpacing', e.target.value));
    if (lineHeightSelect) lineHeightSelect.addEventListener('change', (e) => setLineHeight(e.target.value));
    
    // 文字排行功能
    const writingModeSelect = toolbar.querySelector('.writing-mode-select');
    if (writingModeSelect) {
        console.log('找到文字排行下拉框');
        writingModeSelect.addEventListener('change', async (e) => {
            console.log('文字排行下拉框值改变:', e.target.value);
            if (!hasSelection()) {
                console.log('没有选中文字');
                showMessage('请先选中要设置的文字', 'error');
                writingModeSelect.value = 'horizontal';
                return;
            }
            const value = e.target.value;
            console.log('处理文字排行:', value);
            
            // 获取选区
            const selection = window.getSelection();
            const range = selection.getRangeAt(0);
            
            if (value === 'horizontal') {
                // 横排：移除所有相关样式
                console.log('设置为横排');
                const span = document.createElement('span');
                span.style.writingMode = 'horizontal-tb';
                span.style.transform = 'none';
                span.style.textOrientation = 'mixed';
                span.style.textAlign = 'left'; // 默认左对齐
                const contents = range.extractContents();
                span.appendChild(contents);
                range.insertNode(span);
            } else if (value === 'vertical') {
                // 竖排
                console.log('设置为竖排');
                const span = document.createElement('span');
                span.style.writingMode = 'vertical-rl';
                span.style.transform = 'none';
                span.style.textOrientation = 'mixed';
                span.style.textAlign = 'start'; // 竖排默认顶部对齐
                span.style.display = 'inline-block'; // 确保竖排效果
                span.style.lineHeight = '1.2'; // 设置行高
                span.style.verticalAlign = 'top'; // 默认顶部对齐
                span.style.padding = '2px'; // 添加一些内边距
                const contents = range.extractContents();
                span.appendChild(contents);
                range.insertNode(span);
            } else if (value === 'rotate') {
                let angle = prompt('请输入旋转角度（单位：度，例如90）：', '90');
                if (!angle || isNaN(angle)) {
                    showMessage('请输入有效的角度数字', 'error');
                    writingModeSelect.value = 'horizontal';
                    return;
                }
                angle = parseInt(angle);
                console.log('设置旋转角度:', angle);
                // 旋转，保持横排
                const span = document.createElement('span');
                span.style.writingMode = 'horizontal-tb';
                span.style.transform = `rotate(${angle}deg)`;
                span.style.display = 'inline-block'; // 确保旋转生效
                span.style.textOrientation = 'mixed';
                span.style.textAlign = 'left'; // 默认左对齐
                const contents = range.extractContents();
                span.appendChild(contents);
                range.insertNode(span);
            }
            
            // 选完后重置下拉框为横排，避免误操作
            setTimeout(()=>{writingModeSelect.value = 'horizontal';}, 200);
            updatePreview();
        });
    } else {
        console.log('未找到文字排行下拉框');
    }
    
    if (boldBtn) boldBtn.addEventListener('click', () => execCommand('bold'));
    if (italicBtn) italicBtn.addEventListener('click', () => execCommand('italic'));
    if (underlineBtn) underlineBtn.addEventListener('click', () => execCommand('underline'));
    if (insertDateBtn) insertDateBtn.addEventListener('click', () => insertTextAtCursor('[date]'));
    if (insertDayBtn) insertDayBtn.addEventListener('click', () => insertTextAtCursor('[week]'));
    if (insertUserBtn) insertUserBtn.addEventListener('click', () => insertTextAtCursor('[user]'));
    if (insertTableBtn) insertTableBtn.addEventListener('click', () => insertTable());
    if (insertNestedTableBtn) insertNestedTableBtn.addEventListener('click', () => insertNestedTable());
    if (addRowBtn) addRowBtn.addEventListener('click', addRow);
    if (addColBtn) addColBtn.addEventListener('click', addColumn);
    if (deleteRowBtn) deleteRowBtn.addEventListener('click', deleteRow);
    if (deleteColBtn) deleteColBtn.addEventListener('click', deleteColumn);
    
    toolbar.addEventListener('click', (e) => {
        const button = e.target.closest('button');
        if (!button) return;
        contentEditor.focus();
        // BIU
        if (button.classList.contains('bold-btn')) {
            applyStyleToSelection('fontWeight', 'bold');
        }
        if (button.classList.contains('italic-btn')) {
            applyStyleToSelection('fontStyle', 'italic');
        }
        if (button.classList.contains('underline-btn')) {
            applyStyleToSelection('textDecoration', 'underline');
        }
        // LCR
        if (button.classList.contains('h-align-btn')) {
            const align = button.dataset.hAlign;
            setTextAlignForSelection(
                align === 'justifyLeft' ? 'left' : align === 'justifyCenter' ? 'center' : 'right'
            );
        }
        // TMB
        if (button.classList.contains('v-align-btn')) {
            toolbar.querySelectorAll('.v-align-btn').forEach(b => b.classList.remove('active'));
            button.classList.add('active');
            styleState.vAlign = button.dataset.vAlign;
            updatePreview();
            if (onVAlignChange) {
                onVAlignChange(styleState.vAlign);
            }
        }
    });
    
    updatePreview();

    // 编辑区输入回车时自动包裹div段落
    contentEditor.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            // 阻止默认换行
            e.preventDefault();
            // 插入一个新的div段落
            const selection = window.getSelection();
            if (!selection.rangeCount) return;
            const range = selection.getRangeAt(0);
            // 创建新div
            const newDiv = document.createElement('div');
            newDiv.innerHTML = '<br>';
            // 插入新div
            range.collapse(false);
            range.insertNode(newDiv);
            // 将光标移到新div内
            range.setStart(newDiv, 0);
            range.setEnd(newDiv, 0);
            selection.removeAllRanges();
            selection.addRange(range);
        }
    });

    function initializeInteractiveElements(editor) {
        if (!editor) return;

        // 1. 初始化表格缩放、右键菜单
        const tables = editor.querySelectorAll('table:not(.resize-initialized)');
        tables.forEach(table => {
            // 确保 initializeTableResize 在作用域内
            if (typeof initializeTableResize === 'function') {
                initializeTableResize(table);
            }
        });

        // 2. 初始化表格移动
        const containers = editor.querySelectorAll('.table-container:not(.move-initialized), .nested-table-container:not(.move-initialized)');
        containers.forEach(container => {
            container.classList.add('move-initialized');
            if (container.classList.contains('nested-table-container')) {
                 if (typeof initializeNestedTableMove === 'function') {
                    initializeNestedTableMove(container);
                }
            } else {
                if (typeof initializeTableMove === 'function') {
                    initializeTableMove(container);
                }
            }
        });
    }
    
    function initializeTableResize(table) {
        if (table.classList.contains('resize-initialized')) return;
        table.classList.add('resize-initialized');
        
        // 为每个单元格添加调整大小的手柄
        const cells = table.querySelectorAll('td, th');
        cells.forEach(cell => {
            // 在添加新句柄之前，先移除任何可能存在的旧句柄，以确保幂等性
            cell.querySelectorAll('.resize-handle, .resize-handle-right, .resize-handle-bottom').forEach(handle => handle.remove());

            // 添加右下角调整手柄
            const resizeHandle = document.createElement('div');
            resizeHandle.className = 'resize-handle';
            resizeHandle.style.cssText = `
                position: absolute;
                bottom: 0;
                right: 0;
                width: 8px;
                height: 8px;
                background: #007bff;
                cursor: se-resize;
                border-radius: 2px;
                opacity: 0;
                transition: opacity 0.2s;
                z-index: 1000;
            `;
            cell.appendChild(resizeHandle);
            
            // 添加右边框调整手柄
            const rightHandle = document.createElement('div');
            rightHandle.className = 'resize-handle-right';
            rightHandle.style.cssText = `
                position: absolute;
                top: 0;
                right: -2px;
                width: 4px;
                height: 100%;
                background: #007bff;
                cursor: col-resize;
                opacity: 0;
                transition: opacity 0.2s;
                z-index: 1000;
            `;
            cell.appendChild(rightHandle);
            
            // 添加下边框调整手柄
            const bottomHandle = document.createElement('div');
            bottomHandle.className = 'resize-handle-bottom';
            bottomHandle.style.cssText = `
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 100%;
                height: 4px;
                background: #007bff;
                cursor: row-resize;
                opacity: 0;
                transition: opacity 0.2s;
                z-index: 1000;
            `;
            cell.appendChild(bottomHandle);
            
            // 鼠标悬停显示调整手柄
            cell.addEventListener('mouseenter', () => {
                resizeHandle.style.opacity = '1';
                rightHandle.style.opacity = '1';
                bottomHandle.style.opacity = '1';
            });
            
            cell.addEventListener('mouseleave', () => {
                resizeHandle.style.opacity = '0';
                rightHandle.style.opacity = '0';
                bottomHandle.style.opacity = '0';
            });
            
            // 右下角调整手柄事件
            resizeHandle.addEventListener('mousedown', (e) => {
                e.preventDefault();
                e.stopPropagation();
                startResize(cell, 'both', e);
            });
            
            // 右边框调整手柄事件
            rightHandle.addEventListener('mousedown', (e) => {
                e.preventDefault();
                e.stopPropagation();
                startResize(cell, 'width', e);
            });
            
            // 下边框调整手柄事件
            bottomHandle.addEventListener('mousedown', (e) => {
                e.preventDefault();
                e.stopPropagation();
                startResize(cell, 'height', e);
            });
            
            // 添加右键菜单
            addContextMenu(cell);
        });
    }
}

function showAddLabelModal() {
    const modalContent = `
        <div class="form-group">
                <label for="label-type">标签类型:</label>
                <select id="label-type">
                    <option value="普通">普通</option>
                    <option value="高级">高级</option>
                    <option value="特殊">特殊</option>
                    <option value="全局">全局</option>
                </select>
        </div>
        <div class="form-group" style="display: flex; gap: 10px;">
                <div style="flex: 1;">
                <label for="label-name">标签名称:</label>
                <input type="text" id="label-name" placeholder="输入标签名称">
                </div>
                <div style="flex: 1;">
                    <label for="copy-info">复制信息:</label>
                    <input type="text" id="copy-info" placeholder="输入复制信息">
                </div>
        </div>
        <div class="form-group" style="display: flex; gap: 10px;">
                <div style="flex: 1;">
                    <label for="print-quantity">打印数量:</label>
                    <input type="text" id="print-quantity" placeholder="打印数量" value="1">
                </div>
                <div style="flex: 1;">
                    <label for="label-code-shortcut">标签代码简写:</label>
                    <input type="text" id="label-code-shortcut" placeholder="输入标签简称">
                </div>
            </div>
            <div class="form-group editor-wrapper">
                <div class="editor-main">
                    <label>标签内容:</label>
                    ${getEditorToolbarHTML()}
                    <div id="label-content-add" class="rich-text-editor" contenteditable="true" placeholder="输入标签内容"></div>
                </div>
                <div class="label-preview-container">
                    <h4>3×2in 标签预览</h4>
                    <div class="label-preview"><div id="label-preview-content-add"></div></div>
                </div>
        </div>
        <div class="form-group">
                <label>所属部门:</label>
                <div id="department-selector-add" class="department-selector"></div>
            </div>
            <div class="form-group checkbox-group" id="should-remind-group">
                <label class="custom-checkbox">
                    <input type="checkbox" id="should-remind">
                    <span>打印数量提醒</span>
                </label>
                <label class="custom-checkbox">
                    <input type="checkbox" id="usage-question-enabled">
                    <span>使用问题</span>
                </label>
                <label class="custom-checkbox">
                    <input type="checkbox" id="is-hidden">
                    <span>隐藏标签</span>
                </label>
            </div>
            <div class="form-group" id="usage-question-title-group" style="display: none;">
                <label for="usage-question-title">问题标题:</label>
                <input type="text" id="usage-question-title" placeholder="输入问题标题">
            </div>
    `;
    const modal = createModal('添加新标签', modalContent, 'modal-add-label', () => addLabel());

    const toolbarId = modal.querySelector('.text-editor-toolbar').id;

    // 添加使用问题复选框的事件监听器
    const usageQuestionCheckbox = modal.querySelector('#usage-question-enabled');
    const usageQuestionTitleGroup = modal.querySelector('#usage-question-title-group');

    if (usageQuestionCheckbox && usageQuestionTitleGroup) {
        usageQuestionCheckbox.addEventListener('change', function() {
            usageQuestionTitleGroup.style.display = this.checked ? 'block' : 'none';
        });
    }
    
    setupRichTextEditor({
        editorSelector: '#label-content-add',
        previewSelector: '#label-preview-content-add',
        toolbarSelector: `#${toolbarId}`,
        initialVAlign: 'center',
        onVAlignChange: (v) => { window.currentAddLabelVAlign = v; }
    });

    setupDepartmentSelector('department-selector-add');

    // 设置打印数量提醒的逻辑
    const shouldRemindCheckbox = modal.querySelector('#should-remind');
    const printQuantityInput = modal.querySelector('#print-quantity');

    // 切换打印数量输入框的启用状态
    const togglePrintQuantityInput = () => {
        if (shouldRemindCheckbox.checked) {
            // 勾选了打印数量提醒，禁用打印数量输入框
            printQuantityInput.disabled = true;
            printQuantityInput.value = '';
            printQuantityInput.placeholder = '由用户决定数量';
        } else {
            // 未勾选打印数量提醒，启用打印数量输入框
            printQuantityInput.disabled = false;
            printQuantityInput.value = '1';
            printQuantityInput.placeholder = '打印数量';
        }
    };

    // 初始状态设置
    togglePrintQuantityInput();

    // 监听复选框变化
    shouldRemindCheckbox.addEventListener('change', togglePrintQuantityInput);

    // Toggle placeholder buttons based on label type
    const labelTypeSelect = modal.querySelector('#label-type');
    const shouldRemindGroup = modal.querySelector('#should-remind-group');

    const toggleAdvancedFeatures = () => {
        const isAdvanced = labelTypeSelect.value === '高级' || labelTypeSelect.value === '特殊' || labelTypeSelect.value === '全局';
        
        // 获取工具栏中的高级功能按钮
        const toolbar = modal.querySelector('.text-editor-toolbar');
        if (toolbar) {
            const insertTableBtn = toolbar.querySelector('.insert-table-btn');
            const insertNestedTableBtn = toolbar.querySelector('.insert-nested-table-btn');
            const insertDateBtn = toolbar.querySelector('.insert-date-btn');
            const insertDayBtn = toolbar.querySelector('.insert-day-btn');
            const insertUserBtn = toolbar.querySelector('.insert-user-btn');
            
            // 显示/隐藏高级功能按钮
            if (insertTableBtn) insertTableBtn.style.display = isAdvanced ? 'inline-block' : 'none';
            if (insertNestedTableBtn) insertNestedTableBtn.style.display = isAdvanced ? 'inline-block' : 'none';
            if (insertDateBtn) insertDateBtn.style.display = isAdvanced ? 'inline-block' : 'none';
            if (insertDayBtn) insertDayBtn.style.display = isAdvanced ? 'inline-block' : 'none';
            if (insertUserBtn) insertUserBtn.style.display = isAdvanced ? 'inline-block' : 'none';
        }
        
        if (shouldRemindGroup) {
            shouldRemindGroup.style.display = (labelTypeSelect.value === '特殊' || labelTypeSelect.value === '全局') ? 'block' : 'none';
        }
    };
    
    if (labelTypeSelect) {
        labelTypeSelect.addEventListener('change', toggleAdvancedFeatures);
        toggleAdvancedFeatures(); // Initial check
    }
}

function addLabel() {
    const nameInput = document.getElementById('label-name');
    const typeSelect = document.getElementById('label-type');
    const contentEditor = document.getElementById('label-content-add');
    const remindCheckbox = document.getElementById('should-remind');
    const copyInfoInput = document.getElementById('copy-info');
    const printQuantityInput = document.getElementById('print-quantity');
    const labelCodeShortcutInput = document.getElementById('label-code-shortcut');
    const usageQuestionEnabledCheckbox = document.getElementById('usage-question-enabled');
    const usageQuestionTitleInput = document.getElementById('usage-question-title');
    const isHiddenCheckbox = document.getElementById('is-hidden');

    if (!nameInput || !typeSelect || !contentEditor || !remindCheckbox || !copyInfoInput || !printQuantityInput || !labelCodeShortcutInput || !usageQuestionEnabledCheckbox || !usageQuestionTitleInput) {
        console.error('One or more form elements could not be found for adding a label.');
        showMessage('表单元素缺失，无法添加。', 'error');
        return;
    }
    
    const name = nameInput.value.trim();
    const type = typeSelect.value;
    const content = contentEditor.innerHTML.trim();
    const departmentNames = getSelectedDepartments('department-selector-add');
    const print_prompt = remindCheckbox.checked;
    const copy_info = copyInfoInput.value.trim();
    // 如果勾选了打印数量提醒，则使用默认值1，否则使用用户输入的值
    const print_quantity = print_prompt ? 1 : (parseInt(printQuantityInput.value) || 1);
    const label_code_shortcut = labelCodeShortcutInput.value.trim();
    const usage_question_enabled = usageQuestionEnabledCheckbox.checked;
    const usage_question_title = usageQuestionTitleInput.value.trim();
    const is_hidden = isHiddenCheckbox.checked;

    // 获取当前编辑器的vAlign
    const vAlign = window.currentAddLabelVAlign || 'center';

    if (!name || !content) {
        showMessage('标签名称和内容不能为空。', 'error');
        return;
    }

    // 如果启用了使用问题功能，必须填写问题标题
    if (usage_question_enabled && !usage_question_title) {
        showMessage('启用使用问题功能时，必须填写问题标题。', 'error');
        return;
    }

    const labelData = {
        name,
        type,
        content,
        department_names: departmentNames,
        should_remind: print_prompt,
        v_align: vAlign,
        copy_info,
        print_quantity,
        label_code_shortcut,
        usage_question_enabled,
        usage_question_title,
        is_hidden
    };
    
    fetch('/api/labels', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(labelData),
    })
    .then(response => response.json())
    .then(data => {
        if (data.id) {
            const addModal = document.getElementById('modal-add-label');
            if (addModal) {
                closeModal(addModal);
            }
            showMessage('标签添加成功！', 'success');
            loadLabels();
        } else {
             showMessage(data.error || '添加标签失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error adding label:', error);
        showMessage(`添加失败: ${error.message}`, 'error');
    });
}

function getSelectedDepartments(containerId) {
    const container = document.getElementById(containerId);
    if (!container) {
        console.error(`Container with ID ${containerId} not found`);
        return '';
    }
    
    const selectedDiv = container.querySelector('.selected-departments');
    if (!selectedDiv) {
        console.error(`Selected departments div not found in container ${containerId}`);
        return '';
    }
    
    const departmentTags = selectedDiv.querySelectorAll('.department-tag');
    const departments = Array.from(departmentTags).map(tag => tag.textContent.replace('×', '').trim());
    return departments.join(', ');
}

function setupDepartmentSelector(containerId, initialDepartments = '') {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    // 清空容器
    container.innerHTML = '';
    
    // 创建已选部门显示区域
    const selectedDiv = document.createElement('div');
    selectedDiv.className = 'selected-departments';
    container.appendChild(selectedDiv);
    
    // 创建输入框
    const input = document.createElement('input');
    input.type = 'text';
    input.placeholder = '输入部门名称或从下拉列表选择';
    container.appendChild(input);
    
    // 创建下拉列表
    const dropdown = document.createElement('div');
    dropdown.className = 'department-dropdown';
    dropdown.style.display = 'none';
    container.appendChild(dropdown);
    
    // 定义addDepartmentTag函数（移到前面）
    function addDepartmentTag(deptName) {
        // 检查是否已存在
        const existing = selectedDiv.querySelector(`[data-name="${deptName}"]`);
        if (existing) return;
        
        const tag = document.createElement('div');
        tag.className = 'department-tag';
        tag.setAttribute('data-name', deptName);
        tag.innerHTML = `
            ${deptName}
            <button class="remove-btn" onclick="removeDepartmentTag(this)">×</button>
        `;
        selectedDiv.appendChild(tag);
    }
    
    // 加载部门列表
    fetch('/api/departments')
        .then(response => response.json())
        .then(departments => {
            dropdown.innerHTML = departments.map(dept => 
                `<div class="dropdown-item" data-name="${dept.name}">${dept.name}</div>`
            ).join('');
            
            // 在部门列表加载完成后初始化已选部门
            if (initialDepartments) {
                const deptArray = initialDepartments.split(',').map(d => d.trim()).filter(d => d);
                deptArray.forEach(dept => addDepartmentTag(dept));
            }
    })
    .catch(error => {
            console.error('Error loading departments:', error);
            dropdown.innerHTML = '<div class="dropdown-item">加载失败</div>';
        });
    
    // 输入框事件
    input.addEventListener('input', function() {
        const value = this.value.trim();
        if (value) {
            dropdown.style.display = 'block';
            const items = dropdown.querySelectorAll('.dropdown-item');
            items.forEach(item => {
                if (item.textContent.toLowerCase().includes(value.toLowerCase())) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        } else {
            dropdown.style.display = 'block';
            const items = dropdown.querySelectorAll('.dropdown-item');
            items.forEach(item => {
                item.style.display = 'block';
            });
        }
    });
    
    // 输入框获得焦点时显示下拉列表
    input.addEventListener('focus', function() {
        dropdown.style.display = 'block';
    });
    
    // 输入框失去焦点时隐藏下拉列表
    input.addEventListener('blur', function() {
        setTimeout(() => {
            dropdown.style.display = 'none';
        }, 200);
    });
    
    // 下拉列表点击事件
    dropdown.addEventListener('click', function(e) {
        if (e.target.classList.contains('dropdown-item')) {
            const deptName = e.target.getAttribute('data-name');
            if (deptName && deptName !== '加载失败') {
                addDepartmentTag(deptName);
                input.value = '';
                dropdown.style.display = 'none';
            }
        }
    });
    
    // 输入框回车事件
    input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            const value = this.value.trim();
            if (value) {
                addDepartmentTag(value);
                this.value = '';
                dropdown.style.display = 'none';
            }
        }
    });
}

function removeDepartmentTag(button) {
    button.parentElement.remove();
}

function editLabel(id) {
    fetch(`/api/labels/${id}`)
        .then(response => {
            if (!response.ok) throw new Error('Network response was not ok.');
            return response.json();
        })
        .then(label => {
            if (!label || label.error) {
                showMessage(label.error || '未找到该标签，可能已被删除。', 'error');
                return;
            }
            const modalId = `modal-edit-${id}`;
            const editorId = `label-content-edit-${id}`;
            const previewId = `label-preview-content-edit-${id}`;
            const toolbarId = `text-editor-toolbar-edit-${id}`;
            const modalContent = `
                <div class="form-group"><label for="label-type-edit-${id}">标签类型:</label><select id="label-type-edit-${id}"><option value="普通">普通</option><option value="高级">高级</option><option value="特殊">特殊</option><option value="全局">全局</option></select></div>
                <div class="form-group" style="display: flex; gap: 10px;">
                    <div style="flex: 1;"><label for="label-name-edit-${id}">标签名称:</label><input type="text" id="label-name-edit-${id}" value="${escapeHTML(label.name)}"></div>
                    <div style="flex: 1;"><label for="copy-info-edit-${id}">复制信息:</label><input type="text" id="copy-info-edit-${id}" value="${escapeHTML(label.copy_info || '')}" placeholder="输入复制信息"></div>
                </div>
                <div class="form-group" style="display: flex; gap: 10px;">
                    <div style="flex: 1;"><label for="print-quantity-edit-${id}">打印数量:</label><input type="text" id="print-quantity-edit-${id}" value="${label.print_quantity || 1}"></div>
                    <div style="flex: 1;"><label for="label-code-shortcut-edit-${id}">标签代码简写:</label><input type="text" id="label-code-shortcut-edit-${id}" value="${escapeHTML(label.label_code_shortcut || '')}" placeholder="输入标签简称"></div>
                </div>
                <div class="form-group editor-wrapper">
                    <div class="editor-main"><label>标签内容:</label>${getEditorToolbarHTML(toolbarId)}<div id="${editorId}" class="rich-text-editor" contenteditable="true"></div></div>
                    <div class="label-preview-container"><h4 style="margin-bottom: 5px;">3×2in 标签预览</h4><div class="label-preview"><div id="${previewId}"></div></div></div>
                </div>
                <div class="form-group"><label>所属部门:</label><div id="department-selector-edit-${id}" class="department-selector"></div></div>
                <div class="form-group checkbox-group">
                    <label class="custom-checkbox">
                        <input type="checkbox" id="should-remind-edit-${id}" ${label.print_prompt ? 'checked' : ''}>
                        <span>打印数量提醒</span>
                    </label>
                    <label class="custom-checkbox">
                        <input type="checkbox" id="usage-question-enabled-edit-${id}" ${label.usage_question_enabled ? 'checked' : ''}>
                        <span>使用问题</span>
                    </label>
                    <label class="custom-checkbox">
                        <input type="checkbox" id="is-hidden-edit-${id}" ${label.is_hidden ? 'checked' : ''}>
                        <span>隐藏标签</span>
                    </label>
                </div>
                <div class="form-group" id="usage-question-title-group-edit-${id}" style="display: ${label.usage_question_enabled ? 'block' : 'none'};">
                    <label for="usage-question-title-edit-${id}">问题标题:</label>
                    <input type="text" id="usage-question-title-edit-${id}" value="${escapeHTML(label.usage_question_title || '')}" placeholder="输入问题标题">
                </div>
            `;
            const modal = createModal('编辑标签', modalContent, modalId, () => updateLabel(id));
            document.getElementById(`label-type-edit-${id}`).value = label.type;

            let content = label.content || '';
            // 清理掉会阻止重新初始化的class，以便表格功能可以重新加载
            content = content.replace(/\s?resize-initialized/g, '');
            content = content.replace(/\s?move-initialized/g, '');

            const toolbar = document.getElementById(toolbarId);
            const preview = document.getElementById(previewId);
            const contentEditor = document.getElementById(editorId);
            
            if (!contentEditor || !toolbar || !preview) {
                showMessage('编辑器关键元素未找到，初始化失败。', 'error');
                return;
            }

            // 让 setupRichTextEditor 统一处理内容的注入和初始化
            setupRichTextEditor({
                editorSelector: `#${editorId}`,
                previewSelector: `#${previewId}`,
                toolbarSelector: `#${toolbarId}`,
                initialContent: content, // 传递清洗后的内容
                initialVAlign: label.v_align || 'center',
                onVAlignChange: (v) => { window[`currentEditLabelVAlign_${id}`] = v; }
            });
            
            setupDepartmentSelector(`department-selector-edit-${id}`, label.department_names);

            // 添加使用问题复选框的事件监听器
            const usageQuestionCheckboxEdit = document.getElementById(`usage-question-enabled-edit-${id}`);
            const usageQuestionTitleGroupEdit = document.getElementById(`usage-question-title-group-edit-${id}`);

            if (usageQuestionCheckboxEdit && usageQuestionTitleGroupEdit) {
                usageQuestionCheckboxEdit.addEventListener('change', function() {
                    usageQuestionTitleGroupEdit.style.display = this.checked ? 'block' : 'none';
                });
            }

            // 设置打印数量提醒的逻辑
            const shouldRemindCheckbox = modal.querySelector(`#should-remind-edit-${id}`);
            const printQuantityInput = modal.querySelector(`#print-quantity-edit-${id}`);

            // 切换打印数量输入框的启用状态
            const togglePrintQuantityInput = () => {
                if (shouldRemindCheckbox.checked) {
                    // 勾选了打印数量提醒，禁用打印数量输入框
                    printQuantityInput.disabled = true;
                    printQuantityInput.value = '';
                    printQuantityInput.placeholder = '由用户决定数量';
                } else {
                    // 未勾选打印数量提醒，启用打印数量输入框
                    printQuantityInput.disabled = false;
                    if (!printQuantityInput.value) {
                        printQuantityInput.value = label.print_quantity || 1;
                    }
                    printQuantityInput.placeholder = '打印数量';
                }
            };

            // 初始状态设置
            togglePrintQuantityInput();

            // 监听复选框变化
            shouldRemindCheckbox.addEventListener('change', togglePrintQuantityInput);

            // 根据标签类型显示/隐藏高级功能
            const labelTypeSelect = document.getElementById(`label-type-edit-${id}`);
            const shouldRemindGroup = modal.querySelector('.checkbox-group');
            
            const toggleAdvancedFeatures = () => {
                const isAdvanced = labelTypeSelect.value === '高级' || labelTypeSelect.value === '特殊' || labelTypeSelect.value === '全局';
                
                // 获取工具栏中的高级功能按钮
                const toolbar = document.getElementById(toolbarId);
                if (toolbar) {
                    const insertTableBtn = toolbar.querySelector('.insert-table-btn');
                    const insertNestedTableBtn = toolbar.querySelector('.insert-nested-table-btn');
                    const insertDateBtn = toolbar.querySelector('.insert-date-btn');
                    const insertDayBtn = toolbar.querySelector('.insert-day-btn');
                    const insertUserBtn = toolbar.querySelector('.insert-user-btn');
                    
                    // 显示/隐藏高级功能按钮
                    if (insertTableBtn) insertTableBtn.style.display = isAdvanced ? 'inline-block' : 'none';
                    if (insertNestedTableBtn) insertNestedTableBtn.style.display = isAdvanced ? 'inline-block' : 'none';
                    if (insertDateBtn) insertDateBtn.style.display = isAdvanced ? 'inline-block' : 'none';
                    if (insertDayBtn) insertDayBtn.style.display = isAdvanced ? 'inline-block' : 'none';
                    if (insertUserBtn) insertUserBtn.style.display = isAdvanced ? 'inline-block' : 'none';
                }
                
                if (shouldRemindGroup) {
                    shouldRemindGroup.style.display = (labelTypeSelect.value === '特殊' || labelTypeSelect.value === '全局') ? 'block' : 'none';
                }
            };
            
            if (labelTypeSelect) {
                labelTypeSelect.addEventListener('change', toggleAdvancedFeatures);
                toggleAdvancedFeatures(); // Initial check
            }
        })
        .catch(error => {
            console.error('Error fetching label for edit:', error);
            showMessage('加载标签数据失败，请检查网络或联系管理员。', 'error');
        });
}

function updateLabel(id) {
    const nameInput = document.getElementById(`label-name-edit-${id}`);
    const typeSelect = document.getElementById(`label-type-edit-${id}`);
    const contentEditor = document.getElementById(`label-content-edit-${id}`);
    const remindCheckbox = document.getElementById(`should-remind-edit-${id}`);
    const copyInfoInput = document.getElementById(`copy-info-edit-${id}`);
    const printQuantityInput = document.getElementById(`print-quantity-edit-${id}`);
    const labelCodeShortcutInput = document.getElementById(`label-code-shortcut-edit-${id}`);
    const usageQuestionEnabledCheckbox = document.getElementById(`usage-question-enabled-edit-${id}`);
    const usageQuestionTitleInput = document.getElementById(`usage-question-title-edit-${id}`);
    const isHiddenCheckbox = document.getElementById(`is-hidden-edit-${id}`);

    if (!nameInput || !typeSelect || !contentEditor || !remindCheckbox || !copyInfoInput || !printQuantityInput || !labelCodeShortcutInput || !usageQuestionEnabledCheckbox || !usageQuestionTitleInput || !isHiddenCheckbox) {
        console.error('One or more form elements could not be found for updating a label.');
        showMessage('表单元素缺失，无法更新。', 'error');
        return;
    }
    
    const name = nameInput.value.trim();
    const type = typeSelect.value;
    const content = contentEditor.innerHTML.trim();
    const departmentNames = getSelectedDepartments(`department-selector-edit-${id}`);
    const print_prompt = remindCheckbox.checked;
    const copy_info = copyInfoInput.value.trim();
    // 如果勾选了打印数量提醒，则使用默认值1，否则使用用户输入的值
    const print_quantity = print_prompt ? 1 : (parseInt(printQuantityInput.value) || 1);
    const label_code_shortcut = labelCodeShortcutInput.value.trim();
    const usage_question_enabled = usageQuestionEnabledCheckbox.checked;
    const usage_question_title = usageQuestionTitleInput.value.trim();
    const is_hidden = isHiddenCheckbox.checked;

    // 获取当前编辑器的vAlign
    const vAlign = window[`currentEditLabelVAlign_${id}`] || 'center';

    if (!name || !content) {
        showMessage('标签名称和内容不能为空。', 'error');
        return;
    }

    // 如果启用了使用问题功能，必须填写问题标题
    if (usage_question_enabled && !usage_question_title) {
        showMessage('启用使用问题功能时，必须填写问题标题。', 'error');
        return;
    }

    const labelData = {
        name,
        type,
        content,
        department_names: departmentNames,
        should_remind: print_prompt,
        v_align: vAlign,
        copy_info,
        print_quantity,
        label_code_shortcut,
        usage_question_enabled,
        usage_question_title,
        is_hidden
    };
    
    fetch(`/api/labels/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(labelData),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const openModal = document.getElementById(`modal-edit-${id}`);
            if (openModal) {
                closeModal(openModal);
            }
            showMessage('标签更新成功！', 'success');
            loadLabels();
        } else {
            showMessage(data.error || '更新失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating label:', error);
        showMessage(`更新失败: ${error.message}`, 'error');
    });
}

function deleteLabel(id) {
    if (confirm('确定要删除这个标签吗？')) {
        fetch(`/api/labels/${id}`, {
            method: 'DELETE',
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadLabels();
            } else {
                alert('删除失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => console.error('Error:', error));
    }
}

// Users Management
function loadUsers() {
    if (!checkPermission('can_manage_users')) {
        showNoPermission('users', 7, '用户管理');
        return;
    }
    const tbody = document.querySelector('#users-table tbody');
    if (!tbody) return;
    tbody.innerHTML = '<tr><td colspan="7" class="empty-state">加载中...</td></tr>';

    Promise.all([
        fetch('/api/users').then(res => res.json()),
        fetch('/api/departments').then(res => res.json()),
        fetch('/api/user-groups').then(res => res.json())
    ]).then(([users, departments, userGroups]) => {
        if (!Array.isArray(users)) {
            throw new Error("收到的用户数据格式不正确");
        }
        if (!Array.isArray(departments)) {
            // This is a dependency, but let's log an error if it's malformed
            console.error("收到的部门数据格式不正确");
            departments = []; // prevent further errors
        }
        if (!Array.isArray(userGroups)) {
             // This is a dependency, but let's log an error if it's malformed
            console.error("收到的用户组数据格式不正确");
            userGroups = []; // prevent further errors
        }

        const departmentMap = new Map(departments.map(d => [d.id, d.name]));
        const groupMap = new Map(userGroups.map(g => [g.id, g.name]));

        if (users.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="empty-state">暂无用户数据</td></tr>';
            return;
        }
        
        tbody.innerHTML = users.map(user => {
            // 根据权限生成操作按钮
            const actionButtons = [];
            if (checkPermission('can_manage_users_edit')) {
                actionButtons.push(`<button class="btn-edit" onclick="editUser(${user.id})">编辑</button>`);
            }
            if (checkPermission('can_manage_users_delete')) {
                actionButtons.push(`<button class="btn-delete" onclick="deleteUser(${user.id})">删除</button>`);
            }
            
            return `
            <tr>
                <td>${user.id}</td>
                <td>${user.card_id}</td>
                <td>${user.name}</td>
                <td>${user.code || ''}</td>
                <td>${departmentMap.get(user.department_id) || ''}</td>
                <td>${groupMap.get(user.group_id) || ''}</td>
                <td>
                        ${actionButtons.join('')}
                </td>
            </tr>
            `;
        }).join('');
    }).catch(error => {
        console.error('Error loading users:', error);
        tbody.innerHTML = '<tr><td colspan="7" class="empty-state">加载失败</td></tr>';
    });
}

// Departments Management
function loadDepartments() {
    if (!checkPermission('can_manage_departments')) {
        showNoPermission('departments', 3, '部门管理');
        return;
    }
    const tableBody = document.getElementById('departments-table-body');
    if (!tableBody) return;
    tableBody.innerHTML = '<tr><td colspan="3">加载中...</td></tr>';
    
    fetch('/api/departments')
        .then(response => {
            if (!response.ok) {
                throw new Error(`网络错误: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (!Array.isArray(data)) {
                console.error("加载部门失败: 响应不是一个数组", data);
                tableBody.innerHTML = '<tr><td colspan="3" class="empty-state">加载部门失败：数据格式错误。</td></tr>';
                return;
            }

            if (data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="3" class="empty-state">暂无部门数据</td></tr>';
                return;
            }
            
            tableBody.innerHTML = '';
            data.forEach(department => {
                const row = document.createElement('tr');
                
                // 根据权限生成操作按钮
                const actionButtons = [];
                if (checkPermission('can_manage_departments_edit')) {
                    actionButtons.push(`<button onclick="editDepartment(${department.id})">编辑</button>`);
                }
                if (checkPermission('can_manage_departments_delete')) {
                    actionButtons.push(`<button onclick="deleteDepartment(${department.id})">删除</button>`);
                }
                
                row.innerHTML = `
                    <td>${department.id}</td>
                    <td>${escapeHTML(department.name)}</td>
                    <td>
                        ${actionButtons.join('')}
                    </td>
                `;
                tableBody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('加载部门失败:', error);
            tableBody.innerHTML = '<tr><td colspan="3" class="empty-state">加载部门失败。</td></tr>';
        });
}

// Announcements Management
function loadAnnouncements() {
    if (!checkPermission('can_manage_announcements')) {
        showNoPermission('announcements', 3, '公告管理');
        return;
    }
    const tbody = document.querySelector('#announcements-table tbody');
    if (!tbody) return;
    
    tbody.innerHTML = '<tr><td colspan="5" class="empty-state">加载中...</td></tr>';
    
    fetch('/api/admin/announcements')
        .then(response => response.json())
        .then(announcements => {
            if (announcements.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" class="empty-state">暂无公告数据</td></tr>';
                return;
            }
            
            tbody.innerHTML = announcements.map(announcement => {
                // 根据权限生成操作按钮
                const actionButtons = [];
                if (checkPermission('can_manage_announcements_edit')) {
                    actionButtons.push(`<button class="btn-edit" onclick="editAnnouncement(${announcement.id})">编辑</button>`);
                }
                if (checkPermission('can_manage_announcements_delete')) {
                    actionButtons.push(`<button class="btn-delete" onclick="deleteAnnouncement(${announcement.id})">删除</button>`);
                }
                
                return `
                <tr>
                    <td>${announcement.id}</td>
                    <td>${announcement.content}</td>
                    <td>${announcement.author_name || ''}</td>
                    <td>${formatDate(announcement.created_at)}</td>
                    <td>
                            ${actionButtons.join('')}
                    </td>
                </tr>
                `;
            }).join('');
        })
        .catch(error => {
            console.error('Error loading announcements:', error);
            tbody.innerHTML = '<tr><td colspan="5" class="empty-state">加载失败</td></tr>';
        });
}

// Utility functions
function createModal(title, content, modalId, onSave) {
    // Check if a modal with this ID already exists to prevent duplicates
    if (modalId && document.getElementById(modalId)) {
        // If it exists, maybe just update its content and return it
        const existingModal = document.getElementById(modalId);
        existingModal.querySelector('.modal-title').textContent = title;
        existingModal.querySelector('.modal-body').innerHTML = content;
        if (onSave) {
             existingModal.querySelector('.btn-save').onclick = onSave;
        }
        return existingModal;
    }

    const modal = document.createElement('div');
    modal.className = 'modal';
    if (modalId) {
        modal.id = modalId;
    }

    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">${title}</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            <div class="modal-actions">
                <button class="btn-cancel">取消</button>
                <button class="btn-save">保存</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Event listeners
    modal.querySelector('.close').onclick = () => closeModal(modal);
    modal.querySelector('.btn-cancel').onclick = () => closeModal(modal);
    
    // Attach the save handler if provided
    if (onSave) {
        modal.querySelector('.btn-save').onclick = onSave;
    }
    
    modal.style.display = 'flex'; // Make it visible
    
    return modal;
}

function closeModal(modal) {
    if (!modal) return;
    modal.style.display = 'none';
    setTimeout(() => {
        if (modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }
    }, 300);
}

function showMessage(message, type = 'info') {
    const msgDiv = document.createElement('div');
    msgDiv.className = `message message-${type}`;
    msgDiv.textContent = message;
    msgDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 25px;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        z-index: 1001;
        animation: slideIn 0.3s ease;
    `;
    
    switch(type) {
        case 'success':
            msgDiv.style.backgroundColor = '#28a745';
            break;
        case 'error':
            msgDiv.style.backgroundColor = '#dc3545';
            break;
        default:
            msgDiv.style.backgroundColor = '#667eea';
    }
    
    document.body.appendChild(msgDiv);
    
    setTimeout(() => {
        msgDiv.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (msgDiv.parentNode) {
                msgDiv.parentNode.removeChild(msgDiv);
            }
        }, 300);
    }, 3000);
}

function formatDate(dateString) {
    if (!dateString) return '';
    return new Date(dateString).toLocaleString('zh-CN');
}

// Placeholder functions for other sections
function loadUserGroups() {
    if (!checkPermission('can_manage_user_groups')) {
        showNoPermission('user-groups', 3, '用户组管理');
        return;
    }
    const tbody = document.querySelector('#user-groups-table tbody');
    if (!tbody) return;
    tbody.innerHTML = '<tr><td colspan="4" class="empty-state">加载中...</td></tr>';

    // 权限名称映射 - 更新为包含所有新权限
    const permissionNames = {
        // 后台管理权限
        'can_manage_backend': '后台管理入口',
        'can_manage_users': '用户管理',
        'can_manage_users_add': '添加用户',
        'can_manage_users_edit': '编辑用户',
        'can_manage_users_delete': '删除用户',
        'can_manage_departments': '部门管理',
        'can_manage_departments_add': '添加部门',
        'can_manage_departments_edit': '编辑部门',
        'can_manage_departments_delete': '删除部门',
        'can_manage_user_groups': '用户组管理',
        'can_manage_user_groups_add': '添加用户组',
        'can_manage_user_groups_edit': '编辑用户组',
        'can_manage_user_groups_delete': '删除用户组',
        'can_manage_labels': '标签管理',
        'can_manage_labels_add': '添加标签',
        'can_manage_labels_edit': '编辑标签',
        'can_manage_labels_delete': '删除标签',
        'can_manage_announcements': '公告管理',
        'can_manage_announcements_add': '添加公告',
        'can_manage_announcements_edit': '编辑公告',
        'can_manage_announcements_delete': '删除公告',
        'can_view_feedback': '查看反馈',
        'can_reply_feedback': '回复反馈',
        'can_delete_feedback': '删除反馈',
        'can_view_audit_log': '操作日志',
        'can_clear_audit_log': '清空操作日志',
        'can_manage_barcode_prefixes': '条形码前缀管理',
        'can_manage_barcode_prefixes_add': '添加条形码前缀',
        'can_manage_barcode_prefixes_edit': '编辑条形码前缀',
        'can_manage_barcode_prefixes_delete': '删除条形码前缀',
        'can_manage_chatbot_kb': '聊天机器人词库管理',
        'can_manage_chatbot_kb_add': '添加聊天机器人词条',
        'can_manage_chatbot_kb_edit': '编辑聊天机器人词条',
        'can_manage_chatbot_kb_delete': '删除聊天机器人词条',
        
        // 前台功能权限
        'can_view_problem_solve': '显示Problem Solve按钮',
        'can_reply_problem_solve': 'Problem Solve回复权限',
        'can_delete_problem_solve': 'Problem Solve删除权限',
        'can_print_labels': '标签打印权限',
        'can_search_labels': '标签搜索权限',
        'can_copy_labels': '标签复制权限',
        
        // 数据查看权限
        'can_view_statistics': '查看统计数据',
        'can_view_reports': '查看报表',
        'can_export_data': '导出数据',
        
        // 系统设置权限
        'can_refresh_permissions': '权限刷新',
        'can_backup_restore': '备份恢复',
        'can_manage_security': '安全设置',
        'can_manage_system_settings': '系统设定'
    };

    fetch('/api/user-groups')
        .then(response => {
            if (!response.ok) {
                throw new Error('无法加载用户组');
            }
            return response.json();
        })
        .then(groups => {
            if (!Array.isArray(groups)) {
                console.error("加载用户组失败: 响应不是一个数组", groups);
                tbody.innerHTML = '<tr><td colspan="4" class="empty-state">加载用户组失败：数据格式错误。</td></tr>';
                return;
            }
            if (groups.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" class="empty-state">暂无用户组数据</td></tr>';
                return;
            }

            tbody.innerHTML = groups.map(group => {
                let permissions = '无权限';
                if (group.permissions) {
                    const activePermissions = Object.keys(group.permissions)
                        .filter(p => group.permissions[p])
                        .map(p => permissionNames[p] || p);
                    permissions = activePermissions.length > 0 ? activePermissions.join(', ') : '无权限';
                }
                
                return `
                    <tr>
                        <td>${group.id}</td>
                        <td>${escapeHTML(group.name)}</td>
                        <td>${escapeHTML(permissions)}</td>
                        <td>
                            <button onclick="editUserGroup(${group.id})">编辑</button>
                            <button onclick="deleteUserGroup(${group.id})">删除</button>
                        </td>
                    </tr>
                `;
            }).join('');
        })
        .catch(error => {
            console.error('加载用户组失败:', error);
            tbody.innerHTML = `<tr><td colspan="4" class="empty-state">加载用户组失败: ${error.message}</td></tr>`;
        });
}

function loadFeedback() {
    if (!checkPermission('can_view_feedback')) {
        showNoPermission('feedback', 3, '反馈查看');
        return;
    }
    const tbody = document.querySelector('#feedback-table tbody');
    if (!tbody) return;
    
    tbody.innerHTML = '<tr><td colspan="7" class="empty-state">加载中...</td></tr>';
    
    fetch('/api/feedback')
        .then(response => response.json())
        .then(feedback => {
            if (feedback.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="empty-state">暂无反馈数据</td></tr>';
                return;
            }
            
            tbody.innerHTML = feedback.map(item => {
                // 根据权限生成操作按钮
                const actionButtons = [];
                if (checkPermission('can_reply_feedback')) {
                    actionButtons.push(`<button class="btn-edit" onclick="replyToFeedback(${item.id})">回复</button>`);
                }
                if (checkPermission('can_delete_feedback')) {
                    actionButtons.push(`<button class="btn-delete" onclick="deleteFeedback(${item.id})">删除</button>`);
                }
                
                return `
                <tr>
                    <td>${item.id}</td>
                    <td>${item.user_name || ''}</td>
                    <td>${item.title || ''}</td>
                    <td>${item.content ? item.content.substring(0, 50) + (item.content.length > 50 ? '...' : '') : ''}</td>
                    <td>${formatDate(item.created_at)}</td>
                    <td>${item.reply || '未回复'}</td>
                    <td>
                            ${actionButtons.join('')}
                    </td>
                </tr>
                `;
            }).join('');
        })
        .catch(error => {
            console.error('Error loading feedback:', error);
            tbody.innerHTML = '<tr><td colspan="7" class="empty-state">加载失败</td></tr>';
        });
}

function loadAuditLog() {
    if (!checkPermission('can_view_audit_log')) {
        const tbody = document.querySelector('#audit-log-table tbody');
        if (tbody) tbody.innerHTML = '<tr><td colspan="3" class="empty-state">无权限查看操作日志</td></tr>';
        const clearBtn = document.querySelector('.btn-clear');
        if (clearBtn) clearBtn.style.display = 'none';
        return;
    }
    const tbody = document.querySelector('#audit-log-table tbody');
    if (!tbody) return;
    
    tbody.innerHTML = '<tr><td colspan="4" class="empty-state">加载中...</td></tr>';
    
    fetch('/api/audit-log')
        .then(response => response.json())
        .then(logs => {
            if (logs.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" class="empty-state">暂无操作日志</td></tr>';
                return;
            }
            
            tbody.innerHTML = logs.map(log => `
                <tr>
                    <td>${log.id}</td>
                    <td>${log.user_name || '未知用户'}</td>
                    <td>${log.action}</td>
                    <td>${formatDate(log.timestamp)}</td>
                </tr>
            `).join('');
        })
        .catch(error => {
            console.error('Error loading audit log:', error);
            tbody.innerHTML = '<tr><td colspan="4" class="empty-state">加载失败</td></tr>';
    });
    // 控制清空按钮显示
    const clearBtn = document.querySelector('.btn-clear');
    if (clearBtn) {
        clearBtn.style.display = (checkPermission('can_view_audit_log') && checkPermission('can_clear_audit_log')) ? '' : 'none';
    }
}

function clearAuditLog() {
    if (!confirm('确定要清空所有操作日志吗？此操作不可恢复。')) return;
    
    fetch('/api/audit-log', {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('操作日志清空成功', 'success');
            loadAuditLog();
        } else {
            showMessage(data.error || '清空失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error clearing audit log:', error);
        showMessage('清空失败', 'error');
    });
}

// CRUD Operations for Users
function showAddUserModal() {
    // First load departments and groups for dropdowns
    Promise.all([
        fetch('/api/departments').then(r => r.json()),
        fetch('/api/user-groups').then(r => r.json())
    ]).then(([departments, groups]) => {
        const modal = createModal('添加用户', `
            <div class="form-group">
                <label for="user-card-id">员工卡ID</label>
                <input type="text" id="user-card-id" required>
            </div>
            <div class="form-group">
                <label for="user-name">姓名</label>
                <input type="text" id="user-name" required>
            </div>
            <div class="form-group">
                <label for="user-code">代码</label>
                <input type="text" id="user-code">
            </div>
            <div class="form-group">
                <label for="user-department">部门</label>
                <select id="user-department">
                    <option value="">选择部门</option>
                    ${departments.map(dept => `<option value="${dept.id}">${dept.name}</option>`).join('')}
                </select>
            </div>
            <div class="form-group">
                <label for="user-group">用户组</label>
                <select id="user-group" required>
                    ${groups.map(group => `<option value="${group.id}">${group.name}</option>`).join('')}
                </select>
            </div>
        `);
        
        modal.querySelector('.btn-save').onclick = () => {
            const cardId = document.getElementById('user-card-id').value;
            const name = document.getElementById('user-name').value;
            const code = document.getElementById('user-code').value;
            const departmentId = document.getElementById('user-department').value || null;
            const groupId = document.getElementById('user-group').value;
            
            if (!cardId || !name || !groupId) {
                alert('请填写必填字段');
                return;
            }
            
            createUser({ card_id: cardId, name, code, department_id: departmentId, group_id: groupId });
            closeModal(modal);
        };
    }).catch(error => {
        console.error('Error loading data for user modal:', error);
        showMessage('加载数据失败', 'error');
    });
}

function createUser(userData) {
    fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('用户创建成功', 'success');
            loadUsers();
        } else {
            showMessage(data.error || '创建失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error creating user:', error);
        showMessage('创建失败', 'error');
    });
}

function editUser(id) {
    // Load user data and show edit modal
    fetch('/api/users')
        .then(response => response.json())
        .then(users => {
            const user = users.find(u => u.id === id);
            if (!user) {
                showMessage('用户不存在', 'error');
                return;
            }
            
            // Load departments and groups for dropdowns
            Promise.all([
                fetch('/api/departments').then(r => r.json()),
                fetch('/api/user-groups').then(r => r.json())
            ]).then(([departments, groups]) => {
                const modal = createModal('编辑用户', `
                    <div class="form-group">
                        <label for="user-name">姓名</label>
                        <input type="text" id="user-name" value="${user.name}" required>
                    </div>
                    <div class="form-group">
                        <label for="user-code">代码</label>
                        <input type="text" id="user-code" value="${user.code || ''}">
                    </div>
                    <div class="form-group">
                        <label for="user-department">部门</label>
                        <select id="user-department">
                            <option value="">选择部门</option>
                            ${departments.map(dept => `<option value="${dept.id}" ${dept.id === user.department_id ? 'selected' : ''}>${dept.name}</option>`).join('')}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="user-group">用户组</label>
                        <select id="user-group" required>
                            ${groups.map(group => `<option value="${group.id}" ${group.id === user.group_id ? 'selected' : ''}>${group.name}</option>`).join('')}
                        </select>
                    </div>
                `);
                
                modal.querySelector('.btn-save').onclick = () => {
                    const name = document.getElementById('user-name').value;
                    const code = document.getElementById('user-code').value;
                    const departmentId = document.getElementById('user-department').value || null;
                    const groupId = document.getElementById('user-group').value;
                    
                    if (!name || !groupId) {
                        alert('请填写必填字段');
                        return;
                    }
                    
                    updateUser(id, { name, code, department_id: departmentId, group_id: groupId });
                    closeModal(modal);
                };
            });
        })
        .catch(error => {
            console.error('Error loading user:', error);
            showMessage('加载用户失败', 'error');
        });
}

function updateUser(id, userData) {
    fetch(`/api/users/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('用户更新成功', 'success');
            loadUsers();
        } else {
            showMessage(data.error || '更新失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating user:', error);
        showMessage('更新失败', 'error');
    });
}

// CRUD Operations for Departments
function showAddDepartmentModal() {
    const modal = createModal('添加部门', `
        <div class="form-group">
            <label for="department-name">部门名称</label>
            <input type="text" id="department-name" required>
        </div>
    `);
    
    modal.querySelector('.btn-save').onclick = () => {
        const name = document.getElementById('department-name').value;
        
        if (!name) {
            alert('请填写部门名称');
            return;
        }
        
        createDepartment({ name });
        closeModal(modal);
    };
}

function createDepartment(deptData) {
    fetch('/api/departments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(deptData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('部门创建成功', 'success');
            loadDepartments();
        } else {
            showMessage(data.error || '创建失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error creating department:', error);
        showMessage('创建失败', 'error');
    });
}

function editDepartment(id) {
    fetch('/api/departments')
        .then(response => response.json())
        .then(departments => {
            const dept = departments.find(d => d.id === id);
            if (!dept) {
                showMessage('部门不存在', 'error');
                return;
            }
            
            const modal = createModal('编辑部门', `
                <div class="form-group">
                    <label for="department-name">部门名称</label>
                    <input type="text" id="department-name" value="${dept.name}" required>
                </div>
            `);
            
            modal.querySelector('.btn-save').onclick = () => {
                const name = document.getElementById('department-name').value;
                
                if (!name) {
                    alert('请填写部门名称');
                    return;
                }
                
                updateDepartment(id, { name });
                closeModal(modal);
            };
        })
        .catch(error => {
            console.error('Error loading department:', error);
            showMessage('加载部门失败', 'error');
        });
}

function updateDepartment(id, deptData) {
    fetch(`/api/departments/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(deptData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('部门更新成功', 'success');
            loadDepartments();
        } else {
            showMessage(data.error || '更新失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating department:', error);
        showMessage('更新失败', 'error');
    });
}

function deleteDepartment(id) {
    if (!confirm('确定要删除这个部门吗？')) return;
    
    fetch(`/api/departments/${id}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('部门删除成功', 'success');
            loadDepartments();
        } else {
            showMessage(data.error || '删除失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting department:', error);
        showMessage('删除失败', 'error');
    });
}

// 权限分类和颜色配置
function getPermissionCategory(permission) {
    const categories = {
        // 后台管理 - 蓝色系
        'can_manage_backend': { color: '#3498db', bgColor: '#3498db15' },

        // 用户管理 - 绿色系
        'can_manage_users': { color: '#27ae60', bgColor: '#27ae6015' },
        'can_manage_users_add': { color: '#27ae60', bgColor: '#27ae6015' },
        'can_manage_users_edit': { color: '#27ae60', bgColor: '#27ae6015' },
        'can_manage_users_delete': { color: '#27ae60', bgColor: '#27ae6015' },

        // 部门管理 - 橙色系
        'can_manage_departments': { color: '#f39c12', bgColor: '#f39c1215' },
        'can_manage_departments_add': { color: '#f39c12', bgColor: '#f39c1215' },
        'can_manage_departments_edit': { color: '#f39c12', bgColor: '#f39c1215' },
        'can_manage_departments_delete': { color: '#f39c12', bgColor: '#f39c1215' },

        // 用户组管理 - 紫色系
        'can_manage_user_groups': { color: '#9b59b6', bgColor: '#9b59b615' },
        'can_manage_user_groups_add': { color: '#9b59b6', bgColor: '#9b59b615' },
        'can_manage_user_groups_edit': { color: '#9b59b6', bgColor: '#9b59b615' },
        'can_manage_user_groups_delete': { color: '#9b59b6', bgColor: '#9b59b615' },

        // 标签管理 - 青色系
        'can_manage_labels': { color: '#1abc9c', bgColor: '#1abc9c15' },
        'can_manage_labels_add': { color: '#1abc9c', bgColor: '#1abc9c15' },
        'can_manage_labels_edit': { color: '#1abc9c', bgColor: '#1abc9c15' },
        'can_manage_labels_delete': { color: '#1abc9c', bgColor: '#1abc9c15' },

        // 公告管理 - 红色系
        'can_manage_announcements': { color: '#e74c3c', bgColor: '#e74c3c15' },
        'can_manage_announcements_add': { color: '#e74c3c', bgColor: '#e74c3c15' },
        'can_manage_announcements_edit': { color: '#e74c3c', bgColor: '#e74c3c15' },
        'can_manage_announcements_delete': { color: '#e74c3c', bgColor: '#e74c3c15' },

        // 反馈管理 - 粉色系
        'can_view_feedback': { color: '#e91e63', bgColor: '#e91e6315' },
        'can_reply_feedback': { color: '#e91e63', bgColor: '#e91e6315' },
        'can_delete_feedback': { color: '#e91e63', bgColor: '#e91e6315' },

        // 审计日志 - 灰色系
        'can_view_audit_log': { color: '#34495e', bgColor: '#34495e15' },
        'can_clear_audit_log': { color: '#34495e', bgColor: '#34495e15' },

        // 条形码管理 - 棕色系
        'can_manage_barcode_prefixes': { color: '#8d6e63', bgColor: '#8d6e6315' },
        'can_manage_barcode_prefixes_add': { color: '#8d6e63', bgColor: '#8d6e6315' },
        'can_manage_barcode_prefixes_edit': { color: '#8d6e63', bgColor: '#8d6e6315' },
        'can_manage_barcode_prefixes_delete': { color: '#8d6e63', bgColor: '#8d6e6315' },

        // 聊天机器人 - 深蓝色系
        'can_manage_chatbot_kb': { color: '#2196f3', bgColor: '#2196f315' },
        'can_manage_chatbot_kb_add': { color: '#2196f3', bgColor: '#2196f315' },
        'can_manage_chatbot_kb_edit': { color: '#2196f3', bgColor: '#2196f315' },
        'can_manage_chatbot_kb_delete': { color: '#2196f3', bgColor: '#2196f315' },

        // 前台功能 - 绿松石色系
        'can_view_problem_solve': { color: '#00bcd4', bgColor: '#00bcd415' },
        'can_reply_problem_solve': { color: '#00bcd4', bgColor: '#00bcd415' },
        'can_delete_problem_solve': { color: '#00bcd4', bgColor: '#00bcd415' },
        'can_search_labels': { color: '#00bcd4', bgColor: '#00bcd415' },
        'can_print_labels': { color: '#00bcd4', bgColor: '#00bcd415' },
        'can_copy_labels': { color: '#00bcd4', bgColor: '#00bcd415' },

        // 数据查看 - 深绿色系
        'can_view_statistics': { color: '#4caf50', bgColor: '#4caf5015' },
        'can_view_reports': { color: '#4caf50', bgColor: '#4caf5015' },
        'can_export_data': { color: '#4caf50', bgColor: '#4caf5015' },

        // 系统设置 - 深紫色系
        'can_refresh_permissions': { color: '#673ab7', bgColor: '#673ab715' },
        'can_backup_restore': { color: '#673ab7', bgColor: '#673ab715' },
        'can_manage_security': { color: '#673ab7', bgColor: '#673ab715' }
    };

    return categories[permission] || { color: '#666', bgColor: '#66615' };
}

// 权限ID到数据库字段的映射
function getDbFieldByPermId(permId) {
    const mapping = {
        'perm-backend': 'can_manage_backend',
        'perm-users': 'can_manage_users',
        'perm-users-add': 'can_manage_users_add',
        'perm-users-edit': 'can_manage_users_edit',
        'perm-users-delete': 'can_manage_users_delete',
        'perm-departments': 'can_manage_departments',
        'perm-departments-add': 'can_manage_departments_add',
        'perm-departments-edit': 'can_manage_departments_edit',
        'perm-departments-delete': 'can_manage_departments_delete',
        'perm-user-groups': 'can_manage_user_groups',
        'perm-user-groups-add': 'can_manage_user_groups_add',
        'perm-user-groups-edit': 'can_manage_user_groups_edit',
        'perm-user-groups-delete': 'can_manage_user_groups_delete',
        'perm-labels': 'can_manage_labels',
        'perm-labels-add': 'can_manage_labels_add',
        'perm-labels-edit': 'can_manage_labels_edit',
        'perm-labels-delete': 'can_manage_labels_delete',
        'perm-announcements': 'can_manage_announcements',
        'perm-announcements-add': 'can_manage_announcements_add',
        'perm-announcements-edit': 'can_manage_announcements_edit',
        'perm-announcements-delete': 'can_manage_announcements_delete',
        'perm-feedback': 'can_view_feedback',
        'perm-feedback-view': 'can_view_feedback',
        'perm-feedback-reply': 'can_reply_feedback',
        'perm-feedback-delete': 'can_delete_feedback',
        'perm-audit-log': 'can_view_audit_log',
        'perm-audit-log-clear': 'can_clear_audit_log',
        'perm-barcode-prefixes': 'can_manage_barcode_prefixes',
        'perm-barcode-prefixes-add': 'can_manage_barcode_prefixes_add',
        'perm-barcode-prefixes-edit': 'can_manage_barcode_prefixes_edit',
        'perm-barcode-prefixes-delete': 'can_manage_barcode_prefixes_delete',
        'perm-chatbot-kb': 'can_manage_chatbot_kb',
        'perm-chatbot-kb-add': 'can_manage_chatbot_kb_add',
        'perm-chatbot-kb-edit': 'can_manage_chatbot_kb_edit',
        'perm-chatbot-kb-delete': 'can_manage_chatbot_kb_delete',
        'perm-problem-solve': 'can_view_problem_solve',
        'perm-problem-solve-view': 'can_view_problem_solve',
        'perm-problem-solve-reply': 'can_reply_problem_solve',
        'perm-problem-solve-delete': 'can_delete_problem_solve',
        'perm-label-operations': 'can_print_labels',
        'perm-label-print': 'can_print_labels',
        'perm-label-search': 'can_search_labels',
        'perm-label-copy': 'can_copy_labels',
        'perm-view-statistics': 'can_view_statistics',
        'perm-view-reports': 'can_view_reports',
        'perm-export-data': 'can_export_data',
        'perm-refresh-permissions': 'can_refresh_permissions',
        'perm-backup-restore': 'can_backup_restore',
        'perm-security-settings': 'can_manage_security'
    };
    return mapping[permId];
}

// 为权限标签添加彩色圆点
function addColorDotToPermissionLabel(permissionId, labelText) {
    const dbField = getDbFieldByPermId(permissionId);
    if (!dbField) return labelText;

    const category = getPermissionCategory(dbField);
    return `${labelText}<span class="permission-dot" style="
        color: ${category.color};
        font-weight: bold;
        margin-left: 6px;
        font-size: 1.1em;
        text-shadow: 0 0 3px currentColor;
    ">·</span>`;
}

// Other CRUD operations
function showAddUserGroupModal() {
    const modal = createModal('添加用户组', `
        <div class="form-group">
            <label for="group-name">用户组名称</label>
            <input type="text" id="group-name" required>
        </div>
        <div class="permissions-container">
            <div class="form-group">
                <label style="font-weight: bold; color: #333; margin-bottom: 10px; display: flex; align-items: center; justify-content: space-between; gap: 12px;">
    <span>🔧 后台管理权限 <small style="color: #666;">（系统核心管理功能）</small></span>
    <span style="display: flex; align-items: center; gap: 4px; white-space: nowrap;">
        <input type="checkbox" id="perm-backend" class="main-permission" style="vertical-align: middle; margin-right: 4px;">
        <span style="font-size: 18px;">🔑</span> 后台管理入口
    </span>
</label>
                <div class="permissions-grid">
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-users" class="main-permission"> ${addColorDotToPermissionLabel('perm-users', '👤 用户管理')}</label>
                        <div class="sub-permissions" data-parent="perm-users">
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-users-add" class="sub-permission"> ${addColorDotToPermissionLabel('perm-users-add', '添加用户')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-users-edit" class="sub-permission"> ${addColorDotToPermissionLabel('perm-users-edit', '编辑用户')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-users-delete" class="sub-permission"> ${addColorDotToPermissionLabel('perm-users-delete', '删除用户')}</label>
                </div>
            </div>
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-departments" class="main-permission"> ${addColorDotToPermissionLabel('perm-departments', '🏢 部门管理')}</label>
                        <div class="sub-permissions" data-parent="perm-departments">
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-departments-add" class="sub-permission"> ${addColorDotToPermissionLabel('perm-departments-add', '添加部门')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-departments-edit" class="sub-permission"> ${addColorDotToPermissionLabel('perm-departments-edit', '编辑部门')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-departments-delete" class="sub-permission"> ${addColorDotToPermissionLabel('perm-departments-delete', '删除部门')}</label>
                        </div>
                    </div>
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-user-groups" class="main-permission"> ${addColorDotToPermissionLabel('perm-user-groups', '👥 用户组管理')}</label>
                        <div class="sub-permissions" data-parent="perm-user-groups">
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-user-groups-add" class="sub-permission"> ${addColorDotToPermissionLabel('perm-user-groups-add', '添加用户组')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-user-groups-edit" class="sub-permission"> ${addColorDotToPermissionLabel('perm-user-groups-edit', '编辑用户组')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-user-groups-delete" class="sub-permission"> ${addColorDotToPermissionLabel('perm-user-groups-delete', '删除用户组')}</label>
                        </div>
                    </div>
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-labels" class="main-permission"> ${addColorDotToPermissionLabel('perm-labels', '🏷️ 标签管理')}</label>
                        <div class="sub-permissions" data-parent="perm-labels">
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-labels-add" class="sub-permission"> ${addColorDotToPermissionLabel('perm-labels-add', '添加标签')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-labels-edit" class="sub-permission"> ${addColorDotToPermissionLabel('perm-labels-edit', '编辑标签')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-labels-delete" class="sub-permission"> ${addColorDotToPermissionLabel('perm-labels-delete', '删除标签')}</label>
                        </div>
                    </div>
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-announcements" class="main-permission"> ${addColorDotToPermissionLabel('perm-announcements', '📢 公告管理')}</label>
                        <div class="sub-permissions" data-parent="perm-announcements">
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-announcements-add" class="sub-permission"> ${addColorDotToPermissionLabel('perm-announcements-add', '添加公告')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-announcements-edit" class="sub-permission"> ${addColorDotToPermissionLabel('perm-announcements-edit', '编辑公告')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-announcements-delete" class="sub-permission"> ${addColorDotToPermissionLabel('perm-announcements-delete', '删除公告')}</label>
                        </div>
                    </div>
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-feedback" class="main-permission"> ${addColorDotToPermissionLabel('perm-feedback', '💬 反馈管理')}</label>
                        <div class="sub-permissions" data-parent="perm-feedback">
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-feedback-view" class="sub-permission"> ${addColorDotToPermissionLabel('perm-feedback-view', '查看反馈')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-feedback-reply" class="sub-permission"> ${addColorDotToPermissionLabel('perm-feedback-reply', '回复反馈')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-feedback-delete" class="sub-permission"> ${addColorDotToPermissionLabel('perm-feedback-delete', '删除反馈')}</label>
                        </div>
                    </div>
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-barcode-prefixes" class="main-permission"> ${addColorDotToPermissionLabel('perm-barcode-prefixes', '📊 条形码前缀管理')}</label>
                        <div class="sub-permissions" data-parent="perm-barcode-prefixes">
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-barcode-prefixes-add" class="sub-permission"> ${addColorDotToPermissionLabel('perm-barcode-prefixes-add', '添加前缀')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-barcode-prefixes-edit" class="sub-permission"> ${addColorDotToPermissionLabel('perm-barcode-prefixes-edit', '编辑前缀')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-barcode-prefixes-delete" class="sub-permission"> ${addColorDotToPermissionLabel('perm-barcode-prefixes-delete', '删除前缀')}</label>
                        </div>
                    </div>
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-chatbot-kb" class="main-permission"> ${addColorDotToPermissionLabel('perm-chatbot-kb', '🤖 聊天机器人词库管理')}</label>
                        <div class="sub-permissions" data-parent="perm-chatbot-kb">
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-chatbot-kb-add" class="sub-permission"> ${addColorDotToPermissionLabel('perm-chatbot-kb-add', '添加词条')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-chatbot-kb-edit" class="sub-permission"> ${addColorDotToPermissionLabel('perm-chatbot-kb-edit', '编辑词条')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-chatbot-kb-delete" class="sub-permission"> ${addColorDotToPermissionLabel('perm-chatbot-kb-delete', '删除词条')}</label>
                        </div>
                    </div>
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-audit-log" class="main-permission"> ${addColorDotToPermissionLabel('perm-audit-log', '📋 操作日志')}</label>
                        <div class="sub-permissions" data-parent="perm-audit-log">
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-audit-log-clear" class="sub-permission"> ${addColorDotToPermissionLabel('perm-audit-log-clear', '清空操作日志')}</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label style="font-weight: bold; color: #333; margin-bottom: 10px;">🖥️ 前台功能权限 <small style="color: #666;">（用户日常操作功能）</small></label>
                <div class="permissions-grid">
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-problem-solve" class="main-permission"> ${addColorDotToPermissionLabel('perm-problem-solve', '❓ Problem Solve')}</label>
                        <div class="sub-permissions" data-parent="perm-problem-solve">
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-problem-solve-view" class="sub-permission"> ${addColorDotToPermissionLabel('perm-problem-solve-view', '显示Problem Solve按钮')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-problem-solve-reply" class="sub-permission"> ${addColorDotToPermissionLabel('perm-problem-solve-reply', 'Problem Solve回复权限')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-problem-solve-delete" class="sub-permission"> ${addColorDotToPermissionLabel('perm-problem-solve-delete', 'Problem Solve删除权限')}</label>
                        </div>
                    </div>
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-label-operations" class="main-permission"> ${addColorDotToPermissionLabel('perm-label-operations', '🏷️ 标签操作')}</label>
                        <div class="sub-permissions" data-parent="perm-label-operations">
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-label-print" class="sub-permission"> ${addColorDotToPermissionLabel('perm-label-print', '标签打印权限')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-label-search" class="sub-permission"> ${addColorDotToPermissionLabel('perm-label-search', '标签搜索权限')}</label>
                            <label class="permission-item sub-item"><input type="checkbox" id="perm-label-copy" class="sub-permission"> ${addColorDotToPermissionLabel('perm-label-copy', '标签复制权限')}</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label style="font-weight: bold; color: #333; margin-bottom: 10px;">📊 数据查看权限 <small style="color: #666;">（数据统计和报表功能）</small></label>
                <div class="permissions-grid">
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-view-statistics" class="main-permission"> ${addColorDotToPermissionLabel('perm-view-statistics', '📈 查看统计数据')}</label>
                    </div>
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-view-reports" class="main-permission"> ${addColorDotToPermissionLabel('perm-view-reports', '📋 查看报表')}</label>
                    </div>
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-export-data" class="main-permission"> ${addColorDotToPermissionLabel('perm-export-data', '📤 导出数据')}</label>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label style="font-weight: bold; color: #333; margin-bottom: 10px;">⚙️ 系统设置权限 <small style="color: #666;">（系统配置和维护功能）</small></label>
                <div class="permissions-grid">
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-refresh-permissions" class="main-permission"> ${addColorDotToPermissionLabel('perm-refresh-permissions', '🔧 权限刷新')}</label>
                    </div>
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-backup-restore" class="main-permission"> ${addColorDotToPermissionLabel('perm-backup-restore', '💾 备份恢复')}</label>
                    </div>
                    <div class="permissions-group">
                        <label class="permission-item"><input type="checkbox" id="perm-security-settings" class="main-permission"> ${addColorDotToPermissionLabel('perm-security-settings', '🔒 安全设置')}</label>
                    </div>
                </div>
            </div>
        </div>
    `);

    // 设置权限联动逻辑
    setupPermissionLogic(modal);

    modal.querySelector('.btn-save').onclick = () => {
        const name = document.getElementById('group-name').value;
        if (!name) {
            alert('请填写用户组名称');
            return;
        }
        const permissions = collectPermissions(modal);
        
        createUserGroup({ name, permissions });
        closeModal(modal);
    };
}

// 设置权限联动逻辑
function setupPermissionLogic(modal) {
    // 移除主权限自动勾选子权限的逻辑
    // 主权限和子权限保持独立
    // 主权限控制菜单显示，子权限控制具体操作
    
    // 子权限变化时检查主权限状态（可选，用于显示半选状态）
    modal.querySelectorAll('.sub-permission').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const parentId = this.closest('.sub-permissions').getAttribute('data-parent');
            const parentCheckbox = modal.querySelector(`#${parentId}`);
            const siblingCheckboxes = modal.querySelectorAll(`[data-parent="${parentId}"] .sub-permission`);
            
            // 检查是否所有子权限都被勾选
            const allChecked = Array.from(siblingCheckboxes).every(cb => cb.checked);
            const anyChecked = Array.from(siblingCheckboxes).some(cb => cb.checked);
            
            // 设置主权限的半选状态（仅视觉提示，不影响功能）
            if (allChecked) {
                parentCheckbox.indeterminate = false;
            } else if (anyChecked) {
                parentCheckbox.indeterminate = true;
            } else {
                parentCheckbox.indeterminate = false;
            }
        });
    });

    // 新增：后台管理入口联动
    const backendCheckbox = modal.querySelector('#perm-backend');
    const backendPermissionIds = [
        'perm-users', 'perm-users-add', 'perm-users-edit', 'perm-users-delete',
        'perm-departments', 'perm-departments-add', 'perm-departments-edit', 'perm-departments-delete',
        'perm-user-groups', 'perm-user-groups-add', 'perm-user-groups-edit', 'perm-user-groups-delete',
        'perm-labels', 'perm-labels-add', 'perm-labels-edit', 'perm-labels-delete',
        'perm-announcements', 'perm-announcements-add', 'perm-announcements-edit', 'perm-announcements-delete',
        'perm-feedback', 'perm-feedback-view', 'perm-feedback-reply', 'perm-feedback-delete',
        'perm-audit-log',
        'perm-barcode-prefixes', 'perm-barcode-prefixes-add', 'perm-barcode-prefixes-edit', 'perm-barcode-prefixes-delete',
        'perm-chatbot-kb', 'perm-chatbot-kb-add', 'perm-chatbot-kb-edit', 'perm-chatbot-kb-delete'
    ];
    function updateBackendCheckbox() {
        const anyChecked = backendPermissionIds.some(id => {
            const cb = modal.querySelector(`#${id}`);
            return cb && cb.checked;
        });
        if (anyChecked) {
            backendCheckbox.checked = true;
        } else {
            // 只有在未手动取消时才自动取消
            if (!backendCheckbox.dataset.manualUncheck) {
                backendCheckbox.checked = false;
            }
        }
    }
    backendPermissionIds.forEach(id => {
        const cb = modal.querySelector(`#${id}`);
        if (cb) {
            cb.addEventListener('change', updateBackendCheckbox);
        }
    });
    backendCheckbox.addEventListener('change', function() {
        if (!this.checked) {
            this.dataset.manualUncheck = '1';
        } else {
            delete this.dataset.manualUncheck;
        }
    });
}

// 收集权限数据
function collectPermissions(modal) {
    const permissions = {};
    
    // 映射权限ID到数据库字段名
    const permissionMapping = {
        // 后台管理权限
        'perm-backend': 'can_manage_backend',
        'perm-users': 'can_manage_users',
        'perm-users-add': 'can_manage_users_add',
        'perm-users-edit': 'can_manage_users_edit',
        'perm-users-delete': 'can_manage_users_delete',
        'perm-departments': 'can_manage_departments',
        'perm-departments-add': 'can_manage_departments_add',
        'perm-departments-edit': 'can_manage_departments_edit',
        'perm-departments-delete': 'can_manage_departments_delete',
        'perm-user-groups': 'can_manage_user_groups',
        'perm-user-groups-add': 'can_manage_user_groups_add',
        'perm-user-groups-edit': 'can_manage_user_groups_edit',
        'perm-user-groups-delete': 'can_manage_user_groups_delete',
        'perm-labels': 'can_manage_labels',
        'perm-labels-add': 'can_manage_labels_add',
        'perm-labels-edit': 'can_manage_labels_edit',
        'perm-labels-delete': 'can_manage_labels_delete',
        'perm-announcements': 'can_manage_announcements',
        'perm-announcements-add': 'can_manage_announcements_add',
        'perm-announcements-edit': 'can_manage_announcements_edit',
        'perm-announcements-delete': 'can_manage_announcements_delete',
        'perm-feedback': 'can_view_feedback',
        'perm-feedback-view': 'can_view_feedback',
        'perm-feedback-reply': 'can_reply_feedback',
        'perm-feedback-delete': 'can_delete_feedback',
        'perm-audit-log': 'can_view_audit_log',
        'perm-barcode-prefixes': 'can_manage_barcode_prefixes',
        'perm-barcode-prefixes-add': 'can_manage_barcode_prefixes_add',
        'perm-barcode-prefixes-edit': 'can_manage_barcode_prefixes_edit',
        'perm-barcode-prefixes-delete': 'can_manage_barcode_prefixes_delete',
        'perm-chatbot-kb': 'can_manage_chatbot_kb',
        'perm-chatbot-kb-add': 'can_manage_chatbot_kb_add',
        'perm-chatbot-kb-edit': 'can_manage_chatbot_kb_edit',
        'perm-chatbot-kb-delete': 'can_manage_chatbot_kb_delete',
        
        // 前台功能权限
        'perm-problem-solve': 'can_view_problem_solve',
        'perm-problem-solve-view': 'can_view_problem_solve',
        'perm-problem-solve-reply': 'can_reply_problem_solve',
        'perm-problem-solve-delete': 'can_delete_problem_solve',
        'perm-label-operations': 'can_print_labels',
        'perm-label-print': 'can_print_labels',
        'perm-label-search': 'can_search_labels',
        'perm-label-copy': 'can_copy_labels',
        'perm-chat': 'can_use_chat',
        'perm-chat-robot': 'can_chat_robot',
        'perm-chat-public': 'can_chat_public',
        'perm-chat-knowledge-review': 'can_review_knowledge_submissions',
        'perm-chat-knowledge-submit': 'can_submit_knowledge',
        
        // 数据查看权限
        'perm-view-statistics': 'can_view_statistics',
        'perm-view-reports': 'can_view_reports',
        'perm-export-data': 'can_export_data',
        
        // 系统设置权限
        'perm-refresh-permissions': 'can_refresh_permissions',
        'perm-backup-restore': 'can_backup_restore',
        'perm-security-settings': 'can_manage_security',
        'perm-system-settings': 'can_manage_system_settings',
        'perm-audit-log-clear': 'can_clear_audit_log'
    };
    
    // 收集所有权限状态
    modal.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        const permId = checkbox.id;
        const dbField = permissionMapping[permId];
        
        if (dbField) {
            // 主权限和子权限保持独立，直接记录当前状态
            permissions[dbField] = checkbox.checked;
        }
    });
    
    // 🔧 自动设置后台管理权限：如果拥有后台管理权限分组下的任何一项权限，就设置can_manage_backend为True
    const backendPermissions = [
        'can_manage_users', 'can_manage_users_add', 'can_manage_users_edit', 'can_manage_users_delete',
        'can_manage_departments', 'can_manage_departments_add', 'can_manage_departments_edit', 'can_manage_departments_delete',
        'can_manage_user_groups', 'can_manage_user_groups_add', 'can_manage_user_groups_edit', 'can_manage_user_groups_delete',
        'can_manage_labels', 'can_manage_labels_add', 'can_manage_labels_edit', 'can_manage_labels_delete',
        'can_manage_announcements', 'can_manage_announcements_add', 'can_manage_announcements_edit', 'can_manage_announcements_delete',
        'can_view_feedback', 'can_reply_feedback', 'can_delete_feedback',
        'can_view_audit_log', 'can_clear_audit_log',
        'can_manage_barcode_prefixes', 'can_manage_barcode_prefixes_add', 'can_manage_barcode_prefixes_edit', 'can_manage_barcode_prefixes_delete',
        'can_manage_chatbot_kb', 'can_manage_chatbot_kb_add', 'can_manage_chatbot_kb_edit', 'can_manage_chatbot_kb_delete',
        'can_manage_system_settings'
    ];
    
    // 检查是否有任何后台管理权限
    const hasBackendPermission = backendPermissions.some(perm => permissions[perm]);
    permissions['can_manage_backend'] = hasBackendPermission;
    
    return permissions;
}

function createUserGroup(groupData) {
    fetch('/api/user-groups', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(groupData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('用户组创建成功', 'success');
            loadUserGroups();
        } else {
            showMessage(data.error || '创建失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error creating user group:', error);
        showMessage('创建失败', 'error');
    });
}

function editUserGroup(id) {
    fetch('/api/user-groups')
        .then(response => response.json())
        .then(groups => {
            const group = groups.find(g => g.id === id);
            if (!group) {
                showMessage('用户组不存在', 'error');
                return;
            }
            const permissions = group.permissions || {};
            const modal = createModal('编辑用户组', `
                <div class="form-group">
                    <label for="group-name">用户组名称</label>
                    <input type="text" id="group-name" value="${group.name}" required>
                </div>
                <div class="permissions-container">
                    <div class="form-group">
                        <label style="font-weight: bold; color: #333; margin-bottom: 10px; display: flex; align-items: center; justify-content: space-between; gap: 12px;">
    <span>🔧 后台管理权限 <small style="color: #666;">（系统核心管理功能）</small></span>
    <span style="display: flex; align-items: center; gap: 4px; white-space: nowrap;">
        <input type="checkbox" id="perm-backend" class="main-permission" style="vertical-align: middle; margin-right: 4px;">
        <span style="font-size: 18px;">🔑</span> 后台管理入口
    </span>
</label>
                        <div class="permissions-grid">
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-users" class="main-permission" ${permissions.can_manage_users ? 'checked' : ''}> ${addColorDotToPermissionLabel('perm-users', '👤 用户管理')}</label>
                                <div class="sub-permissions" data-parent="perm-users">
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-users-add" class="sub-permission" ${permissions.can_manage_users_add ? 'checked' : ''}> ${addColorDotToPermissionLabel('perm-users-add', '添加用户')}</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-users-edit" class="sub-permission" ${permissions.can_manage_users_edit ? 'checked' : ''}> ${addColorDotToPermissionLabel('perm-users-edit', '编辑用户')}</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-users-delete" class="sub-permission" ${permissions.can_manage_users_delete ? 'checked' : ''}> ${addColorDotToPermissionLabel('perm-users-delete', '删除用户')}</label>
                        </div>
                    </div>
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-departments" class="main-permission" ${permissions.can_manage_departments ? 'checked' : ''}> ${addColorDotToPermissionLabel('perm-departments', '🏢 部门管理')}</label>
                                <div class="sub-permissions" data-parent="perm-departments">
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-departments-add" class="sub-permission" ${permissions.can_manage_departments_add ? 'checked' : ''}> ${addColorDotToPermissionLabel('perm-departments-add', '添加部门')}</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-departments-edit" class="sub-permission" ${permissions.can_manage_departments_edit ? 'checked' : ''}> ${addColorDotToPermissionLabel('perm-departments-edit', '编辑部门')}</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-departments-delete" class="sub-permission" ${permissions.can_manage_departments_delete ? 'checked' : ''}> ${addColorDotToPermissionLabel('perm-departments-delete', '删除部门')}</label>
                                </div>
                            </div>
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-user-groups" class="main-permission" ${permissions.can_manage_user_groups ? 'checked' : ''}> ${addColorDotToPermissionLabel('perm-user-groups', '👥 用户组管理')}</label>
                                <div class="sub-permissions" data-parent="perm-user-groups">
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-user-groups-add" class="sub-permission" ${permissions.can_manage_user_groups_add ? 'checked' : ''}> ${addColorDotToPermissionLabel('perm-user-groups-add', '添加用户组')}</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-user-groups-edit" class="sub-permission" ${permissions.can_manage_user_groups_edit ? 'checked' : ''}> ${addColorDotToPermissionLabel('perm-user-groups-edit', '编辑用户组')}</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-user-groups-delete" class="sub-permission" ${permissions.can_manage_user_groups_delete ? 'checked' : ''}> ${addColorDotToPermissionLabel('perm-user-groups-delete', '删除用户组')}</label>
                                </div>
                            </div>
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-labels" class="main-permission" ${permissions.can_manage_labels ? 'checked' : ''}> 🏷️ 标签管理</label>
                                <div class="sub-permissions" data-parent="perm-labels">
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-labels-add" class="sub-permission" ${permissions.can_manage_labels_add ? 'checked' : ''}> 添加标签</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-labels-edit" class="sub-permission" ${permissions.can_manage_labels_edit ? 'checked' : ''}> 编辑标签</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-labels-delete" class="sub-permission" ${permissions.can_manage_labels_delete ? 'checked' : ''}> 删除标签</label>
                                </div>
                            </div>
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-announcements" class="main-permission" ${permissions.can_manage_announcements ? 'checked' : ''}> 📢 公告管理</label>
                                <div class="sub-permissions" data-parent="perm-announcements">
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-announcements-add" class="sub-permission" ${permissions.can_manage_announcements_add ? 'checked' : ''}> 添加公告</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-announcements-edit" class="sub-permission" ${permissions.can_manage_announcements_edit ? 'checked' : ''}> 编辑公告</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-announcements-delete" class="sub-permission" ${permissions.can_manage_announcements_delete ? 'checked' : ''}> 删除公告</label>
                                </div>
                            </div>
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-feedback" class="main-permission" ${permissions.can_view_feedback ? 'checked' : ''}> 💬 反馈管理</label>
                                <div class="sub-permissions" data-parent="perm-feedback">
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-feedback-view" class="sub-permission" ${permissions.can_view_feedback ? 'checked' : ''}> 查看反馈</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-feedback-reply" class="sub-permission" ${permissions.can_reply_feedback ? 'checked' : ''}> 回复反馈</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-feedback-delete" class="sub-permission" ${permissions.can_delete_feedback ? 'checked' : ''}> 删除反馈</label>
                                </div>
                            </div>
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-barcode-prefixes" class="main-permission" ${permissions.can_manage_barcode_prefixes ? 'checked' : ''}> 📊 条形码前缀管理</label>
                                <div class="sub-permissions" data-parent="perm-barcode-prefixes">
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-barcode-prefixes-add" class="sub-permission" ${permissions.can_manage_barcode_prefixes_add ? 'checked' : ''}> 添加前缀</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-barcode-prefixes-edit" class="sub-permission" ${permissions.can_manage_barcode_prefixes_edit ? 'checked' : ''}> 编辑前缀</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-barcode-prefixes-delete" class="sub-permission" ${permissions.can_manage_barcode_prefixes_delete ? 'checked' : ''}> 删除前缀</label>
                                </div>
                            </div>
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-chatbot-kb" class="main-permission" ${permissions.can_manage_chatbot_kb ? 'checked' : ''}> 🤖 聊天机器人词库管理</label>
                                <div class="sub-permissions" data-parent="perm-chatbot-kb">
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-chatbot-kb-add" class="sub-permission" ${permissions.can_manage_chatbot_kb_add ? 'checked' : ''}> 添加词条</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-chatbot-kb-edit" class="sub-permission" ${permissions.can_manage_chatbot_kb_edit ? 'checked' : ''}> 编辑词条</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-chatbot-kb-delete" class="sub-permission" ${permissions.can_manage_chatbot_kb_delete ? 'checked' : ''}> 删除词条</label>
                                </div>
                            </div>
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-audit-log" class="main-permission" ${permissions.can_view_audit_log ? 'checked' : ''}> 📋 操作日志</label>
                                <div class="sub-permissions" data-parent="perm-audit-log">
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-audit-log-clear" class="sub-permission" ${permissions.can_clear_audit_log ? 'checked' : ''}> 清空操作日志</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label style="font-weight: bold; color: #333; margin-bottom: 10px;">🖥️ 前台功能权限 <small style="color: #666;">（用户日常操作功能）</small></label>
                        <div class="permissions-grid">
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-problem-solve" class="main-permission" ${permissions.can_view_problem_solve ? 'checked' : ''}> ❓ Problem Solve</label>
                                <div class="sub-permissions" data-parent="perm-problem-solve">
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-problem-solve-view" class="sub-permission" ${permissions.can_view_problem_solve ? 'checked' : ''}> 显示Problem Solve按钮</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-problem-solve-reply" class="sub-permission" ${permissions.can_reply_problem_solve ? 'checked' : ''}> Problem Solve回复权限</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-problem-solve-delete" class="sub-permission" ${permissions.can_delete_problem_solve ? 'checked' : ''}> Problem Solve删除权限</label>
                                </div>
                            </div>
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-label-operations" class="main-permission" ${permissions.can_print_labels ? 'checked' : ''}> 🏷️ 标签操作</label>
                                <div class="sub-permissions" data-parent="perm-label-operations">
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-label-print" class="sub-permission" ${permissions.can_print_labels ? 'checked' : ''}> 标签打印权限</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-label-search" class="sub-permission" ${permissions.can_search_labels ? 'checked' : ''}> 标签搜索权限</label>
                                    <label class="permission-item sub-item"><input type="checkbox" id="perm-label-copy" class="sub-permission" ${permissions.can_copy_labels ? 'checked' : ''}> 标签复制权限</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label style="font-weight: bold; color: #333; margin-bottom: 10px;">📊 数据查看权限 <small style="color: #666;">（数据统计和报表功能）</small></label>
                        <div class="permissions-grid">
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-view-statistics" class="main-permission" ${permissions.can_view_statistics ? 'checked' : ''}> 📈 查看统计数据</label>
                            </div>
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-view-reports" class="main-permission" ${permissions.can_view_reports ? 'checked' : ''}> 📋 查看报表</label>
                            </div>
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-export-data" class="main-permission" ${permissions.can_export_data ? 'checked' : ''}> 📤 导出数据</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label style="font-weight: bold; color: #333; margin-bottom: 10px;">⚙️ 系统设置权限 <small style="color: #666;">（系统配置和维护功能）</small></label>
                        <div class="permissions-grid">
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-refresh-permissions" class="main-permission" ${permissions.can_refresh_permissions ? 'checked' : ''}> 🔧 权限刷新</label>
                            </div>
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-backup-restore" class="main-permission" ${permissions.can_backup_restore ? 'checked' : ''}> 💾 备份恢复</label>
                            </div>
                            <div class="permissions-group">
                                <label class="permission-item"><input type="checkbox" id="perm-security-settings" class="main-permission" ${permissions.can_manage_security ? 'checked' : ''}> 🔒 安全设置</label>
                            </div>
                        </div>
                    </div>
                </div>
            `);
            
            // 设置权限联动逻辑
            setupPermissionLogic(modal);
            
            // 初始化权限状态
            initializePermissionStates(modal, permissions);
            
            modal.querySelector('.btn-save').onclick = () => {
                const name = document.getElementById('group-name').value;
                if (!name) {
                    alert('请填写用户组名称');
                    return;
                }
                const permissions = collectPermissions(modal);
                
                updateUserGroup(id, { name, permissions });
                closeModal(modal);
            };
        })
        .catch(error => {
            console.error('Error loading user group:', error);
            showMessage('加载用户组失败', 'error');
        });
}

// 初始化权限状态
function initializePermissionStates(modal, permissions) {
    // 设置主权限的indeterminate状态
    modal.querySelectorAll('.main-permission').forEach(checkbox => {
        const parentId = checkbox.id;
        const subPermissions = modal.querySelectorAll(`[data-parent="${parentId}"] .sub-permission`);
        
        if (subPermissions.length > 0) {
            const allChecked = Array.from(subPermissions).every(cb => cb.checked);
            const anyChecked = Array.from(subPermissions).some(cb => cb.checked);
            
            if (allChecked) {
                checkbox.checked = true;
                checkbox.indeterminate = false;
            } else if (anyChecked) {
                checkbox.checked = false;
                checkbox.indeterminate = true;
            } else {
                checkbox.checked = false;
                checkbox.indeterminate = false;
            }
        }
        });
}

function updateUserGroup(id, groupData) {
    fetch(`/api/user-groups/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(groupData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('用户组更新成功', 'success');
            loadUserGroups();
        } else {
            showMessage(data.error || '更新失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating user group:', error);
        showMessage('更新失败', 'error');
    });
}

function deleteUserGroup(id) {
    const confirmText = window.t ? window.t('confirm_delete_user_group') : '确定要删除这个用户组吗？';
    if (!confirm(confirmText)) return;
    
    fetch(`/api/user-groups/${id}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const successText = window.t ? window.t('user_group_deleted_success') : '用户组删除成功';
            showMessage(successText, 'success');
            loadUserGroups();
        } else {
            const failedText = window.t ? window.t('operation_failed') : '删除失败';
            showMessage(data.error || failedText, 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting user group:', error);
        showMessage('删除失败', 'error');
    });
}

function updateChatbotKB(id, kbData) {
    fetch(`/api/chatbot-kb/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(kbData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('聊天机器人词条更新成功', 'success');
            loadChatbotKB();
        } else {
            showMessage(data.error || '更新失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating chatbot KB:', error);
        showMessage('更新失败', 'error');
    });
}

function deleteChatbotKB(id) {
    const confirmText = window.t ? window.t('confirm_delete_chatbot_kb') : '确定要删除这个聊天机器人词条吗？';
    if (!confirm(confirmText)) return;
    
    fetch(`/api/chatbot-kb/${id}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const successText = window.t ? window.t('chatbot_kb_deleted_success') : '聊天机器人词条删除成功';
            showMessage(successText, 'success');
            loadChatbotKB();
        } else {
            const failedText = window.t ? window.t('operation_failed') : '删除失败';
            showMessage(data.error || failedText, 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting chatbot KB:', error);
        showMessage('删除失败', 'error');
    });
}

// 在页面初始化时定时检查未回复反馈数量
setInterval(checkFeedbackBadge, 60000);

function checkFeedbackBadge() {
    fetch('/api/feedback/unread-count')
        .then(response => response.json())
        .then(data => {
            const badge = document.getElementById('feedback-badge');
            if (badge) {
                badge.textContent = data.count;
                badge.style.display = data.count > 0 ? 'inline' : 'none';
            }
        })
        .catch(error => console.error('Error checking feedback badge:', error));
}

// 全局变量来存储当前的styleState
let globalStyleState = {
    fontFamily: 'Microsoft YaHei',
    fontSize: 'auto',
    bold: false,
    italic: false,
    underline: false,
    hAlign: 'center',
    vAlign: 'center'
};

// 垂直对齐函数
function setVerticalAlign(align) {
    console.log('设置垂直对齐:', align);
    
    // 更新按钮状态
    document.querySelectorAll('.v-align-btn').forEach(b => b.classList.remove('active'));
    const activeBtn = document.querySelector(`[data-v-align='${align}']`);
    if (activeBtn) activeBtn.classList.add('active');
    
    // 直接更新预览容器的垂直对齐
    const previewContent = document.getElementById('label-preview-content');
    if (previewContent && previewContent.parentElement) {
        const vAlignMap = { top: 'flex-start', center: 'center', bottom: 'flex-end' };
        previewContent.parentElement.style.alignItems = vAlignMap[align] || 'center';
        console.log('已更新垂直对齐为:', align);
    }
    
    // 更新预览
    const updatePreview = window.updatePreview;
    if (updatePreview) {
        updatePreview();
    }
}

// 公告管理功能
function showAddAnnouncementModal() {
    const addAnnouncementText = window.t ? window.t('add_announcement') : '添加公告';
    const announcementContentText = window.t ? window.t('announcement_content') : '公告内容';
    const placeholderText = window.t ? window.t('please_enter_announcement') : '请输入公告内容...';

    const modal = createModal(addAnnouncementText, `
        <div class="form-group">
            <label for="announcement-content">${announcementContentText}</label>
            <textarea id="announcement-content" rows="4" required placeholder="${placeholderText}"></textarea>
        </div>
    `);

    modal.querySelector('.btn-save').onclick = () => {
        const content = document.getElementById('announcement-content').value.trim();
        if (!content) {
            const fillContentText = window.t ? window.t('please_fill_content') : '请填写公告内容';
            alert(fillContentText);
            return;
        }
        createAnnouncement({ content });
        closeModal(modal);
    };
}

function createAnnouncement(announcementData) {
    fetch('/api/admin/announcements', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(announcementData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const successText = window.t ? window.t('announcement_created_success') : '公告创建成功';
            showMessage(successText, 'success');
            loadAnnouncements();
        } else {
            const failedText = window.t ? window.t('operation_failed') : '创建失败';
            showMessage(data.error || failedText, 'error');
        }
    })
    .catch(error => {
        console.error('Error creating announcement:', error);
        const failedText = window.t ? window.t('operation_failed') : '创建失败';
        showMessage(failedText, 'error');
    });
}

function editAnnouncement(id) {
    fetch('/api/admin/announcements')
        .then(response => response.json())
        .then(announcements => {
            const announcement = announcements.find(a => a.id === id);
            if (!announcement) {
                showMessage('公告不存在', 'error');
                return;
            }
            
            const modal = createModal('编辑公告', `
                <div class="form-group">
                    <label for="announcement-content">公告内容</label>
                    <textarea id="announcement-content" rows="4" required placeholder="请输入公告内容...">${announcement.content}</textarea>
                </div>
            `);
            
            modal.querySelector('.btn-save').onclick = () => {
                const content = document.getElementById('announcement-content').value.trim();
                if (!content) {
                    alert('请填写公告内容');
                    return;
                }
                updateAnnouncement(id, { content });
                closeModal(modal);
            };
        })
        .catch(error => {
            console.error('Error loading announcement:', error);
            showMessage('加载公告失败', 'error');
        });
}

function updateAnnouncement(id, announcementData) {
    fetch(`/api/admin/announcements/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(announcementData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('公告更新成功', 'success');
            loadAnnouncements();
        } else {
            showMessage(data.error || '更新失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating announcement:', error);
        showMessage('更新失败', 'error');
    });
}

function deleteAnnouncement(id) {
    if (!confirm('确定要删除这个公告吗？')) return;
    
    fetch(`/api/admin/announcements/${id}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('公告删除成功', 'success');
            loadAnnouncements();
        } else {
            showMessage(data.error || '删除失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting announcement:', error);
        showMessage('删除失败', 'error');
    });
}

// 反馈管理功能
function replyToFeedback(id) {
    fetch('/api/feedback')
        .then(response => response.json())
        .then(feedback => {
            const item = feedback.find(f => f.id === id);
            if (!item) {
                showMessage('反馈不存在', 'error');
                return;
            }
            
    const modal = createModal('回复反馈', `
                <div class="form-group">
                    <label>原反馈内容：</label>
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-bottom: 15px;">
                        <strong>标题：</strong>${item.title || '无标题'}<br>
                        <strong>内容：</strong>${item.content}
                    </div>
                </div>
        <div class="form-group">
            <label for="feedback-reply">回复内容</label>
                    <textarea id="feedback-reply" rows="4" required placeholder="请输入回复内容...">${item.reply || ''}</textarea>
        </div>
    `);
    
    modal.querySelector('.btn-save').onclick = () => {
                const reply = document.getElementById('feedback-reply').value.trim();
        if (!reply) {
            alert('请填写回复内容');
            return;
        }
        submitFeedbackReply(id, { reply });
        closeModal(modal);
    };
        })
        .catch(error => {
            console.error('Error loading feedback:', error);
            showMessage('加载反馈失败', 'error');
        });
}

function submitFeedbackReply(id, replyData) {
    fetch(`/api/feedback/${id}/reply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(replyData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('回复成功', 'success');
            loadFeedback();
        } else {
            showMessage(data.error || '回复失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error replying to feedback:', error);
        showMessage('回复失败', 'error');
    });
}

function deleteFeedback(id) {
    if (!confirm('确定要删除这个反馈吗？')) return;
    
    fetch(`/api/feedback/${id}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('反馈删除成功', 'success');
            loadFeedback();
        } else {
            showMessage(data.error || '删除失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting feedback:', error);
        showMessage('删除失败', 'error');
    });
}

// 权限控制：各后台模块无权限时显示无权限提示
function showNoPermission(sectionId, colCount = 3, moduleName = '') {
    const noPermissionText = window.t ? window.t('no_permission_view') : '无权限查看';
    const tbody = document.querySelector(`#${sectionId}-table tbody`);
    if (tbody) tbody.innerHTML = `<tr><td colspan="${colCount}" class="empty-state">${noPermissionText}${moduleName ? ' ' + moduleName : ''}</td></tr>`;
    const addBtn = document.querySelector(`#${sectionId} .btn-add`);
    if (addBtn) addBtn.style.display = 'none';
}

// 修改各模块数据加载函数
function loadDepartments() {
    if (!checkPermission('can_manage_departments')) {
        const departmentManagementText = window.t ? window.t('department_management') : '部门管理';
        showNoPermission('departments', 3, departmentManagementText);
        return;
    }
    const tableBody = document.getElementById('departments-table-body');
    if (!tableBody) return;
    const loadingText = window.t ? window.t('loading') : '加载中...';
    tableBody.innerHTML = `<tr><td colspan="3">${loadingText}</td></tr>`;
    
    fetch('/api/departments')
        .then(response => {
            if (!response.ok) {
                throw new Error(`网络错误: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (!Array.isArray(data)) {
                console.error("加载部门失败: 响应不是一个数组", data);
                const loadingFailedText = window.t ? window.t('loading_departments_failed') : '加载部门失败：数据格式错误。';
                tableBody.innerHTML = `<tr><td colspan="3" class="empty-state">${loadingFailedText}</td></tr>`;
                return;
            }

            if (data.length === 0) {
                const noDepartmentDataText = window.t ? window.t('no_department_data') : '暂无部门数据';
                tableBody.innerHTML = `<tr><td colspan="3" class="empty-state">${noDepartmentDataText}</td></tr>`;
                return;
            }
            
            tableBody.innerHTML = '';
            data.forEach(department => {
                const row = document.createElement('tr');
                
                // 根据权限生成操作按钮
                const actionButtons = [];
                if (checkPermission('can_manage_departments_edit')) {
                    const editText = window.t ? window.t('edit') : '编辑';
                    actionButtons.push(`<button onclick="editDepartment(${department.id})">${editText}</button>`);
                }
                if (checkPermission('can_manage_departments_delete')) {
                    const deleteText = window.t ? window.t('delete') : '删除';
                    actionButtons.push(`<button onclick="deleteDepartment(${department.id})">${deleteText}</button>`);
                }
                
                row.innerHTML = `
                    <td>${department.id}</td>
                    <td>${escapeHTML(department.name)}</td>
                    <td>
                        ${actionButtons.join('')}
                    </td>
                `;
                tableBody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('加载部门失败:', error);
            const loadingFailedText = window.t ? window.t('loading_departments_failed') : '加载部门失败。';
            tableBody.innerHTML = `<tr><td colspan="3" class="empty-state">${loadingFailedText}</td></tr>`;
        });
}

function loadUserGroups() {
    if (!checkPermission('can_manage_user_groups')) {
        const userGroupManagementText = window.t ? window.t('user_group_management') : '用户组管理';
        showNoPermission('user-groups', 3, userGroupManagementText);
        return;
    }
    const tbody = document.querySelector('#user-groups-table tbody');
    if (!tbody) return;
    const loadingText = window.t ? window.t('loading') : '加载中...';
    tbody.innerHTML = `<tr><td colspan="4" class="empty-state">${loadingText}</td></tr>`;

    // 权限名称映射 - 更新为包含所有新权限
    const permissionNames = {
        // 后台管理权限
        'can_manage_backend': '后台管理入口',
        'can_manage_users': '用户管理',
        'can_manage_users_add': '添加用户',
        'can_manage_users_edit': '编辑用户',
        'can_manage_users_delete': '删除用户',
        'can_manage_departments': '部门管理',
        'can_manage_departments_add': '添加部门',
        'can_manage_departments_edit': '编辑部门',
        'can_manage_departments_delete': '删除部门',
        'can_manage_user_groups': '用户组管理',
        'can_manage_user_groups_add': '添加用户组',
        'can_manage_user_groups_edit': '编辑用户组',
        'can_manage_user_groups_delete': '删除用户组',
        'can_manage_labels': '标签管理',
        'can_manage_labels_add': '添加标签',
        'can_manage_labels_edit': '编辑标签',
        'can_manage_labels_delete': '删除标签',
        'can_manage_announcements': '公告管理',
        'can_manage_announcements_add': '添加公告',
        'can_manage_announcements_edit': '编辑公告',
        'can_manage_announcements_delete': '删除公告',
        'can_view_feedback': '查看反馈',
        'can_reply_feedback': '回复反馈',
        'can_delete_feedback': '删除反馈',
        'can_view_audit_log': '操作日志',
        'can_clear_audit_log': '清空操作日志',
        'can_manage_barcode_prefixes': '条形码前缀管理',
        'can_manage_barcode_prefixes_add': '添加条形码前缀',
        'can_manage_barcode_prefixes_edit': '编辑条形码前缀',
        'can_manage_barcode_prefixes_delete': '删除条形码前缀',
        'can_manage_chatbot_kb': '聊天机器人词库管理',
        'can_manage_chatbot_kb_add': '添加聊天机器人词条',
        'can_manage_chatbot_kb_edit': '编辑聊天机器人词条',
        'can_manage_chatbot_kb_delete': '删除聊天机器人词条',
        
        // 前台功能权限
        'can_view_problem_solve': '显示Problem Solve按钮',
        'can_reply_problem_solve': 'Problem Solve回复权限',
        'can_delete_problem_solve': 'Problem Solve删除权限',
        'can_print_labels': '标签打印权限',
        'can_search_labels': '标签搜索权限',
        'can_copy_labels': '标签复制权限',
        
        // 数据查看权限
        'can_view_statistics': '查看统计数据',
        'can_view_reports': '查看报表',
        'can_export_data': '导出数据',
        
        // 系统设置权限
        'can_refresh_permissions': '权限刷新',
        'can_backup_restore': '备份恢复',
        'can_manage_security': '安全设置',
        'can_manage_system_settings': '系统设定'
    };

    fetch('/api/user-groups')
        .then(response => {
            if (!response.ok) {
                throw new Error('无法加载用户组');
            }
            return response.json();
        })
        .then(groups => {
            if (!Array.isArray(groups)) {
                console.error("加载用户组失败: 响应不是一个数组", groups);
                const loadingFailedText = window.t ? window.t('loading_user_groups_failed') : '加载用户组失败：数据格式错误。';
                tbody.innerHTML = `<tr><td colspan="4" class="empty-state">${loadingFailedText}</td></tr>`;
                return;
            }
            if (groups.length === 0) {
                const noUserGroupsDataText = window.t ? window.t('no_user_groups_data') : '暂无用户组数据';
                tbody.innerHTML = `<tr><td colspan="4" class="empty-state">${noUserGroupsDataText}</td></tr>`;
                return;
            }

            tbody.innerHTML = groups.map(group => {
                const noPermissionText = window.t ? window.t('no_permission') : '无权限';
                let permissions = noPermissionText;
                if (group.permissions) {
                    const activePermissions = Object.keys(group.permissions)
                        .filter(p => group.permissions[p])
                        .map(p => permissionNames[p] || p);
                    permissions = activePermissions.length > 0 ? activePermissions.join(', ') : noPermissionText;
                }
                
                return `
                    <tr>
                        <td>${group.id}</td>
                        <td>${escapeHTML(group.name)}</td>
                        <td>${escapeHTML(permissions)}</td>
                        <td>
                            <button onclick="editUserGroup(${group.id})">${window.t ? window.t('edit') : '编辑'}</button>
                            <button onclick="deleteUserGroup(${group.id})">${window.t ? window.t('delete') : '删除'}</button>
                        </td>
                    </tr>
                `;
            }).join('');
        })
        .catch(error => {
            console.error('加载用户组失败:', error);
            const loadingFailedText = window.t ? window.t('loading_user_groups_failed') : '加载用户组失败';
            tbody.innerHTML = `<tr><td colspan="4" class="empty-state">${loadingFailedText}: ${error.message}</td></tr>`;
        });
}

function loadLabels() {
    if (!checkPermission('can_manage_labels')) {
        showNoPermission('labels', 3, '标签管理');
        return;
    }
    const tableBody = document.getElementById('labels-table-body');
    if (!tableBody) return;
    tableBody.innerHTML = `<tr><td colspan="9" class="empty-state">正在加载...</td></tr>`;
    const filter = document.getElementById('label-type-filter');
    const selectedType = filter ? filter.value : 'all';
    fetch('/api/labels')
        .then(response => response.json())
        .then(data => {
            const filteredData = selectedType === 'all' 
                ? data 
                : data.filter(label => label.type === selectedType);
            if (filteredData.length === 0) {
                tableBody.innerHTML = `<tr><td colspan="9" class="empty-state">没有找到符合条件的标签。</td></tr>`;
                return;
            }
            tableBody.innerHTML = '';
            filteredData.forEach(label => {
                const row = document.createElement('tr');
                const renderedContent = renderLabelContent(label.content || '', label.v_align || 'center');
                
                // 根据权限生成操作按钮
                const actionButtons = [];
                if (checkPermission('can_manage_labels_edit')) {
                    actionButtons.push(`<button onclick="editLabel(${label.id})">编辑</button>`);
                }
                if (checkPermission('can_manage_labels_delete')) {
                    actionButtons.push(`<button onclick="deleteLabel(${label.id})">删除</button>`);
                }
                
                row.innerHTML = `
                    <td>${label.id}</td>
                    <td>${escapeHTML(label.name)}</td>
                    <td>${escapeHTML(label.type)}</td>
                    <td class="content-cell-preview">
                        <div class="table-preview-wrapper">
                            <div class="table-preview-content">
                                ${renderedContent}
                            </div>
                        </div>
                    </td>
                    <td>${label.print_prompt ? '是' : '否'}</td>
                    <td>${label.print_quantity || 1}</td>
                    <td>${escapeHTML(label.copy_info || '')}</td>
                    <td>${escapeHTML(label.department_names || 'N/A')}</td>
                    <td>
                        ${actionButtons.join('')}
                    </td>
                `;
                tableBody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('Error fetching labels:', error);
            tableBody.innerHTML = `<tr><td colspan="9" class="empty-state">加载标签失败。</td></tr>`;
        });
}

function loadFeedback() {
    if (!checkPermission('can_view_feedback')) {
        const feedbackViewText = window.t ? window.t('feedback_view') : '反馈查看';
        showNoPermission('feedback', 3, feedbackViewText);
        return;
    }
    const tbody = document.querySelector('#feedback-table tbody');
    if (!tbody) return;

    const loadingText = window.t ? window.t('loading') : '加载中...';
    tbody.innerHTML = `<tr><td colspan="7" class="empty-state">${loadingText}</td></tr>`;
    
    fetch('/api/feedback')
        .then(response => response.json())
        .then(feedback => {
            if (feedback.length === 0) {
                const noFeedbackDataText = window.t ? window.t('no_feedback_data') : '暂无反馈数据';
                tbody.innerHTML = `<tr><td colspan="7" class="empty-state">${noFeedbackDataText}</td></tr>`;
                return;
            }
            
            tbody.innerHTML = feedback.map(item => {
                // 根据权限生成操作按钮
                const actionButtons = [];
                if (checkPermission('can_reply_feedback')) {
                    const replyText = window.t ? window.t('reply') : '回复';
                    actionButtons.push(`<button class="btn-edit" onclick="replyToFeedback(${item.id})">${replyText}</button>`);
                }
                if (checkPermission('can_delete_feedback')) {
                    const deleteText = window.t ? window.t('delete') : '删除';
                    actionButtons.push(`<button class="btn-delete" onclick="deleteFeedback(${item.id})">${deleteText}</button>`);
                }
                
                return `
                <tr>
                    <td>${item.id}</td>
                    <td>${item.user_name || ''}</td>
                    <td>${item.title || ''}</td>
                    <td>${item.content ? item.content.substring(0, 50) + (item.content.length > 50 ? '...' : '') : ''}</td>
                    <td>${formatDate(item.created_at)}</td>
                    <td>${item.reply || (window.t ? window.t('not_replied') : '未回复')}</td>
                    <td>
                            ${actionButtons.join('')}
                    </td>
                </tr>
                `;
            }).join('');
        })
        .catch(error => {
            console.error('Error loading feedback:', error);
            const loadingFailedText = window.t ? window.t('loading_failed') : '加载失败';
            tbody.innerHTML = `<tr><td colspan="7" class="empty-state">${loadingFailedText}</td></tr>`;
        });
} 

// 在页面加载后添加刷新权限按钮
window.addEventListener('DOMContentLoaded', function() {
    const headerRight = document.querySelector('.header-right');
    if (headerRight && !document.getElementById('refresh-permissions-btn') && window.userPermissions && window.userPermissions.can_refresh_permissions) {
        const refreshBtn = document.createElement('button');
        refreshBtn.id = 'refresh-permissions-btn';
        const refreshPermissionsText = window.t ? window.t('refresh') : '刷新权限';
        refreshBtn.textContent = refreshPermissionsText;
        refreshBtn.className = 'btn-refresh';
        refreshBtn.onclick = function() {
            fetch('/api/refresh-permissions', { method: 'POST' })
                .then(res => res.json())
                .then(data => {
                    if (data.success) {
                        window.userPermissions = data.permissions;
                        const permissionsRefreshedText = window.t ? window.t('permissions_refreshed') : '权限已刷新';
                        showMessage(permissionsRefreshedText, 'success');
                        location.reload();
                    } else {
                        const refreshFailedText = window.t ? window.t('refresh_failed') : '刷新失败';
                        showMessage(data.error || refreshFailedText, 'error');
                    }
                })
                .catch(() => {
                    const refreshFailedText = window.t ? window.t('refresh_failed') : '刷新失败';
                    showMessage(refreshFailedText, 'error');
                });
        };
        headerRight.appendChild(refreshBtn);
    }
});

// 在 updateUserGroup 和 createUserGroup 成功回调后，自动刷新权限
function afterUserGroupSave(groupId, permissions) {
    // 判断当前用户是否属于被编辑的用户组，并且勾选了 can_refresh_permissions
    if (window.userInfo && window.userInfo.group_id === groupId && permissions.can_refresh_permissions) {
        fetch('/api/refresh-permissions', { method: 'POST' })
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    window.userPermissions = data.permissions;
                    const permissionsRefreshedText = window.t ? window.t('permissions_refreshed') : '权限已刷新';
                    showMessage(permissionsRefreshedText, 'success');
                    location.reload();
                }
            });
    }
}
// 在 updateUserGroup 和 createUserGroup 的成功回调里调用 afterUserGroupSave

// 本地表格搜索功能
function setupAdminTableSearch() {
    // 标签管理
    const labelInput = document.getElementById('search-labels');
    if (labelInput) {
        if (!checkPermission('can_manage_labels')) {
            labelInput.parentNode && labelInput.parentNode.removeChild(labelInput);
        } else {
            labelInput.addEventListener('input', function() {
                const val = labelInput.value.trim().toLowerCase();
                const rows = document.querySelectorAll('#labels-table tbody tr');
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    row.style.display = val === '' || text.includes(val) ? '' : 'none';
                });
            });
        }
    }
    // 用户管理
    const userInput = document.getElementById('search-users');
    if (userInput) {
        if (!checkPermission('can_manage_users')) {
            userInput.parentNode && userInput.parentNode.removeChild(userInput);
        } else {
            userInput.addEventListener('input', function() {
                const val = userInput.value.trim().toLowerCase();
                const rows = document.querySelectorAll('#users-table tbody tr');
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    row.style.display = val === '' || text.includes(val) ? '' : 'none';
                });
            });
        }
    }
    // 部门管理
    const deptInput = document.getElementById('search-departments');
    if (deptInput) {
        if (!checkPermission('can_manage_departments')) {
            deptInput.parentNode && deptInput.parentNode.removeChild(deptInput);
        } else {
            deptInput.addEventListener('input', function() {
                const val = deptInput.value.trim().toLowerCase();
                const rows = document.querySelectorAll('#departments-table tbody tr');
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    row.style.display = val === '' || text.includes(val) ? '' : 'none';
                });
            });
        }
    }
}
window.addEventListener('DOMContentLoaded', setupAdminTableSearch);