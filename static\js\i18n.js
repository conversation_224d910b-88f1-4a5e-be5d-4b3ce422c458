/**
 * 国际化多语言支持模块
 */

// 语言配置
const languages = {
    'en': {
        name: 'English',
        flag: '🇺🇸',
        translations: {
            // 通用
            'welcome': 'Welcome',
            'logout': 'Logout',
            'login': 'Login',
            'cancel': 'Cancel',
            'confirm': 'Confirm',
            'save': 'Save',
            'delete': 'Delete',
            'edit': 'Edit',
            'add': 'Add',
            'search': 'Search',
            'refresh': 'Refresh',
            'close': 'Close',
            'submit': 'Submit',
            'loading': 'Loading...',
            'success': 'Success',
            'error': 'Error',
            'warning': 'Warning',
            'info': 'Information',
            'yes': 'Yes',
            'no': 'No',
            
            // 页面标题
            'page_title': 'Label Printing System',
            'admin_title': 'Admin Panel - Label Printing System',
            'problem_solve_title': 'Problem Solve',
            
            // 头部导航
            'label_printing': 'Smart Labels',
            'admin_panel': 'Admin Panel',
            'feedback': 'Feedback',
            'language': 'Language',
            
            // 搜索
            'search_placeholder': 'Enter label name or keywords to search...',
            'no_results': 'No results found',
            
            // 聊天
            'chat': 'Chat',
            'robot_chat': 'Robot Chat',
            'public_chat': 'Public Chat',
            'send_message': 'Send Message',
            'type_message': 'Type a message...',
            'chat_permission_denied': 'You do not have chat permission',
            'robot_no_answer': 'Sorry, I cannot answer this question.',

            // 知识分享
            'share_knowledge': 'Share Knowledge',
            'share_my_knowledge': 'Share my knowledge.',
            'knowledge_example': 'Example: Question(Keyword)=Answer',
            'knowledge_example_detail': 'What is your name?(your name)=My name is Robot.',
            'question_keyword': 'Question(Keyword):',
            'answer': 'Answer:',
            'question_placeholder': 'Enter your question or keyword...',
            'answer_placeholder': 'Enter the answer...',
            'knowledge_submitted': 'Knowledge submitted for review successfully!',
            'review_knowledge': 'Review Knowledge Submissions',
            'no_submissions': 'No pending submissions',
            'submitted_by': 'Submitted by',
            'question': 'Question',
            'approve': 'Approve',
            'reject': 'Reject',
            'submission_reviewed': 'Submission reviewed successfully',
            'submission_approved': 'Submission approved and added to knowledge base!',
            'submission_rejected': 'Request rejected',
            'review_failed': 'Review failed',
            'fill_all_fields': 'Please fill in all required fields',
            'submit_failed': 'Submission failed',
            'load_failed': 'Failed to load submissions',

            // Registration
            'sign_up': 'Sign Up',
            'user_registration': 'User Registration',
            'badge_id': 'Badge ID',
            'badge_placeholder': 'Enter badge ID (min 5 digits)',
            'badge_format': 'Badge ID must be at least 5 digits',
            'full_name': 'Full Name',
            'name_placeholder': 'Enter your full name',
            'login_code': 'Login Code',
            'login_placeholder': 'Enter login code',
            'department': 'Department',
            'select_department': 'Select Department',
            'password': 'Password',
            'optional': 'Optional',
            'password_placeholder': 'Leave empty for no password',
            'password_hint': 'Leave empty to login without password',
            'cancel': 'Cancel',
            'register': 'Register',
            'registering': 'Registering...',
            'fill_required_fields': 'Please fill in all required fields.',
            'invalid_badge_format': 'Badge ID must be at least 5 digits.',
            'registration_success': 'Registration successful! You can now login.',
            'registration_failed': 'Registration failed',

            // Enhanced Login
            'enter_password': 'Enter Password',
            'enter_card_id': 'Please enter your card ID',
            'user_not_found': 'User not found',
            'check_failed': 'Failed to check user',
            'welcome': 'Welcome',
            'password_required': 'Password required to continue',
            'logging_in': 'Logging you in...',
            'login': 'Login',
            'continue': 'Continue',
            'invalid_password': 'Invalid password',
            'password_error_limit_reached': 'Password error limit reached (3 attempts). You can choose to change your password:',
            'change_password': 'Change Password',
            'too_many_failed_attempts': 'Too many failed attempts. Please reset your password or contact administrator.',

            // System Settings
            'open_registration_settings': 'Open Registration Settings',
            'open_registration_description': 'Control whether users can register accounts independently',
            'enable_open_registration': 'Enable Open Registration',
            'chat_retention_settings': 'Chat Record Retention Settings',
            'chat_retention_description': 'Set the retention time for public chat records, records exceeding this time will be automatically deleted',
            'save_settings': 'Save Settings',
            'reset': 'Reset',

            // Password Management
            'change_password': 'Change Password',
            'new_password': 'New Password',
            'new_password_placeholder': 'Leave empty to remove password',
            'password_help': 'Leave empty to remove password',
            'save_changes': 'Save Changes',
            'cancel': 'Cancel',
            'password_changed_success': 'Password changed successfully',
            'password_removed_success': 'Password removed successfully',
            'password_change_failed': 'Failed to change password',

            // Password Reset
            'reset_password': 'Reset Password',
            'contact_admin': 'Contact Administrator',
            'reset_password_instruction': 'Please fill in the following information. All information must match user data 100% to reset password.',
            'badge': 'Badge',
            'full_name': 'Full Name',
            'login_code': 'Login',
            'department': 'Department',
            'password_reset_success': 'Password has been cleared, please login directly!',
            'password_reset_updated_success': 'Identity verified! Password has been updated successfully.',
            'verification_failed': 'Verification Failed, Please try again or contact Administrator!',
            'reset_failed': 'Reset failed, please check if the information is correct',
            'contact_admin_info': 'Please contact the system administrator to reset your password.',
            'login_failed': 'Login failed',

            // Registration Validation
            'badge_too_short': 'Badge ID must be at least 5 digits',
            'badge_numbers_only': 'Badge ID must contain only numbers',
            'badge_already_exists': 'This Badge ID is already registered',
            'badge_available': 'Badge ID is available',
            'login_too_short': 'Login code must be at least 2 characters',
            'login_already_exists': 'This Login code is already registered',
            'login_available': 'Login code is available',
            
            // 标签
            'labels': 'Labels',
            'label_name': 'Label Name',
            'label_type': 'Label Type',
            'label_content': 'Label Content',
            'print_quantity': 'Print Quantity',
            'copy_info': 'Copy Info',
            'label_code': 'Label Code',
            
            // 用户管理
            'users': 'Users',
            'user_management': 'User Management',
            'employee_id': 'Badge ID',
            'name': 'Name',
            'code': 'Login',
            'department': 'Department',
            'user_group': 'User Group',
            'actions': 'Actions',
            
            // 部门管理
            'departments': 'Departments',
            'department_management': 'Department Management',
            'department_name': 'Department Name',
            'department_code': 'Department Code',
            'department_head': 'Department Head',
            
            // 权限
            'permissions': 'Permissions',
            'no_permission': 'No Permission',
            'permission_denied': 'Permission Denied',
            
            // 表单验证
            'required_field': 'This field is required',
            'invalid_format': 'Invalid format',
            'confirm_delete': 'Are you sure you want to delete this item?',
            
            // 通知
            'notification': 'Notification',
            'new_notification': 'New Notification',
            'notification_center': 'Notification Center',
            'notification_list': 'Notification List',
            'no_notifications': 'No notifications',
            'mark_as_read': 'Mark as read',
            'notifications_marked_read': 'notifications marked as read',
            'mark_read_failed': 'Failed to mark as read, please try again',
            'fetch_notifications_failed': 'Failed to fetch notifications',
            
            // 反馈
            'feedback_title': 'Feedback',
            'submit_feedback': 'Submit Feedback',
            'feedback_content': 'Feedback Content',

            // Problem Solve
            'problem_solve_center': '🔧 Problem Solve Center',
            'problem_solve_description': 'View and manage user submitted problem feedback',
            'refresh_list': '🔄 Refresh List',
            'problem_title': 'Problem',
            'submitted_by': 'Submitted By',
            'problem_content': 'Problem Content',
            'submitted_time': 'Submitted Time',
            'replied': 'Replied',
            'reply_problem': 'Reply Problem',
            'original_problem': 'Original Problem:',
            'reply_content': 'Reply Content:',
            'reply_placeholder': 'Please enter your reply...',
            'submit_reply': 'Submit Reply',
            'enter_reply_content': 'Please enter reply content',
            'reply_submitted': 'Reply submitted',
            'reply_failed': 'Reply failed',
            'reply_failed_retry': 'Reply failed, please try again',
            'reply_success': 'Reply successful!',
            'unknown_error': 'Unknown error',
            'confirm_delete_problem': 'Are you sure you want to delete this problem record?',
            'delete_success': 'Delete successful',
            'delete_failed': 'Delete failed',
            'no_problem_records': 'No problem records',
            'load_failed': 'Load failed',
            'back_to_home': 'Back to Home',

            // Admin
            'admin_system_title': 'Label Printing System - Admin Panel',
            'label_management': 'Label Management',
            'category_center': 'Category Center',
            'user_group_management': 'User Group Management',
            'announcement_management': 'Announcement Management',
            'feedback_view': 'Feedback View',
            'barcode_prefix_management': 'Barcode Prefix Management',
            'chatbot_kb_management': 'Chatbot Knowledge Base',
            'audit_log': 'Audit Log',
            'system_settings': 'System Settings',
            'search_labels': 'Search labels...',
            'add_label': 'Add Label',
            'filter_by_category': 'Filter by Category:',
            'language': 'Language',
            'add_category_code': 'Add Category Code',
            'search_category_codes': 'Search category codes...',

            // Admin common
            'loading': 'Loading...',
            'no_permission': 'No Permission',
            'no_permission_view': 'No permission to view',
            'edit': 'Edit',
            'delete': 'Delete',
            'yes': 'Yes',
            'no': 'No',
            'loading_failed': 'Loading failed',
            'no_data': 'No data available',
            'operation_success': 'Operation successful',
            'operation_failed': 'Operation failed',
            'confirm_delete_item': 'Are you sure you want to delete this item?',
            'please_fill_content': 'Please fill in the content',
            'please_enter_valid_numbers': 'Please enter valid row and column numbers',
            'insert_table_rows': 'Please enter table rows:',
            'insert_table_cols': 'Please enter table columns:',
            'insert_nested_table_rows': 'Please enter nested table rows:',
            'insert_nested_table_cols': 'Please enter nested table columns:',

            // Labels
            'no_labels_found': 'No labels found matching the criteria.',
            'loading_labels_failed': 'Failed to load labels.',
            'label_updated_success': 'Label updated successfully',
            'label_deleted_success': 'Label deleted successfully',
            'label_created_success': 'Label created successfully',

            // Users
            'user_updated_success': 'User updated successfully',
            'user_deleted_success': 'User deleted successfully',
            'user_created_success': 'User created successfully',

            // Departments
            'loading_departments_failed': 'Loading departments failed',
            'no_department_data': 'No department data',
            'department_updated_success': 'Department updated successfully',
            'department_deleted_success': 'Department deleted successfully',
            'department_created_success': 'Department created successfully',

            // User Groups
            'loading_user_groups_failed': 'Failed to load user groups',
            'no_user_groups_data': 'No user groups data',
            'user_group_updated_success': 'User group updated successfully',
            'user_group_deleted_success': 'User group deleted successfully',
            'user_group_created_success': 'User group created successfully',
            'confirm_delete_user_group': 'Are you sure you want to delete this user group?',

            // Announcements
            'add_announcement': 'Add Announcement',
            'announcement_content': 'Announcement Content',
            'please_enter_announcement': 'Please enter announcement content...',
            'announcement_created_success': 'Announcement created successfully',
            'announcement_updated_success': 'Announcement updated successfully',
            'announcement_deleted_success': 'Announcement deleted successfully',
            'announcement_not_exist': 'Announcement does not exist',
            'edit_announcement': 'Edit Announcement',
            'confirm_delete_announcement': 'Are you sure you want to delete this announcement?',
            'loading_announcement_failed': 'Failed to load announcement',

            // Feedback
            'reply_feedback': 'Reply to Feedback',
            'original_feedback_content': 'Original feedback content:',
            'feedback_title': 'Title:',
            'feedback_content_label': 'Content:',
            'no_title': 'No title',
            'reply_content_label': 'Reply Content',
            'please_enter_reply': 'Please enter reply content...',
            'feedback_not_exist': 'Feedback does not exist',
            'reply_success': 'Reply successful',
            'feedback_deleted_success': 'Feedback deleted successfully',
            'confirm_delete_feedback': 'Are you sure you want to delete this feedback?',
            'loading_feedback_failed': 'Failed to load feedback',
            'no_feedback_data': 'No feedback data',
            'not_replied': 'Not replied',

            // Chatbot KB
            'chatbot_kb_updated_success': 'Chatbot knowledge base updated successfully',
            'chatbot_kb_deleted_success': 'Chatbot knowledge base deleted successfully',
            'confirm_delete_chatbot_kb': 'Are you sure you want to delete this chatbot knowledge base entry?',

            // Permissions
            'permissions_refreshed': 'Permissions refreshed',
            'refresh_failed': 'Refresh failed',
            
            // 公告
            'announcements': 'Announcements',
            'announcement_title': 'Announcement Title',
            'announcement_content': 'Announcement Content',
            
            // 错误消息
            'network_error': 'Network error, please try again',
            'server_error': 'Server error, please contact administrator',
            'login_required': 'Please login first',
            'operation_failed': 'Operation failed',
            'operation_success': 'Operation successful',
            
            // 时间
            'today': 'Today',
            'yesterday': 'Yesterday',
            'this_week': 'This Week',
            'this_month': 'This Month',
            
            // 状态
            'active': 'Active',
            'inactive': 'Inactive',
            'pending': 'Pending',
            'completed': 'Completed',
            'cancelled': 'Cancelled',

            // 额外的翻译
            'no_problem_records': 'No problem records',
            'replied': 'Replied',
            'reply': 'Reply',
            'admin_system_title': 'Label Printing System - Admin Panel'
        }
    },
    'es': {
        name: 'Español',
        flag: '🇪🇸',
        translations: {
            // 通用
            'welcome': 'Bienvenido',
            'logout': 'Cerrar Sesión',
            'login': 'Iniciar Sesión',
            'cancel': 'Cancelar',
            'confirm': 'Confirmar',
            'save': 'Guardar',
            'delete': 'Eliminar',
            'edit': 'Editar',
            'add': 'Agregar',
            'search': 'Buscar',
            'refresh': 'Actualizar',
            'close': 'Cerrar',
            'submit': 'Enviar',
            'loading': 'Cargando...',
            'success': 'Éxito',
            'error': 'Error',
            'warning': 'Advertencia',
            'info': 'Información',
            'yes': 'Sí',
            'no': 'No',
            
            // 页面标题
            'page_title': 'Sistema de Sistema de Etiquetado',
            'admin_title': 'Panel de Administración - Sistema de Sistema de Etiquetado',
            'problem_solve_title': 'Resolver Problemas',
            
            // 头部导航
            'label_printing': 'Sistema de Etiquetado',
            'admin_panel': 'Panel de Administración',
            'feedback': 'Comentarios',
            'language': 'Idioma',
            
            // 搜索
            'search_placeholder': 'Ingrese nombre de etiqueta o palabras clave para buscar...',
            'no_results': 'No se encontraron resultados',
            
            // 聊天
            'chat': 'Chat',
            'robot_chat': 'Chat Robot',
            'public_chat': 'Chat Público',
            'send_message': 'Enviar Mensaje',
            'type_message': 'Escriba un mensaje...',
            'chat_permission_denied': 'No tiene permisos de chat',
            'robot_no_answer': 'Lo siento, no puedo responder esta pregunta.',

            // 知识分享
            'share_knowledge': 'Compartir Conocimiento',
            'share_my_knowledge': 'Compartir mi conocimiento.',
            'knowledge_example': 'Ejemplo: Pregunta(Palabra clave)=Respuesta',
            'knowledge_example_detail': '¿Cuál es tu nombre?(tu nombre)=Mi nombre es Robot.',
            'question_keyword': 'Pregunta(Palabra clave):',
            'answer': 'Respuesta:',
            'question_placeholder': 'Ingrese su pregunta o palabra clave...',
            'answer_placeholder': 'Ingrese la respuesta...',
            'knowledge_submitted': '¡Conocimiento enviado para revisión exitosamente!',
            'review_knowledge': 'Revisar Envíos de Conocimiento',
            'no_submissions': 'No hay envíos pendientes',
            'submitted_by': 'Enviado por',
            'question': 'Pregunta',
            'approve': 'Aprobar',
            'reject': 'Rechazar',
            'submission_reviewed': 'Envío revisado exitosamente',
            'submission_approved': '¡Envío aprobado y agregado a la base de conocimientos!',
            'submission_rejected': 'Solicitud rechazada',
            'review_failed': 'Revisión falló',
            'fill_all_fields': 'Por favor complete todos los campos requeridos',
            'submit_failed': 'Envío falló',
            'load_failed': 'Error al cargar envíos',

            // Registration
            'sign_up': 'Registrarse',
            'user_registration': 'Registro de Usuario',
            'badge_id': 'ID de Tarjeta',
            'badge_placeholder': 'Ingrese ID de tarjeta (mín 5 dígitos)',
            'badge_format': 'El ID de tarjeta debe tener al menos 5 dígitos',
            'full_name': 'Nombre Completo',
            'name_placeholder': 'Ingrese su nombre completo',
            'login_code': 'Código de Acceso',
            'login_placeholder': 'Ingrese código de acceso',
            'department': 'Departamento',
            'select_department': 'Seleccionar Departamento',
            'password': 'Contraseña',
            'optional': 'Opcional',
            'password_placeholder': 'Dejar vacío para no usar contraseña',
            'password_hint': 'Dejar vacío para acceder sin contraseña',
            'cancel': 'Cancelar',
            'register': 'Registrar',
            'registering': 'Registrando...',
            'fill_required_fields': 'Por favor complete todos los campos requeridos.',
            'invalid_badge_format': 'El ID de tarjeta debe tener al menos 5 dígitos.',
            'registration_success': '¡Registro exitoso! Ya puede iniciar sesión.',
            'registration_failed': 'Registro falló',

            // Enhanced Login
            'enter_password': 'Ingrese Contraseña',
            'enter_card_id': 'Por favor ingrese su ID de tarjeta',
            'user_not_found': 'Usuario no encontrado',
            'check_failed': 'Error al verificar usuario',
            'welcome': 'Bienvenido',
            'password_required': 'Contraseña requerida para continuar',
            'logging_in': 'Iniciando sesión...',
            'login': 'Iniciar Sesión',
            'continue': 'Continuar',
            'invalid_password': 'Contraseña inválida',
            'password_error_limit_reached': 'Límite de errores de contraseña alcanzado (3 intentos). Puede elegir cambiar su contraseña:',
            'change_password': 'Cambiar Contraseña',
            'too_many_failed_attempts': 'Demasiados intentos fallidos. Por favor restablezca su contraseña o contacte al administrador.',

            // System Settings
            'open_registration_settings': 'Configuración de Registro Abierto',
            'open_registration_description': 'Controlar si los usuarios pueden registrar cuentas de forma independiente',
            'enable_open_registration': 'Habilitar Registro Abierto',
            'chat_retention_settings': 'Configuración de Retención de Chat',
            'chat_retention_description': 'Establecer el tiempo de retención para registros de chat público, los registros que excedan este tiempo se eliminarán automáticamente',
            'save_settings': 'Guardar Configuración',
            'reset': 'Restablecer',

            // Password Management
            'change_password': 'Cambiar Contraseña',
            'new_password': 'Nueva Contraseña',
            'new_password_placeholder': 'Dejar vacío para eliminar contraseña',
            'password_help': 'Dejar vacío para eliminar contraseña',
            'save_changes': 'Guardar Cambios',
            'cancel': 'Cancelar',
            'password_changed_success': 'Contraseña cambiada exitosamente',
            'password_removed_success': 'Contraseña eliminada exitosamente',
            'password_change_failed': 'Error al cambiar contraseña',

            // Password Reset
            'reset_password': 'Restablecer Contraseña',
            'contact_admin': 'Contactar Administrador',
            'reset_password_instruction': 'Por favor complete la siguiente información. Toda la información debe coincidir 100% con los datos del usuario para restablecer la contraseña.',
            'badge': 'Badge',
            'full_name': 'Nombre Completo',
            'login_code': 'Login',
            'department': 'Departamento',
            'password_reset_success': '¡La contraseña ha sido eliminada, por favor inicie sesión directamente!',
            'password_reset_updated_success': '¡Identidad verificada! La contraseña ha sido actualizada exitosamente.',
            'verification_failed': '¡Verificación fallida, por favor intente nuevamente o contacte al Administrador!',
            'reset_failed': 'Restablecimiento fallido, por favor verifique si la información es correcta',
            'contact_admin_info': 'Por favor contacte al administrador del sistema para restablecer su contraseña.',
            'login_failed': 'Error de inicio de sesión',

            // Registration Validation
            'badge_too_short': 'El Badge ID debe tener al menos 5 dígitos',
            'badge_numbers_only': 'El Badge ID debe contener solo números',
            'badge_already_exists': 'Este Badge ID ya está registrado',
            'badge_available': 'Badge ID está disponible',
            'login_too_short': 'El código de login debe tener al menos 2 caracteres',
            'login_already_exists': 'Este código de login ya está registrado',
            'login_available': 'Código de login está disponible',
            
            // 标签
            'labels': 'Etiquetas',
            'label_name': 'Nombre de Etiqueta',
            'label_type': 'Tipo de Etiqueta',
            'label_content': 'Contenido de Etiqueta',
            'print_quantity': 'Cantidad de Impresión',
            'copy_info': 'Información de Copia',
            'label_code': 'Código de Etiqueta',
            
            // 用户管理
            'users': 'Usuarios',
            'user_management': 'Gestión de Usuarios',
            'employee_id': 'ID de Empleado',
            'name': 'Nombre',
            'code': 'Código',
            'department': 'Departamento',
            'user_group': 'Grupo de Usuario',
            'actions': 'Acciones',
            
            // 部门管理
            'departments': 'Departamentos',
            'department_management': 'Gestión de Departamentos',
            'department_name': 'Nombre del Departamento',
            'department_code': 'Código del Departamento',
            'department_head': 'Jefe del Departamento',
            
            // 权限
            'permissions': 'Permisos',
            'no_permission': 'Sin Permisos',
            'permission_denied': 'Permisos Denegados',
            
            // 表单验证
            'required_field': 'Este campo es obligatorio',
            'invalid_format': 'Formato inválido',
            'confirm_delete': '¿Está seguro de que desea eliminar este elemento?',
            
            // 通知
            'notification': 'Notificación',
            'new_notification': 'Nueva Notificación',
            'notification_center': 'Centro de Notificaciones',
            'notification_list': 'Lista de Notificaciones',
            'no_notifications': 'Sin notificaciones',
            'mark_as_read': 'Marcar como leído',
            'notifications_marked_read': 'notificaciones marcadas como leídas',
            'mark_read_failed': 'Error al marcar como leído, inténtelo de nuevo',
            'fetch_notifications_failed': 'Error al obtener notificaciones',
            
            // 反馈
            'feedback_title': 'Comentarios',
            'submit_feedback': 'Enviar Comentarios',
            'feedback_content': 'Contenido de Comentarios',

            // Problem Solve
            'problem_solve_center': '🔧 Centro de Resolución de Problemas',
            'problem_solve_description': 'Ver y gestionar comentarios de problemas enviados por usuarios',
            'refresh_list': '🔄 Actualizar Lista',
            'problem_title': 'Problema',
            'submitted_by': 'Enviado Por',
            'problem_content': 'Contenido del Problema',
            'submitted_time': 'Tiempo de Envío',
            'replied': 'Respondido',
            'reply_problem': 'Responder Problema',
            'original_problem': 'Problema Original:',
            'reply_content': 'Contenido de Respuesta:',
            'reply_placeholder': 'Por favor ingrese su respuesta...',
            'submit_reply': 'Enviar Respuesta',
            'enter_reply_content': 'Por favor ingrese el contenido de la respuesta',
            'reply_submitted': 'Respuesta enviada',
            'reply_failed': 'Respuesta falló',
            'reply_failed_retry': 'Respuesta falló, por favor intente de nuevo',
            'reply_success': '¡Respuesta exitosa!',
            'unknown_error': 'Error desconocido',
            'confirm_delete_problem': '¿Está seguro de que desea eliminar este registro de problema?',
            'delete_success': 'Eliminación exitosa',
            'delete_failed': 'Eliminación falló',
            'no_problem_records': 'No hay registros de problemas',
            'load_failed': 'Carga falló',
            'back_to_home': 'Volver al Inicio',

            // Admin
            'admin_system_title': 'Sistema de Sistema de Etiquetado - Panel de Administración',
            'label_management': 'Gestión de Etiquetas',
            'user_group_management': 'Gestión de Grupos de Usuario',
            'announcement_management': 'Gestión de Anuncios',
            'feedback_view': 'Ver Comentarios',
            'barcode_prefix_management': 'Gestión de Prefijos de Código de Barras',
            'chatbot_kb_management': 'Base de Conocimientos del Chatbot',
            'audit_log': 'Registro de Auditoría',
            'system_settings': 'Configuración del Sistema',
            'search_labels': 'Buscar etiquetas...',
            'add_label': 'Agregar Etiqueta',
            'filter_by_category': 'Filtrar por Categoría:',
            'language': 'Idioma',

            // Admin common
            'loading': 'Cargando...',
            'no_permission': 'Sin Permisos',
            'no_permission_view': 'Sin permisos para ver',
            'edit': 'Editar',
            'delete': 'Eliminar',
            'yes': 'Sí',
            'no': 'No',
            'loading_failed': 'Carga falló',
            'no_data': 'No hay datos disponibles',
            'operation_success': 'Operación exitosa',
            'operation_failed': 'Operación falló',
            'confirm_delete_item': '¿Está seguro de que desea eliminar este elemento?',
            'please_fill_content': 'Por favor complete el contenido',
            'please_enter_valid_numbers': 'Por favor ingrese números válidos de filas y columnas',
            'insert_table_rows': 'Por favor ingrese filas de tabla:',
            'insert_table_cols': 'Por favor ingrese columnas de tabla:',
            'insert_nested_table_rows': 'Por favor ingrese filas de tabla anidada:',
            'insert_nested_table_cols': 'Por favor ingrese columnas de tabla anidada:',

            // Labels
            'no_labels_found': 'No se encontraron etiquetas que coincidan con los criterios.',
            'loading_labels_failed': 'Error al cargar etiquetas.',
            'label_updated_success': 'Etiqueta actualizada exitosamente',
            'label_deleted_success': 'Etiqueta eliminada exitosamente',
            'label_created_success': 'Etiqueta creada exitosamente',

            // Users
            'user_updated_success': 'Usuario actualizado exitosamente',
            'user_deleted_success': 'Usuario eliminado exitosamente',
            'user_created_success': 'Usuario creado exitosamente',

            // Departments
            'loading_departments_failed': 'Error al cargar departamentos',
            'no_department_data': 'No hay datos de departamentos',
            'department_updated_success': 'Departamento actualizado exitosamente',
            'department_deleted_success': 'Departamento eliminado exitosamente',
            'department_created_success': 'Departamento creado exitosamente',

            // User Groups
            'loading_user_groups_failed': 'Error al cargar grupos de usuarios',
            'no_user_groups_data': 'No hay datos de grupos de usuarios',
            'user_group_updated_success': 'Grupo de usuarios actualizado exitosamente',
            'user_group_deleted_success': 'Grupo de usuarios eliminado exitosamente',
            'user_group_created_success': 'Grupo de usuarios creado exitosamente',
            'confirm_delete_user_group': '¿Está seguro de que desea eliminar este grupo de usuarios?',

            // Announcements
            'add_announcement': 'Agregar Anuncio',
            'announcement_content': 'Contenido del Anuncio',
            'please_enter_announcement': 'Por favor ingrese el contenido del anuncio...',
            'announcement_created_success': 'Anuncio creado exitosamente',
            'announcement_updated_success': 'Anuncio actualizado exitosamente',
            'announcement_deleted_success': 'Anuncio eliminado exitosamente',
            'announcement_not_exist': 'El anuncio no existe',
            'edit_announcement': 'Editar Anuncio',
            'confirm_delete_announcement': '¿Está seguro de que desea eliminar este anuncio?',
            'loading_announcement_failed': 'Error al cargar anuncio',

            // Feedback
            'reply_feedback': 'Responder Comentario',
            'original_feedback_content': 'Contenido original del comentario:',
            'feedback_title': 'Título:',
            'feedback_content_label': 'Contenido:',
            'no_title': 'Sin título',
            'reply_content_label': 'Contenido de Respuesta',
            'please_enter_reply': 'Por favor ingrese el contenido de la respuesta...',
            'feedback_not_exist': 'El comentario no existe',
            'reply_success': 'Respuesta exitosa',
            'feedback_deleted_success': 'Comentario eliminado exitosamente',
            'confirm_delete_feedback': '¿Está seguro de que desea eliminar este comentario?',
            'loading_feedback_failed': 'Error al cargar comentarios',
            'no_feedback_data': 'No hay datos de comentarios',
            'not_replied': 'Sin responder',

            // Chatbot KB
            'chatbot_kb_updated_success': 'Base de conocimientos del chatbot actualizada exitosamente',
            'chatbot_kb_deleted_success': 'Base de conocimientos del chatbot eliminada exitosamente',
            'confirm_delete_chatbot_kb': '¿Está seguro de que desea eliminar esta entrada de la base de conocimientos del chatbot?',

            // Permissions
            'permissions_refreshed': 'Permisos actualizados',
            'refresh_failed': 'Actualización falló',
            
            // 公告
            'announcements': 'Anuncios',
            'announcement_title': 'Título del Anuncio',
            'announcement_content': 'Contenido del Anuncio',
            
            // 错误消息
            'network_error': 'Error de red, por favor intente de nuevo',
            'server_error': 'Error del servidor, por favor contacte al administrador',
            'login_required': 'Por favor inicie sesión primero',
            'operation_failed': 'Operación fallida',
            'operation_success': 'Operación exitosa',
            
            // 时间
            'today': 'Hoy',
            'yesterday': 'Ayer',
            'this_week': 'Esta Semana',
            'this_month': 'Este Mes',
            
            // 状态
            'active': 'Activo',
            'inactive': 'Inactivo',
            'pending': 'Pendiente',
            'completed': 'Completado',
            'cancelled': 'Cancelado',

            // 额外的翻译
            'no_problem_records': 'No hay registros de problemas',
            'replied': 'Respondido',
            'reply': 'Responder',
            'admin_system_title': 'Sistema de Sistema de Etiquetado - Panel de Administración'
        }
    },
    'zh': {
        name: '中文',
        flag: '🇨🇳',
        translations: {
            // 通用
            'welcome': '欢迎',
            'logout': '登出',
            'login': '登录',
            'cancel': '取消',
            'confirm': '确认',
            'save': '保存',
            'delete': '删除',
            'edit': '编辑',
            'add': '添加',
            'search': '搜索',
            'refresh': '刷新',
            'close': '关闭',
            'submit': '提交',
            'loading': '加载中...',
            'success': '成功',
            'error': '错误',
            'warning': '警告',
            'info': '信息',
            'yes': '是',
            'no': '否',
            
            // 页面标题
            'page_title': '标签打印系统',
            'admin_title': '后台管理 - 标签打印系统',
            'problem_solve_title': '问题解决',
            
            // 头部导航
            'label_printing': '标签打印',
            'admin_panel': '后台管理',
            'feedback': '反馈',
            'language': '语言',
            
            // 搜索
            'search_placeholder': '输入标签名称或关键词搜索...',
            'no_results': '未找到结果',
            
            // 聊天
            'chat': '聊天',
            'robot_chat': '机器人聊天',
            'public_chat': '公众聊天',
            'send_message': '发送消息',
            'type_message': '输入消息...',
            'chat_permission_denied': '您没有聊天权限',
            'robot_no_answer': '抱歉，我无法回答这个问题。',

            // 知识分享
            'share_knowledge': '分享知识',
            'share_my_knowledge': '分享我的知识。',
            'knowledge_example': '示例：问题(关键词)=答案',
            'knowledge_example_detail': '你的名字是什么？(你的名字)=我的名字是机器人。',
            'question_keyword': '问题(关键词)：',
            'answer': '答案：',
            'question_placeholder': '输入您的问题或关键词...',
            'answer_placeholder': '输入答案...',
            'knowledge_submitted': '知识提交审核成功！',
            'review_knowledge': '审核知识提交',
            'no_submissions': '暂无待审核提交',
            'submitted_by': '提交者',
            'question': '问题',
            'approve': '通过',
            'reject': '拒绝',
            'submission_reviewed': '提交审核成功',
            'submission_approved': '已通过审核并添加到知识库！',
            'submission_rejected': '已拒绝请求',
            'review_failed': '审核失败',
            'fill_all_fields': '请填写所有必填字段',
            'submit_failed': '提交失败',
            'load_failed': '加载提交失败',

            // Registration
            'sign_up': '注册',
            'user_registration': '用户注册',
            'badge_id': '员工卡ID',
            'badge_placeholder': '输入员工卡ID（至少5位数字）',
            'badge_format': '员工卡ID必须至少5位数字',
            'full_name': '姓名',
            'name_placeholder': '输入您的姓名',
            'login_code': '用户代码',
            'login_placeholder': '输入用户代码',
            'department': '部门',
            'select_department': '选择部门',
            'password': '密码',
            'optional': '可选',
            'password_placeholder': '留空表示无密码登录',
            'password_hint': '留空表示无密码登录',
            'cancel': '取消',
            'register': '注册',
            'registering': '注册中...',
            'fill_required_fields': '请填写所有必填字段。',
            'invalid_badge_format': '员工卡ID必须至少5位数字。',
            'registration_success': '注册成功！您现在可以登录了。',
            'registration_failed': '注册失败',

            // Enhanced Login
            'enter_password': '输入密码',
            'enter_card_id': '请输入您的员工卡ID',
            'user_not_found': '用户未找到',
            'check_failed': '检查用户失败',
            'welcome': '欢迎',
            'password_required': '需要密码才能继续',
            'logging_in': '正在登录...',
            'login': '登录',
            'continue': '继续',
            'invalid_password': '密码错误',
            'password_error_limit_reached': '密码错误次数已达到3次，您可以选择修改密码：',
            'change_password': '修改密码',
            'too_many_failed_attempts': '失败次数过多。请重置密码或联系管理员。',

            // System Settings
            'open_registration_settings': '开放注册设定',
            'open_registration_description': '控制是否允许用户自主注册账户',
            'enable_open_registration': '启用开放注册',
            'chat_retention_settings': '聊天记录保存时间设定',
            'chat_retention_description': '设置公众聊天记录的保存时间，超出时间的记录将自动删除',
            'save_settings': '保存设置',
            'reset': '重置',

            // Password Management
            'change_password': '修改密码',
            'new_password': '新密码',
            'new_password_placeholder': '留空表示去掉密码',
            'password_help': '留空表示去掉密码',
            'save_changes': '保存更改',
            'cancel': '取消',
            'password_changed_success': '密码修改成功',
            'password_removed_success': '密码已移除',
            'password_change_failed': '密码修改失败',

            // Password Reset
            'reset_password': '重置密码',
            'contact_admin': '联系管理员',
            'reset_password_instruction': '请填写以下信息，所有信息必须与用户数据100%吻合才能重置密码。',
            'badge': 'Badge',
            'full_name': '姓名',
            'login_code': 'Login',
            'department': '部门',
            'password_reset_success': '密码已经清除，请直接登入！',
            'password_reset_updated_success': '身份验证成功！密码已更新。',
            'verification_failed': '验证失败，请重试或联系管理员！',
            'reset_failed': '重置失败，请检查信息是否正确',
            'contact_admin_info': '请联系系统管理员重置您的密码。',
            'login_failed': '登录失败',

            // Registration Validation
            'badge_too_short': 'Badge ID必须至少5位数字',
            'badge_numbers_only': 'Badge ID只能包含数字',
            'badge_already_exists': '此Badge ID已被注册',
            'badge_available': 'Badge ID可用',
            'login_too_short': '登录代码必须至少2个字符',
            'login_already_exists': '此登录代码已被注册',
            'login_available': '登录代码可用',
            
            // 标签
            'labels': '标签',
            'label_name': '标签名称',
            'label_type': '标签类型',
            'label_content': '标签内容',
            'print_quantity': '打印数量',
            'copy_info': '复制信息',
            'label_code': '标签代码',
            
            // 用户管理
            'users': '用户',
            'user_management': '用户管理',
            'employee_id': '员工卡ID',
            'name': '姓名',
            'code': '代码',
            'department': '部门',
            'user_group': '用户组',
            'actions': '操作',
            
            // 部门管理
            'departments': '部门',
            'department_management': '部门管理',
            'department_name': '部门名称',
            'department_code': '部门代码',
            'department_head': '部门负责人',
            
            // 权限
            'permissions': '权限',
            'no_permission': '无权限',
            'permission_denied': '权限不足',
            
            // 表单验证
            'required_field': '此字段为必填项',
            'invalid_format': '格式无效',
            'confirm_delete': '确定要删除此项吗？',
            
            // 通知
            'notification': '通知',
            'new_notification': '新通知',
            'notification_center': '通知中心',
            'notification_list': '通知列表',
            'no_notifications': '暂无通知',
            'mark_as_read': '标记为已读',
            'notifications_marked_read': '条通知已标记为已读',
            'mark_read_failed': '标记已读失败，请重试',
            'fetch_notifications_failed': '获取通知失败',
            
            // 反馈
            'feedback_title': '反馈',
            'submit_feedback': '提交反馈',
            'feedback_content': '反馈内容',

            // Problem Solve
            'problem_solve_center': '🔧 问题解决中心',
            'problem_solve_description': '查看和管理用户提交的问题反馈',
            'refresh_list': '🔄 刷新列表',
            'problem_title': '问题',
            'submitted_by': '提交用户',
            'problem_content': '问题内容',
            'submitted_time': '提交时间',
            'replied': '已回复',
            'reply_problem': '回复问题',
            'original_problem': '原问题：',
            'reply_content': '回复内容：',
            'reply_placeholder': '请输入您的回复...',
            'submit_reply': '提交回复',
            'enter_reply_content': '请填写回复内容',
            'reply_submitted': '回复已提交',
            'reply_failed': '回复失败',
            'reply_failed_retry': '回复失败，请重试',
            'reply_success': '回复成功！',
            'unknown_error': '未知错误',
            'confirm_delete_problem': '确定要删除这个问题记录吗？',
            'delete_success': '删除成功',
            'delete_failed': '删除失败',
            'no_problem_records': '暂无问题记录',
            'load_failed': '加载失败',
            'back_to_home': '返回首页',

            // Admin
            'admin_system_title': '标签打印系统 - 后台管理',
            'label_management': '标签管理',
            'category_center': '分类中心',
            'user_group_management': '用户组管理',
            'announcement_management': '公告管理',
            'feedback_view': '反馈查看',
            'barcode_prefix_management': '条形码前缀管理',
            'chatbot_kb_management': '聊天机器人词库',
            'audit_log': '操作日志',
            'system_settings': '系统设定',
            'search_labels': '搜索标签...',
            'add_label': '添加标签',
            'filter_by_category': '分类查看:',
            'language': '语言',
            'add_category_code': '添加分类代码',
            'search_category_codes': '搜索分类代码...',

            // Admin common
            'loading': '加载中...',
            'no_permission': '无权限',
            'no_permission_view': '无权限查看',
            'edit': '编辑',
            'delete': '删除',
            'yes': '是',
            'no': '否',
            'loading_failed': '加载失败',
            'no_data': '暂无数据',
            'operation_success': '操作成功',
            'operation_failed': '操作失败',
            'confirm_delete_item': '确定要删除此项吗？',
            'please_fill_content': '请填写内容',
            'please_enter_valid_numbers': '请输入有效的行数和列数',
            'insert_table_rows': '请输入表格行数:',
            'insert_table_cols': '请输入表格列数:',
            'insert_nested_table_rows': '请输入嵌套表格行数:',
            'insert_nested_table_cols': '请输入嵌套表格列数:',

            // Labels
            'no_labels_found': '没有找到符合条件的标签。',
            'loading_labels_failed': '加载标签失败。',
            'label_updated_success': '标签更新成功',
            'label_deleted_success': '标签删除成功',
            'label_created_success': '标签创建成功',

            // Users
            'user_updated_success': '用户更新成功',
            'user_deleted_success': '用户删除成功',
            'user_created_success': '用户创建成功',

            // Departments
            'loading_departments_failed': '加载部门失败',
            'no_department_data': '暂无部门数据',
            'department_updated_success': '部门更新成功',
            'department_deleted_success': '部门删除成功',
            'department_created_success': '部门创建成功',

            // User Groups
            'loading_user_groups_failed': '加载用户组失败',
            'no_user_groups_data': '暂无用户组数据',
            'user_group_updated_success': '用户组更新成功',
            'user_group_deleted_success': '用户组删除成功',
            'user_group_created_success': '用户组创建成功',
            'confirm_delete_user_group': '确定要删除这个用户组吗？',

            // Announcements
            'add_announcement': '添加公告',
            'announcement_content': '公告内容',
            'please_enter_announcement': '请输入公告内容...',
            'announcement_created_success': '公告创建成功',
            'announcement_updated_success': '公告更新成功',
            'announcement_deleted_success': '公告删除成功',
            'announcement_not_exist': '公告不存在',
            'edit_announcement': '编辑公告',
            'confirm_delete_announcement': '确定要删除这个公告吗？',
            'loading_announcement_failed': '加载公告失败',

            // Feedback
            'reply_feedback': '回复反馈',
            'original_feedback_content': '原反馈内容：',
            'feedback_title': '标题：',
            'feedback_content_label': '内容：',
            'no_title': '无标题',
            'reply_content_label': '回复内容',
            'please_enter_reply': '请输入回复内容...',
            'feedback_not_exist': '反馈不存在',
            'reply_success': '回复成功',
            'feedback_deleted_success': '反馈删除成功',
            'confirm_delete_feedback': '确定要删除这个反馈吗？',
            'loading_feedback_failed': '加载反馈失败',
            'no_feedback_data': '暂无反馈数据',
            'not_replied': '未回复',

            // Chatbot KB
            'chatbot_kb_updated_success': '聊天机器人词条更新成功',
            'chatbot_kb_deleted_success': '聊天机器人词条删除成功',
            'confirm_delete_chatbot_kb': '确定要删除这个聊天机器人词条吗？',

            // Permissions
            'permissions_refreshed': '权限已刷新',
            'refresh_failed': '刷新失败',
            
            // 公告
            'announcements': '公告',
            'announcement_title': '公告标题',
            'announcement_content': '公告内容',
            
            // 错误消息
            'network_error': '网络错误，请重试',
            'server_error': '服务器错误，请联系管理员',
            'login_required': '请先登录',
            'operation_failed': '操作失败',
            'operation_success': '操作成功',
            
            // 时间
            'today': '今天',
            'yesterday': '昨天',
            'this_week': '本周',
            'this_month': '本月',
            
            // 状态
            'active': '活跃',
            'inactive': '非活跃',
            'pending': '待处理',
            'completed': '已完成',
            'cancelled': '已取消',

            // 额外的翻译
            'no_problem_records': '暂无问题记录',
            'replied': '已回复',
            'reply': '回复',
            'admin_system_title': '标签打印系统 - 后台管理'
        }
    }
};

// 当前语言 - 默认英文，将从服务器加载
let currentLanguage = 'en';

// 国际化类
class I18n {
    constructor() {
        this.currentLanguage = currentLanguage;
        this.init();
    }

    async init() {
        // 从服务器获取用户语言设置
        await this.loadUserLanguage();

        // 设置页面语言属性
        document.documentElement.lang = this.getLanguageCode();

        // 初始化语言切换器
        this.initLanguageSwitcher();

        // 翻译页面
        this.translatePage();
    }

    async loadUserLanguage() {
        try {
            const response = await fetch('/api/user-language');
            if (response.ok) {
                const data = await response.json();
                this.currentLanguage = data.language;
                currentLanguage = data.language;
                console.log('Loaded user language:', data.language);
            }
        } catch (error) {
            console.log('Failed to load user language, using default');
            this.currentLanguage = 'en';
            currentLanguage = 'en';
        }
    }

    getLanguageCode() {
        const langMap = {
            'en': 'en-US',
            'es': 'es-ES',
            'zh': 'zh-CN'
        };
        return langMap[this.currentLanguage] || 'en-US';
    }

    // 获取翻译文本
    t(key, params = {}) {
        const translation = languages[this.currentLanguage]?.translations[key] || key;
        
        // 替换参数
        let result = translation;
        Object.keys(params).forEach(param => {
            result = result.replace(`{${param}}`, params[param]);
        });
        
        return result;
    }

    // 切换语言
    async setLanguage(lang) {
        if (languages[lang]) {
            // 保存到服务器
            try {
                const response = await fetch('/api/user-language', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ language: lang })
                });

                if (response.ok) {
                    this.currentLanguage = lang;
                    currentLanguage = lang;
                    // 不再使用localStorage，语言设置已保存到服务器
                    document.documentElement.lang = this.getLanguageCode();
                    this.translatePage();
                    this.updateLanguageSwitcher();

                    // 触发语言切换事件
                    window.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: lang } }));

                    console.log('Language saved to server:', lang);
                } else {
                    console.error('Failed to save language setting');
                }
            } catch (error) {
                console.error('Error saving language setting:', error);
            }
        }
    }

    // 翻译页面
    translatePage() {
        // 翻译带有 data-i18n 属性的元素
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            element.textContent = this.t(key);
        });

        // 翻译带有 data-i18n-placeholder 属性的输入框
        document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            element.placeholder = this.t(key);
        });

        // 翻译带有 data-i18n-title 属性的元素
        document.querySelectorAll('[data-i18n-title]').forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            element.title = this.t(key);
        });

        // 更新页面标题
        if (document.querySelector('title')) {
            document.title = this.t('page_title');
        }
    }

    // 初始化语言切换器
    initLanguageSwitcher() {
        // 为语言选项添加点击事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.language-option')) {
                const option = e.target.closest('.language-option');
                const lang = option.dataset.lang;
                if (lang && lang !== this.currentLanguage) {
                    this.setLanguage(lang);
                }
            }
        });

        // 初始化当前语言高亮
        setTimeout(() => {
            this.updateLanguageSwitcher();
        }, 100);
    }

    // 创建语言切换器
    createLanguageSwitcher(userInfoElement) {
        // 保存原始用户信息
        const originalText = userInfoElement.textContent;
        
        // 创建可点击的用户信息
        userInfoElement.style.cursor = 'pointer';
        userInfoElement.style.position = 'relative';
        userInfoElement.classList.add('language-switcher-trigger');
        
        // 添加点击事件
        userInfoElement.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleLanguageMenu();
        });

        // 创建语言菜单
        this.createLanguageMenu();
    }

    // 创建语言菜单
    createLanguageMenu() {
        // 移除已存在的菜单
        const existingMenu = document.getElementById('language-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        const menu = document.createElement('div');
        menu.id = 'language-menu';
        menu.className = 'language-menu';
        menu.innerHTML = `
            <div class="language-menu-header">
                <span data-i18n="language">${this.t('language')}</span>
            </div>
            <div class="language-options">
                ${Object.keys(languages).map(lang => `
                    <div class="language-option ${lang === this.currentLanguage ? 'active' : ''}" data-lang="${lang}">
                        <span class="language-flag">${languages[lang].flag}</span>
                        <span class="language-name">${languages[lang].name}</span>
                    </div>
                `).join('')}
            </div>
        `;

        document.body.appendChild(menu);

        // 添加点击事件
        menu.querySelectorAll('.language-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const lang = e.currentTarget.getAttribute('data-lang');
                this.setLanguage(lang);
                this.hideLanguageMenu();
            });
        });

        // 点击外部关闭菜单
        document.addEventListener('click', (e) => {
            if (!menu.contains(e.target) && !e.target.classList.contains('language-switcher-trigger')) {
                this.hideLanguageMenu();
            }
        });
    }

    // 显示/隐藏语言菜单
    toggleLanguageMenu() {
        const menu = document.getElementById('language-menu');
        if (menu) {
            if (menu.style.display === 'block') {
                this.hideLanguageMenu();
            } else {
                this.showLanguageMenu();
            }
        }
    }

    showLanguageMenu() {
        const menu = document.getElementById('language-menu');
        const trigger = document.querySelector('.language-switcher-trigger');
        
        if (menu && trigger) {
            const rect = trigger.getBoundingClientRect();
            menu.style.display = 'block';
            menu.style.top = (rect.bottom + 5) + 'px';
            menu.style.right = (window.innerWidth - rect.right) + 'px';
        }
    }

    hideLanguageMenu() {
        const menu = document.getElementById('language-menu');
        if (menu) {
            menu.style.display = 'none';
        }
    }

    // 更新语言切换器
    updateLanguageSwitcher() {
        // 移除所有活动状态
        document.querySelectorAll('.language-option').forEach(option => {
            option.classList.remove('active');
        });

        // 为当前语言添加活动状态
        document.querySelectorAll(`[data-lang="${this.currentLanguage}"]`).forEach(option => {
            option.classList.add('active');
        });

        console.log('Updated language switcher for:', this.currentLanguage);
    }
}

// 全局实例
window.i18n = new I18n();

// 导出翻译函数
window.t = (key, params) => window.i18n.t(key, params);
