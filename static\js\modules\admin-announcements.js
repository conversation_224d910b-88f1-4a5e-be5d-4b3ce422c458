/**
 * 后台公告管理模块
 */
class AdminAnnouncementsModule {
    constructor() {
        this.dependencies = ['createModal', 'showMessage', 'formatDate', 'checkPermission', 'showNoPermission'];
        this.init();
    }

    init() {
        console.log('初始化后台公告管理模块...');
        // 确保依赖项已加载
        this.ensureDependencies().then(() => {
            console.log('后台公告模块依赖项加载完成。');
        });
    }

    async ensureDependencies() {
        for (const dep of this.dependencies) {
            while (typeof window[dep] !== 'function') {
                console.log(`等待依赖 ${dep}...`);
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
    }
    
    // 加载公告列表
    loadAnnouncements() {
        if (!checkPermission('can_manage_announcements')) {
            showNoPermission('announcements', 5, '公告管理');
            return;
        }

        const tbody = document.querySelector('#announcements-table tbody');
        if (!tbody) {
            console.error('无法找到公告表格的tbody');
            return;
        }
        tbody.innerHTML = '<tr><td colspan="5" class="empty-state">加载中...</td></tr>';

        fetch('/api/admin/announcements')
            .then(response => response.json())
            .then(announcements => {
                if (announcements.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="5" class="empty-state">暂无公告</td></tr>';
                    return;
                }
                tbody.innerHTML = announcements.map(announcement => {
                    const actionButtons = [];
                    if (checkPermission('can_manage_announcements_edit')) {
                        actionButtons.push(`<button class="btn-edit" onclick="adminAnnouncementsModule.editAnnouncement(${announcement.id})">编辑</button>`);
                    }
                    if (checkPermission('can_manage_announcements_delete')) {
                        actionButtons.push(`<button class="btn-delete" onclick="adminAnnouncementsModule.deleteAnnouncement(${announcement.id})">删除</button>`);
                    }
                    return `
                        <tr>
                            <td>${announcement.id}</td>
                            <td>${announcement.content}</td>
                            <td>${announcement.author_name || ''}</td>
                            <td>${formatDate(announcement.created_at)}</td>
                            <td>${actionButtons.join(' ')}</td>
                        </tr>
                    `;
                }).join('');
            })
            .catch(error => {
                console.error('Error loading announcements:', error);
                tbody.innerHTML = '<tr><td colspan="5" class="empty-state">加载公告失败</td></tr>';
            });
    }

    // 显示添加公告模态框
    showAddAnnouncementModal() {
        const modal = createModal('添加公告', `
            <div class="form-group">
                <label for="announcement-content">公告内容</label>
                <textarea id="announcement-content" rows="4" required placeholder="请输入公告内容..."></textarea>
            </div>
        `);
        modal.querySelector('.btn-save').onclick = () => {
            const content = document.getElementById('announcement-content').value.trim();
            if (!content) {
                showMessage('请填写公告内容', 'error');
                return;
            }
            this.createAnnouncement({ content });
            modal.remove();
        };
    }

    // 创建公告
    createAnnouncement(announcementData) {
        fetch('/api/admin/announcements', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(announcementData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('公告创建成功', 'success');
                this.loadAnnouncements();
                this.notifyFrontendUpdate(); // 通知前台更新
            } else {
                showMessage(data.error || '创建失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error creating announcement:', error);
            showMessage('创建公告时出错', 'error');
        });
    }

    // 编辑公告
    editAnnouncement(id) {
        fetch('/api/admin/announcements')
            .then(response => response.json())
            .then(announcements => {
                const announcement = announcements.find(a => a.id === id);
                if (!announcement) {
                    showMessage('公告不存在', 'error');
                    return;
                }
                const modal = createModal('编辑公告', `
                    <div class="form-group">
                        <label for="announcement-content">公告内容</label>
                        <textarea id="announcement-content" rows="4" required>${announcement.content}</textarea>
                    </div>
                `);
                modal.querySelector('.btn-save').onclick = () => {
                    const content = document.getElementById('announcement-content').value.trim();
                    if (!content) {
                        showMessage('请填写公告内容', 'error');
                        return;
                    }
                    this.updateAnnouncement(id, { content });
                    modal.remove();
                };
            });
    }

    // 更新公告
    updateAnnouncement(id, announcementData) {
        fetch(`/api/admin/announcements/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(announcementData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('公告更新成功', 'success');
                this.loadAnnouncements();
                this.notifyFrontendUpdate(); // 通知前台更新
            } else {
                showMessage(data.error || '更新失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error updating announcement:', error);
            showMessage('更新公告时出错', 'error');
        });
    }

    // 删除公告
    deleteAnnouncement(id) {
        if (!confirm('确定要删除这个公告吗？')) return;
        fetch(`/api/admin/announcements/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('公告删除成功', 'success');
                this.loadAnnouncements();
                this.notifyFrontendUpdate(); // 通知前台更新
            } else {
                showMessage(data.error || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting announcement:', error);
            showMessage('删除公告时出错', 'error');
        });
    }

    // 通知前台更新公告
    notifyFrontendUpdate() {
        console.log('通知前台更新公告...');
        localStorage.setItem('announcements_updated', Date.now());
    }
}

window.AdminAnnouncementsModule = AdminAnnouncementsModule; 