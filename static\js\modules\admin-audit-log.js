// 操作日志管理模块
const adminAuditLogModule = (() => {
    // 加载操作日志
    function loadAuditLog() {
        if (!window.checkPermission('can_view_audit_log')) {
            const tbody = document.querySelector('#audit-log-table tbody');
            if (tbody) tbody.innerHTML = '<tr><td colspan="3" class="empty-state">无权限查看操作日志</td></tr>';
            const clearBtn = document.querySelector('.btn-clear');
            if (clearBtn) clearBtn.style.display = 'none';
            return;
        }
        const tbody = document.querySelector('#audit-log-table tbody');
        if (!tbody) return;
        tbody.innerHTML = '<tr><td colspan="4" class="empty-state">加载中...</td></tr>';
        fetch('/api/audit-log')
            .then(response => response.json())
            .then(logs => {
                if (logs.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="4" class="empty-state">暂无操作日志</td></tr>';
                    return;
                }
                tbody.innerHTML = logs.map(log => `
                    <tr>
                        <td>${log.id}</td>
                        <td>${log.user_name || '未知用户'}</td>
                        <td>${log.action}</td>
                        <td>${window.formatDate ? window.formatDate(log.timestamp) : log.timestamp}</td>
                    </tr>
                `).join('');
            })
            .catch(error => {
                console.error('Error loading audit log:', error);
                tbody.innerHTML = '<tr><td colspan="4" class="empty-state">加载失败</td></tr>';
        });
        // 控制清空按钮显示
        const clearBtn = document.querySelector('.btn-clear');
        if (clearBtn) {
            clearBtn.style.display = (window.checkPermission('can_view_audit_log') && window.checkPermission('can_clear_audit_log')) ? '' : 'none';
        }
    }

    // 清空操作日志
    function clearAuditLog() {
        if (!confirm('确定要清空所有操作日志吗？此操作不可恢复。')) return;
        fetch('/api/audit-log', {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.showMessage('操作日志清空成功', 'success');
                loadAuditLog();
            } else {
                window.showMessage(data.error || '清空失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error clearing audit log:', error);
            window.showMessage('清空失败', 'error');
        });
    }

    return {
        loadAuditLog,
        clearAuditLog
    };
})();

window.adminAuditLogModule = adminAuditLogModule; 