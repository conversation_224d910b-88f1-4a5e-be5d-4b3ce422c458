// 条形码前缀管理模块
const adminBarcodePrefixesModule = (() => {
    // 加载条形码前缀列表
    function loadBarcodePrefixes() {
        if (!window.checkPermission('can_manage_barcode_prefixes')) {
            window.showNoPermission && window.showNoPermission('barcode-prefixes', 3, '条形码前缀');
            return;
        }
        const tbody = document.querySelector('#barcode-prefixes-table tbody');
        if (!tbody) return;
        
        tbody.innerHTML = '<tr><td colspan="4" class="empty-state">加载中...</td></tr>';
        
        fetch('/api/barcode-prefixes')
            .then(response => response.json())
            .then(prefixes => {
                if (prefixes.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="4" class="empty-state">暂无条形码前缀</td></tr>';
                    return;
                }
                
                tbody.innerHTML = prefixes.map(prefix => {
                    // 根据权限生成操作按钮
                    const actionButtons = [];
                    if (window.checkPermission('can_manage_barcode_prefixes_edit')) {
                        actionButtons.push(`<button class="btn-edit" onclick="window.adminBarcodePrefixesModule.editBarcodePrefix(${prefix.id})">编辑</button>`);
                    }
                    if (window.checkPermission('can_manage_barcode_prefixes_delete')) {
                        actionButtons.push(`<button class="btn-delete" onclick="window.adminBarcodePrefixesModule.deleteBarcodePrefix(${prefix.id})">删除</button>`);
                    }
                    return `
                    <tr>
                        <td>${prefix.id}</td>
                        <td>${prefix.prefix}</td>
                        <td>${prefix.print_count}</td>
                        <td>
                                ${actionButtons.join('')}
                        </td>
                    </tr>
                    `;
                }).join('');
            })
            .catch(error => {
                console.error('Error loading barcode prefixes:', error);
                tbody.innerHTML = '<tr><td colspan="4" class="empty-state">加载失败</td></tr>';
            });
    }

    // 添加前缀弹窗
    function showAddBarcodePrefixModal() {
        const modal = window.createModal('添加条形码前缀', `
            <div class="form-group">
                <label for="prefix-value">前缀</label>
                <input type="text" id="prefix-value" required placeholder="请输入条形码前缀">
            </div>
            <div class="form-group">
                <label for="print-count">打印数量</label>
                <input type="number" id="print-count" value="1" min="1" required>
            </div>
        `);
        
        modal.querySelector('.btn-save').onclick = () => {
            const prefix = document.getElementById('prefix-value').value.trim();
            const printCount = document.getElementById('print-count').value;
            if (!prefix) {
                alert('请填写前缀');
                return;
            }
            createBarcodePrefix({ prefix, print_count: parseInt(printCount) });
            window.closeModal && window.closeModal(modal);
        };
    }

    // 创建前缀
    function createBarcodePrefix(prefixData) {
        fetch('/api/barcode-prefixes', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(prefixData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.showMessage('条形码前缀创建成功', 'success');
                loadBarcodePrefixes();
            } else {
                window.showMessage(data.error || '创建失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error creating barcode prefix:', error);
            window.showMessage('创建失败', 'error');
        });
    }

    // 编辑前缀弹窗
    function editBarcodePrefix(id) {
        fetch('/api/barcode-prefixes')
            .then(response => response.json())
            .then(prefixes => {
                const prefix = prefixes.find(p => p.id === id);
                if (!prefix) {
                    window.showMessage('条形码前缀不存在', 'error');
                    return;
                }
                const modal = window.createModal('编辑条形码前缀', `
                    <div class="form-group">
                        <label for="prefix-value">前缀</label>
                        <input type="text" id="prefix-value" value="${prefix.prefix}" required>
                    </div>
                    <div class="form-group">
                        <label for="print-count">打印数量</label>
                        <input type="number" id="print-count" value="${prefix.print_count}" min="1" required>
                    </div>
                `);
                modal.querySelector('.btn-save').onclick = () => {
                    const prefixValue = document.getElementById('prefix-value').value.trim();
                    const printCount = document.getElementById('print-count').value;
                    if (!prefixValue) {
                        alert('请填写前缀');
                        return;
                    }
                    updateBarcodePrefix(id, { prefix: prefixValue, print_count: parseInt(printCount) });
                    window.closeModal && window.closeModal(modal);
                };
            })
            .catch(error => {
                console.error('Error loading barcode prefix:', error);
                window.showMessage('加载条形码前缀失败', 'error');
            });
    }

    // 更新前缀
    function updateBarcodePrefix(id, prefixData) {
        fetch(`/api/barcode-prefixes/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(prefixData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.showMessage('条形码前缀更新成功', 'success');
                loadBarcodePrefixes();
            } else {
                window.showMessage(data.error || '更新失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error updating barcode prefix:', error);
            window.showMessage('更新失败', 'error');
        });
    }

    // 删除前缀
    function deleteBarcodePrefix(id) {
        if (!confirm('确定要删除这个条形码前缀吗？')) return;
        fetch(`/api/barcode-prefixes/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.showMessage('条形码前缀删除成功', 'success');
                loadBarcodePrefixes();
            } else {
                window.showMessage(data.error || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting barcode prefix:', error);
            window.showMessage('删除失败', 'error');
        });
    }

    return {
        loadBarcodePrefixes,
        showAddBarcodePrefixModal,
        createBarcodePrefix,
        editBarcodePrefix,
        updateBarcodePrefix,
        deleteBarcodePrefix
    };
})();

window.adminBarcodePrefixesModule = adminBarcodePrefixesModule; 