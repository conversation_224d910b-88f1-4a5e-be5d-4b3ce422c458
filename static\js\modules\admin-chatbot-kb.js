// 聊天机器人词库管理模块
const adminChatbotKBModule = (() => {
    // 加载词库列表
    function loadChatbotKB() {
        if (!window.checkPermission('can_manage_chatbot_kb')) {
            window.showNoPermission && window.showNoPermission('chatbot-kb', 3, '聊天机器人词库');
            return;
        }
        const tbody = document.querySelector('#chatbot-kb-table tbody');
        if (!tbody) return;
        tbody.innerHTML = '<tr><td colspan="4" class="empty-state">加载中...</td></tr>';
        fetch('/api/chatbot-kb')
            .then(response => response.json())
            .then(kb => {
                if (kb.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="4" class="empty-state">暂无词库数据</td></tr>';
                    return;
                }
                tbody.innerHTML = kb.map(item => {
                    const actionButtons = [];
                    if (window.checkPermission('can_manage_chatbot_kb_edit')) {
                        actionButtons.push(`<button class=\"btn-edit\" onclick=\"window.adminChatbotKBModule.editChatbotKB(${item.id})\">编辑</button>`);
                    }
                    if (window.checkPermission('can_manage_chatbot_kb_delete')) {
                        actionButtons.push(`<button class=\"btn-delete\" onclick=\"window.adminChatbotKBModule.deleteChatbotKB(${item.id})\">删除</button>`);
                    }
                    return `
                    <tr>
                        <td>${item.id}</td>
                        <td>${item.keyword}</td>
                        <td>${item.answer ? item.answer.substring(0, 50) + (item.answer.length > 50 ? '...' : '') : ''}</td>
                        <td>
                                ${actionButtons.join('')}
                        </td>
                    </tr>
                    `;
                }).join('');
            })
            .catch(error => {
                console.error('Error loading chatbot KB:', error);
                tbody.innerHTML = '<tr><td colspan="4" class="empty-state">加载失败</td></tr>';
            });
    }

    // 添加词条弹窗
    function showAddChatbotKBModal() {
        const modal = window.createModal('添加聊天机器人词条', `
            <div class=\"form-group\">
                <label for=\"kb-keyword\">关键词</label>
                <input type=\"text\" id=\"kb-keyword\" required placeholder=\"请输入关键词\">
            </div>
            <div class=\"form-group\">
                <label for=\"kb-answer\">回答内容</label>
                <textarea id=\"kb-answer\" rows=\"4\" required placeholder=\"请输入回答内容...\"></textarea>
            </div>
        `);
        modal.querySelector('.btn-save').onclick = () => {
            const keyword = document.getElementById('kb-keyword').value.trim();
            const answer = document.getElementById('kb-answer').value.trim();
            if (!keyword || !answer) {
                alert('请填写关键词和回答内容');
                return;
            }
            createChatbotKB({ keyword, answer });
            window.closeModal && window.closeModal(modal);
        };
    }

    // 创建词条
    function createChatbotKB(kbData) {
        fetch('/api/chatbot-kb', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(kbData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.showMessage('聊天机器人词条创建成功', 'success');
                loadChatbotKB();
            } else {
                window.showMessage(data.error || '创建失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error creating chatbot KB:', error);
            window.showMessage('创建失败', 'error');
        });
    }

    // 编辑词条弹窗
    function editChatbotKB(id) {
        fetch('/api/chatbot-kb')
            .then(response => response.json())
            .then(kb => {
                const item = kb.find(k => k.id === id);
                if (!item) {
                    window.showMessage('聊天机器人词条不存在', 'error');
                    return;
                }
                const modal = window.createModal('编辑聊天机器人词条', `
                    <div class=\"form-group\">
                        <label for=\"kb-keyword\">关键词</label>
                        <input type=\"text\" id=\"kb-keyword\" value=\"${item.keyword}\" required>
                    </div>
                    <div class=\"form-group\">
                        <label for=\"kb-answer\">回答内容</label>
                        <textarea id=\"kb-answer\" rows=\"4\" required>${item.answer}</textarea>
                    </div>
                `);
                modal.querySelector('.btn-save').onclick = () => {
                    const keyword = document.getElementById('kb-keyword').value.trim();
                    const answer = document.getElementById('kb-answer').value.trim();
                    if (!keyword || !answer) {
                        alert('请填写关键词和回答内容');
                        return;
                    }
                    updateChatbotKB(id, { keyword, answer });
                    window.closeModal && window.closeModal(modal);
                };
            })
            .catch(error => {
                console.error('Error loading chatbot KB:', error);
                window.showMessage('加载聊天机器人词条失败', 'error');
            });
    }

    // 更新词条
    function updateChatbotKB(id, kbData) {
        fetch(`/api/chatbot-kb/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(kbData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.showMessage('聊天机器人词条更新成功', 'success');
                loadChatbotKB();
            } else {
                window.showMessage(data.error || '更新失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error updating chatbot KB:', error);
            window.showMessage('更新失败', 'error');
        });
    }

    // 删除词条
    function deleteChatbotKB(id) {
        if (!confirm('确定要删除这个聊天机器人词条吗？')) return;
        fetch(`/api/chatbot-kb/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.showMessage('聊天机器人词条删除成功', 'success');
                loadChatbotKB();
            } else {
                window.showMessage(data.error || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting chatbot KB:', error);
            window.showMessage('删除失败', 'error');
        });
    }

    return {
        loadChatbotKB,
        showAddChatbotKBModal,
        createChatbotKB,
        editChatbotKB,
        updateChatbotKB,
        deleteChatbotKB
    };
})();

window.adminChatbotKBModule = adminChatbotKBModule; 