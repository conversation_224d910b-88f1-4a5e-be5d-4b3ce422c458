// 部门管理模块
class AdminDepartmentsModule {
    constructor() {
        this.dependencies = ['createModal', 'closeModal', 'showMessage', 'checkPermission', 'showNoPermission'];
        this.init();
    }

    init() {
        this.ensureDependencies().then(() => {
            console.log('后台部门管理模块依赖加载完成');
        });
    }

    async ensureDependencies() {
        for (const dep of this.dependencies) {
            while (typeof window[dep] !== 'function') {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
    }

    // 加载部门列表
    loadDepartments() {
        if (!checkPermission('can_manage_departments')) {
            showNoPermission('departments', 3, '部门管理');
            return;
        }
        const tbody = document.querySelector('#departments-table-body');
        if (!tbody) return;
        tbody.innerHTML = '<tr><td colspan="3">加载中...</td></tr>';

        fetch('/api/departments')
            .then(res => res.json())
            .then(data => {
                if (!Array.isArray(data)) {
                    tbody.innerHTML = '<tr><td colspan="3" class="empty-state">加载部门失败：数据格式错误。</td></tr>';
                    return;
                }
                if (data.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="3" class="empty-state">暂无部门数据</td></tr>';
                    return;
                }
                tbody.innerHTML = '';
                data.forEach(department => {
                    const actionButtons = [];
                    if (checkPermission('can_manage_departments_edit')) {
                        actionButtons.push(`<button class="btn-edit" onclick="adminDepartmentsModule.editDepartment(${department.id})">编辑</button>`);
                    }
                    if (checkPermission('can_manage_departments_delete')) {
                        actionButtons.push(`<button class="btn-delete" onclick="adminDepartmentsModule.deleteDepartment(${department.id})">删除</button>`);
                    }
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${department.id}</td>
                        <td>${this.escapeHTML(department.name)}</td>
                        <td>${actionButtons.join('')}</td>
                    `;
                    tbody.appendChild(row);
                });
            })
            .catch(error => {
                console.error('加载部门失败:', error);
                tbody.innerHTML = '<tr><td colspan="3" class="empty-state">加载部门失败。</td></tr>';
            });
    }

    // 显示添加部门弹窗
    showAddDepartmentModal() {
        const modal = createModal('添加部门', `
            <div class="form-group">
                <label for="department-name">部门名称</label>
                <input type="text" id="department-name" required>
            </div>
        `);
        modal.querySelector('.btn-save').onclick = () => {
            const name = document.getElementById('department-name').value;
            if (!name) {
                alert('请填写部门名称');
                return;
            }
            this.createDepartment({ name });
            closeModal(modal);
        };
    }

    // 创建部门
    createDepartment(deptData) {
        fetch('/api/departments', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(deptData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('部门创建成功', 'success');
                this.loadDepartments();
            } else {
                showMessage(data.error || '创建失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error creating department:', error);
            showMessage('创建失败', 'error');
        });
    }

    // 编辑部门
    editDepartment(id) {
        fetch('/api/departments')
            .then(response => response.json())
            .then(departments => {
                const dept = departments.find(d => d.id === id);
                if (!dept) {
                    showMessage('部门不存在', 'error');
                    return;
                }
                const modal = createModal('编辑部门', `
                    <div class="form-group">
                        <label for="department-name">部门名称</label>
                        <input type="text" id="department-name" value="${this.escapeHTML(dept.name)}" required>
                    </div>
                `);
                modal.querySelector('.btn-save').onclick = () => {
                    const name = document.getElementById('department-name').value;
                    if (!name) {
                        alert('请填写部门名称');
                        return;
                    }
                    this.updateDepartment(id, { name });
                    closeModal(modal);
                };
            })
            .catch(error => {
                console.error('Error loading department:', error);
                showMessage('加载部门失败', 'error');
            });
    }

    // 更新部门
    updateDepartment(id, deptData) {
        fetch(`/api/departments/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(deptData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('部门更新成功', 'success');
                this.loadDepartments();
            } else {
                showMessage(data.error || '更新失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error updating department:', error);
            showMessage('更新失败', 'error');
        });
    }

    // 删除部门
    deleteDepartment(id) {
        if (!confirm('确定要删除这个部门吗？')) return;
        fetch(`/api/departments/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('部门删除成功', 'success');
                this.loadDepartments();
            } else {
                showMessage(data.error || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting department:', error);
            showMessage('删除失败', 'error');
        });
    }

    // HTML转义
    escapeHTML(str) {
        if (!str) return '';
        return str.replace(/[&<>"']/g, function (c) {
            return {'&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;'}[c];
        });
    }
}

// 挂载到window，便于全局调用
window.adminDepartmentsModule = new AdminDepartmentsModule(); 