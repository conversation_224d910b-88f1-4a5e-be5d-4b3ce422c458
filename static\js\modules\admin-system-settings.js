/**
 * 后台系统设定管理模块
 */
class AdminSystemSettingsModule {
    constructor() {
        this.dependencies = ['showMessage', 'checkPermission', 'showNoPermission'];
        this.init();
    }

    init() {
        this.ensureDependencies().then(() => {
            console.log('后台系统设定模块依赖加载完成');
            this.setupEventListeners();
        });
    }

    async ensureDependencies() {
        for (const dep of this.dependencies) {
            while (typeof window[dep] !== 'function') {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
    }

    setupEventListeners() {
        // 设置聊天记录保存时间表单提交事件
        const chatForm = document.getElementById('chat-retention-form');
        if (chatForm) {
            chatForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveSettings();
            });
        }

        // 设置注册设置表单提交事件
        const registrationForm = document.getElementById('registration-settings-form');
        if (registrationForm) {
            registrationForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveRegistrationSettings();
            });
        }

        // 加载设置
        this.loadSettings();
        this.loadRegistrationSettings();
    }

    // 加载系统设定
    loadSettings() {
        if (!checkPermission('can_manage_system_settings')) {
            showNoPermission('system-settings', 1, '系统设定');
            return;
        }

        fetch('/api/system-settings')
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showMessage(data.error, 'error');
                    return;
                }

                // 填充表单数据
                const daysInput = document.getElementById('retention-days');
                const hoursInput = document.getElementById('retention-hours');
                const minutesInput = document.getElementById('retention-minutes');

                if (daysInput) daysInput.value = data.chat_retention_days || 7;
                if (hoursInput) hoursInput.value = data.chat_retention_hours || 0;
                if (minutesInput) minutesInput.value = data.chat_retention_minutes || 0;
            })
            .catch(error => {
                console.error('Error loading system settings:', error);
                showMessage('加载系统设定失败', 'error');
            });
    }

    // 保存系统设定
    saveSettings() {
        if (!checkPermission('can_manage_system_settings')) {
            showMessage('权限不足', 'error');
            return;
        }

        const daysInput = document.getElementById('retention-days');
        const hoursInput = document.getElementById('retention-hours');
        const minutesInput = document.getElementById('retention-minutes');

        const days = parseInt(daysInput.value) || 0;
        const hours = parseInt(hoursInput.value) || 0;
        const minutes = parseInt(minutesInput.value) || 0;

        // 验证输入
        if (days < 0 || days > 365) {
            showMessage('天数必须在0-365之间', 'error');
            return;
        }
        if (hours < 0 || hours > 23) {
            showMessage('小时数必须在0-23之间', 'error');
            return;
        }
        if (minutes < 0 || minutes > 59) {
            showMessage('分钟数必须在0-59之间', 'error');
            return;
        }
        if (days === 0 && hours === 0 && minutes === 0) {
            showMessage('保存时间不能全部为0', 'error');
            return;
        }

        const data = {
            chat_retention_days: days,
            chat_retention_hours: hours,
            chat_retention_minutes: minutes
        };

        fetch('/api/system-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message || '系统设定保存成功', 'success');
            } else {
                showMessage(data.error || '保存失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error saving system settings:', error);
            showMessage('保存失败', 'error');
        });
    }

    // 清理过期聊天记录
    cleanupChatRecords() {
        if (!checkPermission('can_manage_system_settings')) {
            showMessage('权限不足', 'error');
            return;
        }

        if (!confirm('确定要立即清理过期的聊天记录吗？此操作不可恢复。')) {
            return;
        }

        fetch('/api/cleanup-chat-records', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message || '清理完成', 'success');
            } else {
                showMessage(data.error || '清理失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error cleaning up chat records:', error);
            showMessage('清理失败', 'error');
        });
    }

    // 加载注册设置
    loadRegistrationSettings() {
        if (!checkPermission('can_manage_system_settings')) {
            return;
        }

        fetch('/api/registration-enabled')
            .then(response => response.json())
            .then(data => {
                const toggle = document.getElementById('open-registration-toggle');
                if (toggle) {
                    toggle.checked = data.enabled || false;
                }
            })
            .catch(error => {
                console.error('Error loading registration settings:', error);
                showMessage('加载注册设置失败', 'error');
            });
    }

    // 保存注册设置
    saveRegistrationSettings() {
        if (!checkPermission('can_manage_system_settings')) {
            showNoPermission('system-settings', 1, '系统设定');
            return;
        }

        const toggle = document.getElementById('open-registration-toggle');
        const enabled = toggle ? toggle.checked : false;

        fetch('/api/system-settings/registration', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                open_registration: enabled
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showMessage(data.error, 'error');
            } else {
                showMessage('注册设置保存成功', 'success');
            }
        })
        .catch(error => {
            console.error('Error saving registration settings:', error);
            showMessage('保存注册设置失败', 'error');
        });
    }
}

// 创建全局实例
window.AdminSystemSettingsModule = AdminSystemSettingsModule;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    if (window.AdminSystemSettingsModule) {
        window.adminSystemSettingsModule = new AdminSystemSettingsModule();
    }
});
