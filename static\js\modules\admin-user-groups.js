// 用户组管理模块
class AdminUserGroupsModule {
    constructor() {
        this.dependencies = ['createModal', 'closeModal', 'showMessage', 'checkPermission', 'showNoPermission', 'escapeHTML', 'collectPermissions'];
        this.init();
    }

    init() {
        this.ensureDependencies().then(() => {
            console.log('后台用户组管理模块依赖加载完成');
        });
    }

    async ensureDependencies() {
        for (const dep of this.dependencies) {
            while (typeof window[dep] !== 'function') {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
    }

    // 加载用户组列表
    loadUserGroups() {
        if (!checkPermission('can_manage_user_groups')) {
            showNoPermission('user-groups', 3, '用户组管理');
            return;
        }
        const tbody = document.querySelector('#user-groups-table tbody');
        if (!tbody) return;
        tbody.innerHTML = '<tr><td colspan="4" class="empty-state">加载中...</td></tr>';

        // 权限分类和颜色配置
        const permissionCategories = {
            // 后台管理权限 - 蓝色系
            'backend': {
                color: '#3498db',
                bgColor: '#e3f2fd',
                permissions: ['can_manage_backend']
            },
            // 用户管理权限 - 绿色系
            'user_management': {
                color: '#27ae60',
                bgColor: '#e8f5e8',
                permissions: ['can_manage_users', 'can_manage_users_add', 'can_manage_users_edit', 'can_manage_users_delete']
            },
            // 部门管理权限 - 靛蓝色系
            'department_management': {
                color: '#3f51b5',
                bgColor: '#e8eaf6',
                permissions: ['can_manage_departments', 'can_manage_departments_add', 'can_manage_departments_edit', 'can_manage_departments_delete']
            },
            // 用户组管理权限 - 紫色系
            'group_management': {
                color: '#9b59b6',
                bgColor: '#f3e5f5',
                permissions: ['can_manage_user_groups', 'can_manage_user_groups_add', 'can_manage_user_groups_edit', 'can_manage_user_groups_delete']
            },
            // 标签管理权限 - 青色系
            'label_management': {
                color: '#1abc9c',
                bgColor: '#e0f2f1',
                permissions: ['can_manage_labels', 'can_manage_labels_add', 'can_manage_labels_edit', 'can_manage_labels_delete']
            },
            // 公告管理权限 - 红色系
            'announcement_management': {
                color: '#e74c3c',
                bgColor: '#ffebee',
                permissions: ['can_manage_announcements', 'can_manage_announcements_add', 'can_manage_announcements_edit', 'can_manage_announcements_delete']
            },
            // 反馈管理权限 - 深紫色系
            'feedback_management': {
                color: '#673ab7',
                bgColor: '#ede7f6',
                permissions: ['can_view_feedback', 'can_reply_feedback', 'can_delete_feedback']
            },
            // 审计日志权限 - 灰色系
            'audit_management': {
                color: '#34495e',
                bgColor: '#eceff1',
                permissions: ['can_view_audit_log', 'can_clear_audit_log']
            },
            // 条形码管理权限 - 棕色系
            'barcode_management': {
                color: '#8d6e63',
                bgColor: '#efebe9',
                permissions: ['can_manage_barcode_prefixes', 'can_manage_barcode_prefixes_add', 'can_manage_barcode_prefixes_edit', 'can_manage_barcode_prefixes_delete']
            },
            // 聊天机器人管理权限 - 深蓝色系
            'chatbot_management': {
                color: '#2196f3',
                bgColor: '#e1f5fe',
                permissions: ['can_manage_chatbot_kb', 'can_manage_chatbot_kb_add', 'can_manage_chatbot_kb_edit', 'can_manage_chatbot_kb_delete']
            },
            // Problem Solve权限 - 橙色系
            'problem_solve': {
                color: '#f39c12',
                bgColor: '#fef9e7',
                permissions: ['can_view_problem_solve', 'can_reply_problem_solve', 'can_delete_problem_solve']
            },
            // 前台功能权限 - 绿松石色系
            'frontend_features': {
                color: '#00bcd4',
                bgColor: '#e0f7fa',
                permissions: ['can_print_labels', 'can_search_labels', 'can_copy_labels', 'can_use_chat', 'can_chat_robot', 'can_chat_public', 'can_review_knowledge', 'can_review_knowledge_submissions']
            },
            // 数据查看权限 - 深绿色系
            'data_access': {
                color: '#4caf50',
                bgColor: '#e8f5e8',
                permissions: ['can_view_statistics', 'can_view_reports', 'can_export_data']
            },
            // 系统设置权限 - 粉色系
            'system_settings': {
                color: '#e91e63',
                bgColor: '#fce4ec',
                permissions: ['can_refresh_permissions', 'can_backup_restore', 'can_manage_security', 'can_manage_system_settings']
            }
        };

        // 权限名称映射（与 admin.js 权限映射保持一致，补充所有缺失项）
        const permissionNames = {
            // 后台管理权限
            'can_manage_backend': '后台管理入口',
            'can_manage_users': '用户管理',
            'can_manage_users_add': '添加用户',
            'can_manage_users_edit': '编辑用户',
            'can_manage_users_delete': '删除用户',
            'can_manage_departments': '部门管理',
            'can_manage_departments_add': '添加部门',
            'can_manage_departments_edit': '编辑部门',
            'can_manage_departments_delete': '删除部门',
            'can_manage_user_groups': '用户组管理',
            'can_manage_user_groups_add': '添加用户组',
            'can_manage_user_groups_edit': '编辑用户组',
            'can_manage_user_groups_delete': '删除用户组',
            'can_manage_labels': '标签管理',
            'can_manage_labels_add': '添加标签',
            'can_manage_labels_edit': '编辑标签',
            'can_manage_labels_delete': '删除标签',
            'can_manage_announcements': '公告管理',
            'can_manage_announcements_add': '添加公告',
            'can_manage_announcements_edit': '编辑公告',
            'can_manage_announcements_delete': '删除公告',
            'can_view_feedback': '查看反馈',
            'can_reply_feedback': '回复反馈',
            'can_delete_feedback': '删除反馈',
            'can_view_audit_log': '操作日志',
            'can_clear_audit_log': '清空操作日志',
            'can_manage_barcode_prefixes': '条形码前缀管理',
            'can_manage_barcode_prefixes_add': '添加条形码前缀',
            'can_manage_barcode_prefixes_edit': '编辑条形码前缀',
            'can_manage_barcode_prefixes_delete': '删除条形码前缀',
            'can_manage_chatbot_kb': '聊天机器人词库管理',
            'can_manage_chatbot_kb_add': '添加词条',
            'can_manage_chatbot_kb_edit': '编辑词条',
            'can_manage_chatbot_kb_delete': '删除词条',
            // 前台功能权限
            'can_view_problem_solve': '显示Problem Solve按钮',
            'can_reply_problem_solve': 'Problem Solve回复权限',
            'can_delete_problem_solve': 'Problem Solve删除权限',
            'can_print_labels': '标签打印权限',
            'can_search_labels': '标签搜索权限',
            'can_copy_labels': '标签复制权限',
            'can_use_chat': '聊天权限',
            'can_chat_robot': '机器人聊天',
            'can_chat_public': '公众聊天',
            'can_review_knowledge': '词库审核',
            'can_review_knowledge_submissions': '词库提交',
            // 数据查看权限
            'can_view_statistics': '查看统计数据',
            'can_view_reports': '查看报表',
            'can_export_data': '导出数据',
            // 系统设置权限
            'can_refresh_permissions': '刷新权限',
            'can_backup_restore': '备份恢复',
            'can_manage_security': '安全设置',
            'can_manage_system_settings': '系统设定'
        };

        // 获取权限的分类信息
        function getPermissionCategory(permission) {
            for (const [categoryKey, categoryInfo] of Object.entries(permissionCategories)) {
                if (categoryInfo.permissions.includes(permission)) {
                    return categoryInfo;
                }
            }
            // 默认分类
            return {
                color: '#607d8b',
                bgColor: '#f5f5f5'
            };
        }

        // 获取权限的排序顺序
        function getPermissionOrder(dbFieldName) {
            // 直接使用类的静态属性
            const PERMISSION_GROUPS = [
                {
                    label: '后台管理权限',
                    icon: '🔧',
                    permissions: [
                        { id: 'perm-backend', label: '后台管理入口' },
                        { id: 'perm-users', label: '用户管理', subs: [
                            { id: 'perm-users-add', label: '添加用户' },
                            { id: 'perm-users-edit', label: '编辑用户' },
                            { id: 'perm-users-delete', label: '删除用户' }
                        ]},
                        { id: 'perm-departments', label: '部门管理', subs: [
                            { id: 'perm-departments-add', label: '添加部门' },
                            { id: 'perm-departments-edit', label: '编辑部门' },
                            { id: 'perm-departments-delete', label: '删除部门' }
                        ]},
                        { id: 'perm-user-groups', label: '用户组管理', subs: [
                            { id: 'perm-user-groups-add', label: '添加用户组' },
                            { id: 'perm-user-groups-edit', label: '编辑用户组' },
                            { id: 'perm-user-groups-delete', label: '删除用户组' }
                        ]},
                        { id: 'perm-labels', label: '标签管理', subs: [
                            { id: 'perm-labels-add', label: '添加标签' },
                            { id: 'perm-labels-edit', label: '编辑标签' },
                            { id: 'perm-labels-delete', label: '删除标签' }
                        ]},
                        { id: 'perm-announcements', label: '公告管理', subs: [
                            { id: 'perm-announcements-add', label: '添加公告' },
                            { id: 'perm-announcements-edit', label: '编辑公告' },
                            { id: 'perm-announcements-delete', label: '删除公告' }
                        ]},
                        { id: 'perm-feedback', label: '反馈管理', subs: [
                            { id: 'perm-feedback-view', label: '查看反馈' },
                            { id: 'perm-feedback-reply', label: '回复反馈' },
                            { id: 'perm-feedback-delete', label: '删除反馈' }
                        ]},
                        { id: 'perm-barcode-prefixes', label: '条形码前缀管理', subs: [
                            { id: 'perm-barcode-prefixes-add', label: '添加前缀' },
                            { id: 'perm-barcode-prefixes-edit', label: '编辑前缀' },
                            { id: 'perm-barcode-prefixes-delete', label: '删除前缀' }
                        ]},
                        { id: 'perm-chatbot-kb', label: '聊天机器人词库管理', subs: [
                            { id: 'perm-chatbot-kb-add', label: '添加词条' },
                            { id: 'perm-chatbot-kb-edit', label: '编辑词条' },
                            { id: 'perm-chatbot-kb-delete', label: '删除词条' }
                        ]},
                        { id: 'perm-audit-log', label: '操作日志' },
                        { id: 'perm-audit-log-clear', label: '清空操作日志' }
                    ]
                },
                {
                    label: '前台功能权限',
                    icon: '🖥️',
                    permissions: [
                        { id: 'perm-problem-solve', label: '显示Problem Solve按钮' },
                        { id: 'perm-problem-solve-reply', label: 'Problem Solve回复权限' },
                        { id: 'perm-problem-solve-delete', label: 'Problem Solve删除权限' },
                        { id: 'perm-label-print', label: '标签打印权限' },
                        { id: 'perm-label-search', label: '标签搜索权限' },
                        { id: 'perm-label-copy', label: '标签复制权限' },
                        { id: 'perm-chat', label: '聊天权限', subs: [
                            { id: 'perm-chat-robot', label: '机器人聊天' },
                            { id: 'perm-chat-public', label: '公众聊天' },
                            { id: 'perm-chat-knowledge-review', label: '词库审核' },
                            { id: 'perm-chat-knowledge-submit', label: '词库提交' }
                        ]}
                    ]
                },
                {
                    label: '数据查看权限',
                    icon: '📊',
                    permissions: [
                        { id: 'perm-view-statistics', label: '查看统计数据' },
                        { id: 'perm-view-reports', label: '查看报表' },
                        { id: 'perm-export-data', label: '导出数据' }
                    ]
                },
                {
                    label: '系统设置权限',
                    icon: '⚙️',
                    permissions: [
                        { id: 'perm-refresh-permissions', label: '刷新权限' },
                        { id: 'perm-backup-restore', label: '备份恢复' },
                        { id: 'perm-security-settings', label: '安全设置' },
                        { id: 'perm-system-settings', label: '系统设定' }
                    ]
                }
            ];

            // 权限ID到数据库字段的映射
            const getDbFieldByPermId = (permId) => {
                const mapping = {
                    'perm-backend': 'can_manage_backend',
                    'perm-users': 'can_manage_users',
                    'perm-users-add': 'can_manage_users_add',
                    'perm-users-edit': 'can_manage_users_edit',
                    'perm-users-delete': 'can_manage_users_delete',
                    'perm-departments': 'can_manage_departments',
                    'perm-departments-add': 'can_manage_departments_add',
                    'perm-departments-edit': 'can_manage_departments_edit',
                    'perm-departments-delete': 'can_manage_departments_delete',
                    'perm-user-groups': 'can_manage_user_groups',
                    'perm-user-groups-add': 'can_manage_user_groups_add',
                    'perm-user-groups-edit': 'can_manage_user_groups_edit',
                    'perm-user-groups-delete': 'can_manage_user_groups_delete',
                    'perm-labels': 'can_manage_labels',
                    'perm-labels-add': 'can_manage_labels_add',
                    'perm-labels-edit': 'can_manage_labels_edit',
                    'perm-labels-delete': 'can_manage_labels_delete',
                    'perm-announcements': 'can_manage_announcements',
                    'perm-announcements-add': 'can_manage_announcements_add',
                    'perm-announcements-edit': 'can_manage_announcements_edit',
                    'perm-announcements-delete': 'can_manage_announcements_delete',
                    'perm-feedback': 'can_view_feedback',
                    'perm-feedback-view': 'can_view_feedback',
                    'perm-feedback-reply': 'can_reply_feedback',
                    'perm-feedback-delete': 'can_delete_feedback',
                    'perm-audit-log': 'can_view_audit_log',
                    'perm-audit-log-clear': 'can_clear_audit_log',
                    'perm-barcode-prefixes': 'can_manage_barcode_prefixes',
                    'perm-barcode-prefixes-add': 'can_manage_barcode_prefixes_add',
                    'perm-barcode-prefixes-edit': 'can_manage_barcode_prefixes_edit',
                    'perm-barcode-prefixes-delete': 'can_manage_barcode_prefixes_delete',
                    'perm-chatbot-kb': 'can_manage_chatbot_kb',
                    'perm-chatbot-kb-add': 'can_manage_chatbot_kb_add',
                    'perm-chatbot-kb-edit': 'can_manage_chatbot_kb_edit',
                    'perm-chatbot-kb-delete': 'can_manage_chatbot_kb_delete',
                    'perm-problem-solve': 'can_view_problem_solve',
                    'perm-problem-solve-reply': 'can_reply_problem_solve',
                    'perm-problem-solve-delete': 'can_delete_problem_solve',
                    'perm-label-print': 'can_print_labels',
                    'perm-label-search': 'can_search_labels',
                    'perm-label-copy': 'can_copy_labels',
                    'perm-chat': 'can_use_chat',
                    'perm-chat-robot': 'can_chat_robot',
                    'perm-chat-public': 'can_chat_public',
                    'perm-chat-knowledge-review': 'can_review_knowledge',
                    'perm-chat-knowledge-submit': 'can_submit_knowledge',
                    'perm-view-statistics': 'can_view_statistics',
                    'perm-view-reports': 'can_view_reports',
                    'perm-export-data': 'can_export_data',
                    'perm-refresh-permissions': 'can_refresh_permissions',
                    'perm-backup-restore': 'can_backup_restore',
                    'perm-security-settings': 'can_manage_security',
                    'perm-system-settings': 'can_manage_system_settings'
                };
                return mapping[permId];
            };

            let order = 0;
            for (const group of PERMISSION_GROUPS) {
                for (const perm of group.permissions) {
                    const dbField = getDbFieldByPermId(perm.id);
                    if (dbField === dbFieldName) {
                        return order;
                    }
                    order++;
                    if (perm.subs) {
                        for (const sub of perm.subs) {
                            const subDbField = getDbFieldByPermId(sub.id);
                            if (subDbField === dbFieldName) {
                                return order;
                            }
                            order++;
                        }
                    }
                }
            }
            return 9999; // 未找到的权限排在最后
        }

        // 生成带颜色的权限标签
        function createPermissionTag(permission, displayName) {
            const category = getPermissionCategory(permission);
            return `<span class="permission-tag" style="
                background-color: ${category.bgColor};
                color: ${category.color};
                border: 1px solid ${category.color}30;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 0.85em;
                font-weight: 500;
                margin: 1px 2px;
                display: inline-block;
                white-space: nowrap;
            ">${displayName}</span>`;
        }

        fetch('/api/user-groups')
            .then(response => response.json())
            .then(groups => {
                if (!Array.isArray(groups)) {
                    tbody.innerHTML = '<tr><td colspan="4" class="empty-state">加载用户组失败：数据格式错误。</td></tr>';
                    return;
                }
                if (groups.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="4" class="empty-state">暂无用户组数据</td></tr>';
                    return;
                }
                tbody.innerHTML = groups.map(group => {
                    let permissions = '<span class="no-permissions" style="color: #999; font-style: italic;">无权限</span>';
                    if (group.permissions) {
                        const activePermissions = Object.keys(group.permissions)
                            .filter(p => group.permissions[p])
                            .map(p => {
                                const displayName = permissionNames[p] || p;
                                return {
                                    permission: p,
                                    displayName: displayName,
                                    order: getPermissionOrder(p)
                                };
                            })
                            .sort((a, b) => a.order - b.order)
                            .map(item => createPermissionTag(item.permission, item.displayName));
                        permissions = activePermissions.length > 0 ? activePermissions.join('') : '<span class="no-permissions" style="color: #999; font-style: italic;">无权限</span>';
                    }
                    return `
                        <tr>
                            <td>${group.id}</td>
                            <td>${escapeHTML(group.name)}</td>
                            <td class="permissions-cell">${permissions}</td>
                            <td>
                                <button class="btn-edit" onclick="adminUserGroupsModule.editUserGroup(${group.id})">编辑</button>
                                <button class="btn-delete" onclick="adminUserGroupsModule.deleteUserGroup(${group.id})">删除</button>
                            </td>
                        </tr>
                    `;
                }).join('');
            })
            .catch(error => {
                console.error('加载用户组失败:', error);
                tbody.innerHTML = `<tr><td colspan="4" class="empty-state">加载用户组失败: ${error.message}</td></tr>`;
            });
    }

    // 权限分组定义
    static PERMISSION_GROUPS = [
        {
            label: '后台管理权限',
            icon: '🔧',
            permissions: [
                { id: 'perm-backend', label: '后台管理入口' },
                { id: 'perm-users', label: '用户管理', subs: [
                    { id: 'perm-users-add', label: '添加用户' },
                    { id: 'perm-users-edit', label: '编辑用户' },
                    { id: 'perm-users-delete', label: '删除用户' }
                ]},
                { id: 'perm-departments', label: '部门管理', subs: [
                    { id: 'perm-departments-add', label: '添加部门' },
                    { id: 'perm-departments-edit', label: '编辑部门' },
                    { id: 'perm-departments-delete', label: '删除部门' }
                ]},
                { id: 'perm-user-groups', label: '用户组管理', subs: [
                    { id: 'perm-user-groups-add', label: '添加用户组' },
                    { id: 'perm-user-groups-edit', label: '编辑用户组' },
                    { id: 'perm-user-groups-delete', label: '删除用户组' }
                ]},
                { id: 'perm-labels', label: '标签管理', subs: [
                    { id: 'perm-labels-add', label: '添加标签' },
                    { id: 'perm-labels-edit', label: '编辑标签' },
                    { id: 'perm-labels-delete', label: '删除标签' }
                ]},
                { id: 'perm-category-codes', label: '分类中心', subs: [
                    { id: 'perm-category-codes-add', label: '添加分类代码' },
                    { id: 'perm-category-codes-edit', label: '编辑分类代码' },
                    { id: 'perm-category-codes-delete', label: '删除分类代码' }
                ]},
                { id: 'perm-announcements', label: '公告管理', subs: [
                    { id: 'perm-announcements-add', label: '添加公告' },
                    { id: 'perm-announcements-edit', label: '编辑公告' },
                    { id: 'perm-announcements-delete', label: '删除公告' }
                ]},
                { id: 'perm-feedback', label: '反馈管理', subs: [
                    { id: 'perm-feedback-view', label: '查看反馈' },
                    { id: 'perm-feedback-reply', label: '回复反馈' },
                    { id: 'perm-feedback-delete', label: '删除反馈' }
                ]},
                { id: 'perm-barcode-prefixes', label: '条形码前缀管理', subs: [
                    { id: 'perm-barcode-prefixes-add', label: '添加前缀' },
                    { id: 'perm-barcode-prefixes-edit', label: '编辑前缀' },
                    { id: 'perm-barcode-prefixes-delete', label: '删除前缀' }
                ]},
                { id: 'perm-chatbot-kb', label: '聊天机器人词库管理', subs: [
                    { id: 'perm-chatbot-kb-add', label: '添加词条' },
                    { id: 'perm-chatbot-kb-edit', label: '编辑词条' },
                    { id: 'perm-chatbot-kb-delete', label: '删除词条' }
                ]},
                { id: 'perm-audit-log', label: '操作日志' },
                { id: 'perm-audit-log-clear', label: '清空操作日志' }
            ]
        },
        {
            label: '前台功能权限',
            icon: '🖥️',
            permissions: [
                { id: 'perm-problem-solve', label: '显示Problem Solve按钮' },
                { id: 'perm-problem-solve-reply', label: 'Problem Solve回复权限' },
                { id: 'perm-problem-solve-delete', label: 'Problem Solve删除权限' },
                { id: 'perm-label-print', label: '标签打印权限' },
                { id: 'perm-label-search', label: '标签搜索权限' },
                { id: 'perm-label-copy', label: '标签复制权限' },
                { id: 'perm-chat', label: '聊天权限', subs: [
                    { id: 'perm-chat-robot', label: '机器人聊天' },
                    { id: 'perm-chat-public', label: '公众聊天' },
                    { id: 'perm-chat-knowledge-review', label: '词库审核' },
                    { id: 'perm-chat-knowledge-submit', label: '词库提交' }
                ]}
            ]
        },
        {
            label: '数据查看权限',
            icon: '📊',
            permissions: [
                { id: 'perm-view-statistics', label: '查看统计数据' },
                { id: 'perm-view-reports', label: '查看报表' },
                { id: 'perm-export-data', label: '导出数据' }
            ]
        },
        {
            label: '系统设置权限',
            icon: '⚙️',
            permissions: [
                { id: 'perm-refresh-permissions', label: '刷新权限' },
                { id: 'perm-backup-restore', label: '备份恢复' },
                { id: 'perm-security-settings', label: '安全设置' },
                { id: 'perm-system-settings', label: '系统设定' }
            ]
        }
    ];

    static getDbFieldByPermId(permId) {
        const mapping = {
            'perm-backend': 'can_manage_backend',
            'perm-users': 'can_manage_users',
            'perm-users-add': 'can_manage_users_add',
            'perm-users-edit': 'can_manage_users_edit',
            'perm-users-delete': 'can_manage_users_delete',
            'perm-departments': 'can_manage_departments',
            'perm-departments-add': 'can_manage_departments_add',
            'perm-departments-edit': 'can_manage_departments_edit',
            'perm-departments-delete': 'can_manage_departments_delete',
            'perm-user-groups': 'can_manage_user_groups',
            'perm-user-groups-add': 'can_manage_user_groups_add',
            'perm-user-groups-edit': 'can_manage_user_groups_edit',
            'perm-user-groups-delete': 'can_manage_user_groups_delete',
            'perm-labels': 'can_manage_labels',
            'perm-labels-add': 'can_manage_labels_add',
            'perm-labels-edit': 'can_manage_labels_edit',
            'perm-labels-delete': 'can_manage_labels_delete',
            'perm-category-codes': 'can_manage_category_center',
            'perm-category-codes-add': 'can_manage_category_center_add',
            'perm-category-codes-edit': 'can_manage_category_center_edit',
            'perm-category-codes-delete': 'can_manage_category_center_delete',
            'perm-announcements': 'can_manage_announcements',
            'perm-announcements-add': 'can_manage_announcements_add',
            'perm-announcements-edit': 'can_manage_announcements_edit',
            'perm-announcements-delete': 'can_manage_announcements_delete',
            'perm-feedback': 'can_view_feedback',
            'perm-feedback-view': 'can_view_feedback',
            'perm-feedback-reply': 'can_reply_feedback',
            'perm-feedback-delete': 'can_delete_feedback',
            'perm-audit-log': 'can_view_audit_log',
            'perm-audit-log-clear': 'can_clear_audit_log',
            'perm-barcode-prefixes': 'can_manage_barcode_prefixes',
            'perm-barcode-prefixes-add': 'can_manage_barcode_prefixes_add',
            'perm-barcode-prefixes-edit': 'can_manage_barcode_prefixes_edit',
            'perm-barcode-prefixes-delete': 'can_manage_barcode_prefixes_delete',
            'perm-chatbot-kb': 'can_manage_chatbot_kb',
            'perm-chatbot-kb-add': 'can_manage_chatbot_kb_add',
            'perm-chatbot-kb-edit': 'can_manage_chatbot_kb_edit',
            'perm-chatbot-kb-delete': 'can_manage_chatbot_kb_delete',
            'perm-problem-solve': 'can_view_problem_solve',
            'perm-problem-solve-reply': 'can_reply_problem_solve',
            'perm-problem-solve-delete': 'can_delete_problem_solve',
            'perm-label-print': 'can_print_labels',
            'perm-label-search': 'can_search_labels',
            'perm-label-copy': 'can_copy_labels',
            'perm-chat': 'can_use_chat',
            'perm-chat-robot': 'can_chat_robot',
            'perm-chat-public': 'can_chat_public',
            'perm-chat-knowledge-review': 'can_review_knowledge',
            'perm-chat-knowledge-submit': 'can_submit_knowledge',
            'perm-view-statistics': 'can_view_statistics',
            'perm-view-reports': 'can_view_reports',
            'perm-export-data': 'can_export_data',
            'perm-refresh-permissions': 'can_refresh_permissions',
            'perm-backup-restore': 'can_backup_restore',
            'perm-security-settings': 'can_manage_security',
            'perm-system-settings': 'can_manage_system_settings'
        };
        return mapping[permId];
    }

    renderPermissionsEditor(container, permissions = {}) {
        let html = '<div class="permissions-groups-vertical">';
        const rightPermIds = ['perm-label-search', 'perm-label-print', 'perm-label-copy'];
        AdminUserGroupsModule.PERMISSION_GROUPS.forEach((group, idx) => {
            const isBackend = group.label === '后台管理权限';
            const isFrontend = group.label === '前台功能权限';
            const groupTitleStyle = `
                font-size:1.18em;
                font-weight:700;
                color:#4B3FA7;
                letter-spacing:1px;
                padding:12px 0 12px 18px;
                margin-bottom:10px;
                ${idx!==0?'margin-top:18px;':''}
                border-left:5px solid #667eea;
                background:linear-gradient(90deg,#f0f4ff 0%,#e9ecef 60%,#f8f9fa 100%);
                border-radius:10px 24px 24px 10px;
                box-shadow:0 2px 12px 0 rgba(102,126,234,0.07);
                display:flex;align-items:center;min-height:40px;`;
            html += `<div class="permissions-group-block">
                <div class="permissions-group-title" style="${groupTitleStyle}">
                    <span style="display:flex;align-items:center;gap:8px;">
                        <span style=\"font-size:1.3em;\">${group.icon}</span> ${group.label}
                    </span>
                    <span style="flex:1 1 0;"></span>
                    ${isBackend ? `<label class='permission-item' style='margin-right:32px;gap:6px;font-weight:500;background:transparent !important;box-shadow:none !important;'>` +
                        `<input type='checkbox' id='perm-backend' class='main-permission' ${permissions['can_manage_backend'] ? 'checked' : ''}> 后台管理入口</label>` : ''}
                </div>
                <div class="permissions-group-list" style="display:grid;grid-template-columns:1fr 1fr;gap:10px 24px;">`;
            if (isFrontend) {
                html += `<div class="permissions-group">
                    <label class="permission-item"><input type="checkbox" id="perm-problem-solve" class="main-permission" ${permissions['can_view_problem_solve'] ? 'checked' : ''}> 显示Problem Solve按钮</label>
                    <div class="sub-permissions" data-parent="perm-problem-solve">
                        <label class="permission-item sub-item"><input type="checkbox" id="perm-problem-solve-reply" class="sub-permission" ${permissions['can_reply_problem_solve'] ? 'checked' : ''}> Problem Solve回复权限</label>
                        <label class="permission-item sub-item"><input type="checkbox" id="perm-problem-solve-delete" class="sub-permission" ${permissions['can_delete_problem_solve'] ? 'checked' : ''}> Problem Solve删除权限</label>
                    </div>
                </div>`;
                html += `<div class="permissions-group" style="display:flex;flex-direction:column;justify-content:center;height:100%;gap:8px;">
                    <label class="permission-item"><input type="checkbox" id="perm-label-search" class="main-permission" ${permissions['can_search_labels'] ? 'checked' : ''}> 标签搜索权限</label>
                    <label class="permission-item"><input type="checkbox" id="perm-label-print" class="main-permission" ${permissions['can_print_labels'] ? 'checked' : ''}> 标签打印权限</label>
                    <label class="permission-item"><input type="checkbox" id="perm-label-copy" class="main-permission" ${permissions['can_copy_labels'] ? 'checked' : ''}> 标签复制权限</label>
                </div>`;
                html += `<div class="permissions-group">
                    <label class="permission-item"><input type="checkbox" id="perm-chat" class="main-permission chat-main-permission" ${permissions['can_use_chat'] ? 'checked' : ''}> 聊天权限</label>
                    <div class="sub-permissions" data-parent="perm-chat">
                        <label class="permission-item sub-item"><input type="checkbox" id="perm-chat-robot" class="sub-permission chat-sub-permission" ${permissions['can_chat_robot'] ? 'checked' : ''}> 机器人聊天</label>
                        <label class="permission-item sub-item"><input type="checkbox" id="perm-chat-public" class="sub-permission chat-sub-permission" ${permissions['can_chat_public'] ? 'checked' : ''}> 公众聊天</label>
                        <label class="permission-item sub-item"><input type="checkbox" id="perm-chat-knowledge-review" class="sub-permission chat-sub-permission" ${permissions['can_review_knowledge'] ? 'checked' : ''}> 词库审核</label>
                        <label class="permission-item sub-item"><input type="checkbox" id="perm-chat-knowledge-submit" class="sub-permission chat-sub-permission" ${permissions['can_submit_knowledge'] ? 'checked' : ''}> 词库提交</label>
                    </div>
                </div>`;
                html += `</div></div>`;
                return;
            }
            let leftCol = '', rightCol = '';
            group.permissions.forEach(perm => {
                if (isBackend && perm.id === 'perm-backend') return;
                if (perm.id === 'perm-audit-log') {
                    leftCol += `<div class=\"permissions-group\">
                        <label class=\"permission-item\"><input type=\"checkbox\" id=\"${perm.id}\" class=\"main-permission\" ${permissions[AdminUserGroupsModule.getDbFieldByPermId(perm.id)] ? 'checked' : ''}> ${perm.label}</label>
                        <div class=\"sub-permissions\" data-parent=\"${perm.id}\">\n                            <label class=\"permission-item sub-item\"><input type=\"checkbox\" id=\"perm-audit-log-clear\" class=\"sub-permission\" ${permissions['can_clear_audit_log'] ? 'checked' : ''}> 清空操作日志</label>\n                        </div>
                    </div>`;
                    return;
                }
                if (perm.subs) {
                    leftCol += `<div class=\"permissions-group\">
                        <label class=\"permission-item\"><input type=\"checkbox\" id=\"${perm.id}\" class=\"main-permission\" ${permissions[AdminUserGroupsModule.getDbFieldByPermId(perm.id)] ? 'checked' : ''}> ${perm.label}</label>
                        <div class=\"sub-permissions\" data-parent=\"${perm.id}\">`;
                    perm.subs.forEach(sub => {
                        leftCol += `<label class=\"permission-item sub-item\"><input type=\"checkbox\" id=\"${sub.id}\" class=\"sub-permission\" ${permissions[AdminUserGroupsModule.getDbFieldByPermId(sub.id)] ? 'checked' : ''}> ${sub.label}</label>`;
                    });
                    leftCol += `</div></div>`;
                } else if (perm.id !== 'perm-audit-log-clear') {
                    if (rightPermIds.includes(perm.id)) {
                        rightCol += `<label class=\"permission-item\"><input type=\"checkbox\" id=\"${perm.id}\" class=\"main-permission\" ${permissions[AdminUserGroupsModule.getDbFieldByPermId(perm.id)] ? 'checked' : ''}> ${perm.label}</label>`;
                    } else {
                        leftCol += `<label class=\"permission-item\"><input type=\"checkbox\" id=\"${perm.id}\" class=\"main-permission\" ${permissions[AdminUserGroupsModule.getDbFieldByPermId(perm.id)] ? 'checked' : ''}> ${perm.label}</label>`;
                    }
                }
            });
            html += leftCol + rightCol;
            html += `</div></div>`;
        });
        html += '</div>';
        container.innerHTML = html;

        // 添加聊天权限的特殊逻辑
        this.setupChatPermissionLogic(container);
    }

    // 设置聊天权限的特殊逻辑
    setupChatPermissionLogic(container) {
        const chatMainCheckbox = container.querySelector('#perm-chat');
        const chatRobotCheckbox = container.querySelector('#perm-chat-robot');
        const chatPublicCheckbox = container.querySelector('#perm-chat-public');
        const chatKnowledgeReviewCheckbox = container.querySelector('#perm-chat-knowledge-review');
        const chatKnowledgeSubmitCheckbox = container.querySelector('#perm-chat-knowledge-submit');

        if (!chatMainCheckbox || !chatRobotCheckbox || !chatPublicCheckbox || !chatKnowledgeReviewCheckbox || !chatKnowledgeSubmitCheckbox) {
            return; // 如果找不到聊天权限复选框，直接返回
        }

        // 子权限变化时的处理逻辑
        const handleSubPermissionChange = () => {
            const robotChecked = chatRobotCheckbox.checked;
            const publicChecked = chatPublicCheckbox.checked;
            const knowledgeReviewChecked = chatKnowledgeReviewCheckbox.checked;
            const knowledgeSubmitChecked = chatKnowledgeSubmitCheckbox.checked;

            // 如果任何一个子权限被勾选，主权限也要被勾选
            if (robotChecked || publicChecked || knowledgeReviewChecked || knowledgeSubmitChecked) {
                chatMainCheckbox.checked = true;
            }
            // 注意：这里不会自动取消主权限的勾选，因为主权限可以单独勾选
        };

        // 主权限变化时的处理逻辑
        const handleMainPermissionChange = () => {
            // 如果主权限被取消勾选，所有子权限也要被取消勾选
            if (!chatMainCheckbox.checked) {
                chatRobotCheckbox.checked = false;
                chatPublicCheckbox.checked = false;
                chatKnowledgeReviewCheckbox.checked = false;
                chatKnowledgeSubmitCheckbox.checked = false;
            }
        };

        // 绑定事件监听器
        chatRobotCheckbox.addEventListener('change', handleSubPermissionChange);
        chatPublicCheckbox.addEventListener('change', handleSubPermissionChange);
        chatKnowledgeReviewCheckbox.addEventListener('change', handleSubPermissionChange);
        chatKnowledgeSubmitCheckbox.addEventListener('change', handleSubPermissionChange);
        chatMainCheckbox.addEventListener('change', handleMainPermissionChange);
    }

    // 显示添加用户组弹窗
    showAddUserGroupModal() {
        const modal = createModal('添加用户组', `
            <div class="form-group">
                <label for="group-name">用户组名称</label>
                <input type="text" id="group-name" required>
            </div>
            <div class="permissions-container">
                <div id="permissions-editor"></div>
            </div>
        `);
        // 自动渲染权限勾选框
        this.renderPermissionsEditor(modal.querySelector('#permissions-editor'), {});
        if (typeof setupPermissionLogic === 'function') {
            setupPermissionLogic(modal);
        }
        modal.querySelector('.btn-save').onclick = () => {
            const name = document.getElementById('group-name').value;
            if (!name) {
                alert('请填写用户组名称');
                return;
            }
            const permissions = collectPermissions(modal);
            this.createUserGroup({ name, permissions });
            closeModal(modal);
        };
    }

    // 创建用户组
    createUserGroup(groupData) {
        fetch('/api/user-groups', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(groupData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('用户组创建成功', 'success');
                this.loadUserGroups();
            } else {
                showMessage(data.error || '创建失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error creating user group:', error);
            showMessage('创建失败', 'error');
        });
    }

    // 编辑用户组
    editUserGroup(id) {
        fetch('/api/user-groups')
            .then(response => response.json())
            .then(groups => {
                const group = groups.find(g => g.id === id);
                if (!group) {
                    showMessage('用户组不存在', 'error');
                    return;
                }
                const modal = createModal('编辑用户组', `
                    <div class="form-group">
                        <label for="group-name">用户组名称</label>
                        <input type="text" id="group-name" value="${escapeHTML(group.name)}" required>
                    </div>
                    <div class="permissions-container">
                        <div id="permissions-editor"></div>
                    </div>
                `);
                // 自动渲染权限勾选框
                this.renderPermissionsEditor(modal.querySelector('#permissions-editor'), group.permissions || {});
                if (typeof setupPermissionLogic === 'function') {
                    setupPermissionLogic(modal, group.permissions);
                }
                modal.querySelector('.btn-save').onclick = () => {
                    const name = document.getElementById('group-name').value;
                    if (!name) {
                        alert('请填写用户组名称');
                        return;
                    }
                    const permissions = collectPermissions(modal);
                    this.updateUserGroup(id, { name, permissions });
                    closeModal(modal);
                };
            })
            .catch(error => {
                console.error('Error loading user group:', error);
                showMessage('加载用户组失败', 'error');
            });
    }

    // 更新用户组
    updateUserGroup(id, groupData) {
        fetch(`/api/user-groups/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(groupData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('用户组更新成功', 'success');
                this.loadUserGroups();
            } else {
                showMessage(data.error || '更新失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error updating user group:', error);
            showMessage('更新失败', 'error');
        });
    }

    // 删除用户组
    deleteUserGroup(id) {
        if (!confirm('确定要删除这个用户组吗？')) return;
        fetch(`/api/user-groups/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('用户组删除成功', 'success');
                this.loadUserGroups();
            } else {
                showMessage(data.error || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting user group:', error);
            showMessage('删除失败', 'error');
        });
    }

    // 切换权限分类图例显示
    togglePermissionsLegend() {
        const legend = document.getElementById('permissions-legend');
        const toggleText = document.getElementById('legend-toggle-text');

        if (legend.style.display === 'none') {
            legend.style.display = 'flex';
            toggleText.textContent = '隐藏权限分类';
        } else {
            legend.style.display = 'none';
            toggleText.textContent = '显示权限分类';
        }
    }
}

// 挂载到window，便于全局调用
window.adminUserGroupsModule = new AdminUserGroupsModule(); 