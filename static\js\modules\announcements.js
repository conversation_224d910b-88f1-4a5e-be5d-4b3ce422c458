// 公告模块
class AnnouncementsModule {
    constructor() {
        this.announcements = [];
        this.currentIndex = 0;
        this.marquee = null;
        this.isInitialized = false;
    }

    // 初始化公告模块
    init() {
        if (this.isInitialized) return;
        this.marquee = document.querySelector('.marquee p');
        this.loadAnnouncements();
        this.listenForUpdates(); // 开始监听后台更新
        this.isInitialized = true;
    }

    // 监听后台更新
    listenForUpdates() {
        window.addEventListener('storage', (event) => {
            if (event.key === 'announcements_updated') {
                console.log('检测到公告更新，重新加载...');
                this.loadAnnouncements();
            }
        });
    }

    // 加载公告数据
    loadAnnouncements() {
        fetch('/api/announcements')
            .then(response => response.json())
            .then(data => {
                if (data.announcements && data.announcements.length > 0) {
                    this.announcements = data.announcements;
                    this.startAnnouncementCarousel();
                } else {
                    // 如果没有公告，显示默认内容
                    if (this.marquee) {
                        this.marquee.innerHTML = '📢 欢迎使用标签打印系统';
                    }
                }
            })
            .catch(error => {
                console.error('Error loading announcements:', error);
                if (this.marquee) {
                    this.marquee.innerHTML = '📢 公告加载失败';
                }
            });
    }

    // 启动公告轮播
    startAnnouncementCarousel() {
        if (!this.marquee || this.announcements.length === 0) return;
        this.currentIndex = 0;
        this.showNextAnnouncement();
        // 监听动画结束事件，切换到下一条公告
        this.marquee.addEventListener('animationiteration', () => {
            // 轮播到最后一条时，自动刷新公告数据
            if (this.currentIndex >= this.announcements.length) {
                this.loadAnnouncements();
            }
            this.showNextAnnouncement();
        });
    }

    // 显示下一条公告
    showNextAnnouncement() {
        if (!this.marquee || this.announcements.length === 0) return;
        if (this.currentIndex >= this.announcements.length) {
            this.currentIndex = 0;
        }
        const announcement = this.announcements[this.currentIndex];
        this.marquee.innerHTML = announcement.content;
        // 重置动画
        this.marquee.style.animation = 'none';
        this.marquee.offsetHeight; // 触发重排
        this.marquee.style.animation = 'marquee 10s linear infinite';
        this.currentIndex++;
    }
}

// 导出公告模块
window.AnnouncementsModule = AnnouncementsModule; 