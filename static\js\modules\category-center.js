class CategoryCenterModule {
    constructor() {
        this.isInitialized = false;
        this.categoryCodes = [];
        this.filteredCodes = [];
        this.searchTimeout = null;
    }

    // 初始化模块
    init() {
        if (this.isInitialized) return;
        
        this.setupEventListeners();
        this.loadCategoryCodes();
        this.isInitialized = true;
    }

    // 设置事件监听器
    setupEventListeners() {
        // 搜索框事件
        const searchInput = document.getElementById('search-category-codes');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.filterCategoryCodes(e.target.value.trim());
                }, 300);
            });
        }

        // 添加按钮事件
        const addButton = document.getElementById('add-category-code-btn');
        if (addButton) {
            addButton.addEventListener('click', () => this.showAddModal());
        }
    }

    // 加载分类代码
    loadCategoryCodes() {
        fetch('/api/category-codes')
            .then(response => response.json())
            .then(data => {
                if (Array.isArray(data)) {
                    this.categoryCodes = data;
                    this.filteredCodes = [...data];
                    this.renderCategoryCodes();
                } else {
                    this.showMessage(data.error || '加载分类代码失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error loading category codes:', error);
                this.showMessage('加载分类代码失败', 'error');
            });
    }

    // 过滤分类代码
    filterCategoryCodes(query) {
        if (!query) {
            this.filteredCodes = [...this.categoryCodes];
        } else {
            this.filteredCodes = this.categoryCodes.filter(code => 
                code.name.toLowerCase().includes(query.toLowerCase()) ||
                code.copy_content.toLowerCase().includes(query.toLowerCase()) ||
                (code.department_names && code.department_names.toLowerCase().includes(query.toLowerCase()))
            );
        }
        this.renderCategoryCodes();
    }

    // 渲染分类代码列表
    renderCategoryCodes() {
        const container = document.getElementById('category-codes-list');
        if (!container) return;

        if (this.filteredCodes.length === 0) {
            container.innerHTML = '<div class="no-data">没有找到分类代码</div>';
            return;
        }

        container.innerHTML = this.filteredCodes.map(code => `
            <div class="category-code-item" data-id="${code.id}">
                <div class="category-code-content">
                    <div class="category-code-header">
                        <h4 class="category-code-name">${code.name}</h4>
                        <span class="category-code-department">${code.department_names || '通用部门'}</span>
                    </div>
                    <div class="category-code-copy">${code.copy_content}</div>
                    <div class="category-code-date">创建时间: ${new Date(code.created_at).toLocaleString()}</div>
                </div>
                <div class="category-code-actions">
                    <button class="btn-edit" onclick="categoryCenterModule.editCategoryCode(${code.id})">编辑</button>
                    <button class="btn-delete" onclick="categoryCenterModule.deleteCategoryCode(${code.id})">删除</button>
                </div>
            </div>
        `).join('');
    }

    // 显示添加模态框
    showAddModal() {
        this.showCategoryCodeModal('添加分类代码', null);
    }

    // 编辑分类代码
    editCategoryCode(id) {
        const code = this.categoryCodes.find(c => c.id === id);
        if (code) {
            this.showCategoryCodeModal('编辑分类代码', code);
        }
    }

    // 显示分类代码模态框
    showCategoryCodeModal(title, code = null) {
        const isEdit = code !== null;
        
        const modalContent = `
            <div class="form-group">
                <label for="category-name">名称:</label>
                <input type="text" id="category-name" placeholder="输入分类代码名称" value="${code ? code.name : ''}">
            </div>
            <div class="form-group">
                <label for="category-copy-content">复制内容:</label>
                <textarea id="category-copy-content" placeholder="输入复制内容" rows="3">${code ? code.copy_content : ''}</textarea>
            </div>
            <div class="form-group">
                <label>所属部门:</label>
                <div id="category-department-selector" class="department-selector"></div>
            </div>
        `;

        const modal = this.createModal(title, modalContent, 'modal-category-code', () => {
            if (isEdit) {
                this.updateCategoryCode(code.id);
            } else {
                this.addCategoryCode();
            }
        });

        // 加载部门选择器
        this.loadDepartmentSelector('category-department-selector', code ? code.department_names.split(', ').filter(d => d) : []);
    }

    // 添加分类代码
    addCategoryCode() {
        const name = document.getElementById('category-name').value.trim();
        const copyContent = document.getElementById('category-copy-content').value.trim();
        const departmentNames = this.getSelectedDepartments('category-department-selector');

        if (!name || !copyContent) {
            this.showMessage('名称和复制内容不能为空', 'error');
            return;
        }

        const data = {
            name,
            copy_content: copyContent,
            department_names: departmentNames
        };

        fetch('/api/category-codes', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.closeModal('modal-category-code');
                this.showMessage('分类代码添加成功', 'success');
                this.loadCategoryCodes();
            } else {
                this.showMessage(data.error || '添加失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error adding category code:', error);
            this.showMessage('添加失败', 'error');
        });
    }

    // 更新分类代码
    updateCategoryCode(id) {
        const name = document.getElementById('category-name').value.trim();
        const copyContent = document.getElementById('category-copy-content').value.trim();
        const departmentNames = this.getSelectedDepartments('category-department-selector');

        if (!name || !copyContent) {
            this.showMessage('名称和复制内容不能为空', 'error');
            return;
        }

        const data = {
            name,
            copy_content: copyContent,
            department_names: departmentNames
        };

        fetch(`/api/category-codes/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.closeModal('modal-category-code');
                this.showMessage('分类代码更新成功', 'success');
                this.loadCategoryCodes();
            } else {
                this.showMessage(data.error || '更新失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error updating category code:', error);
            this.showMessage('更新失败', 'error');
        });
    }

    // 删除分类代码
    deleteCategoryCode(id) {
        const code = this.categoryCodes.find(c => c.id === id);
        if (!code) return;

        if (!confirm(`确定要删除分类代码 "${code.name}" 吗？`)) {
            return;
        }

        fetch(`/api/category-codes/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showMessage('分类代码删除成功', 'success');
                this.loadCategoryCodes();
            } else {
                this.showMessage(data.error || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting category code:', error);
            this.showMessage('删除失败', 'error');
        });
    }

    // 获取选中的部门
    getSelectedDepartments(selectorId) {
        const selector = document.getElementById(selectorId);
        if (!selector) return [];
        
        const checkboxes = selector.querySelectorAll('input[type="checkbox"]:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }

    // 加载部门选择器
    loadDepartmentSelector(selectorId, selectedDepartments = []) {
        const selector = document.getElementById(selectorId);
        if (!selector) return;

        // 这里应该从全局的部门数据加载，暂时使用示例数据
        const departments = window.departments || [
            { id: 1, name: 'XLX1' },
            { id: 2, name: 'XLX2' },
            { id: 3, name: 'HGR2' }
        ];

        selector.innerHTML = departments.map(dept => `
            <label class="custom-checkbox">
                <input type="checkbox" value="${dept.name}" ${selectedDepartments.includes(dept.name) ? 'checked' : ''}>
                <span>${dept.name}</span>
            </label>
        `).join('');
    }

    // 创建模态框
    createModal(title, content, modalId, onSave) {
        // 移除已存在的模态框
        const existingModal = document.getElementById(modalId);
        if (existingModal) {
            existingModal.remove();
        }

        const modal = document.createElement('div');
        modal.id = modalId;
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <span class="close" onclick="categoryCenterModule.closeModal('${modalId}')">&times;</span>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary" onclick="categoryCenterModule.closeModal('${modalId}')">取消</button>
                    <button type="button" class="btn-primary" onclick="(${onSave.toString()})()">保存</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.display = 'block';
        return modal;
    }

    // 关闭模态框
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.remove();
        }
    }

    // 显示消息
    showMessage(message, type = 'info') {
        // 使用全局的showMessage函数，如果存在的话
        if (typeof showMessage === 'function') {
            showMessage(message, type);
        } else {
            alert(message);
        }
    }
}

// 创建全局实例
window.categoryCenterModule = new CategoryCenterModule();
