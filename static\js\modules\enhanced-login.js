// 增强登录模块
class EnhancedLoginModule {
    constructor() {
        this.isInitialized = false;
        this.currentCardId = '';
        this.needsPassword = false;
        this.failedAttempts = 0;
        this.maxFailedAttempts = 3;
    }

    // 初始化模块
    init() {
        if (this.isInitialized) return;
        
        this.setupLoginForm();
        this.isInitialized = true;
    }

    // 设置登录表单
    setupLoginForm() {
        const form = document.querySelector('.login-container form');
        if (!form) return;

        const cardInput = form.querySelector('input[name="card_id"]');
        const submitBtn = form.querySelector('button[type="submit"]');

        if (!cardInput || !submitBtn) return;

        // 阻止表单默认提交行为
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            if (this.needsPassword) {
                this.submitLogin();
            } else {
                const cardId = cardInput.value.trim();
                this.checkUser(cardId);
            }
        });

        // Create password input (initially hidden)
        const passwordInput = document.createElement('input');
        passwordInput.type = 'password';
        passwordInput.name = 'password';
        passwordInput.placeholder = this.t('enter_password') || 'Enter Password';
        passwordInput.style.display = 'none';
        passwordInput.style.marginTop = '10px';

        // Insert password input after card input
        cardInput.parentNode.insertBefore(passwordInput, submitBtn);

        // Create user info display
        const userInfo = document.createElement('div');
        userInfo.className = 'user-info';
        userInfo.style.cssText = `
            margin: 10px 0;
            padding: 10px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 6px;
            display: none;
            font-weight: 600;
            color: #667eea;
        `;
        cardInput.parentNode.insertBefore(userInfo, passwordInput);



        // Handle card input changes
        cardInput.addEventListener('input', () => {
            this.resetForm();
        });
    }

    // 检查用户
    checkUser(cardId) {
        if (!cardId) {
            this.showMessage(this.t('enter_card_id') || 'Please enter your card ID', 'error');
            return;
        }

        fetch('/api/check-user', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ card_id: cardId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.exists) {
                this.currentCardId = cardId;
                this.showUserInfo(data.name, data.needs_password);
                
                if (data.needs_password) {
                    this.showPasswordInput();
                } else {
                    this.submitLogin();
                }
            } else {
                this.showMessage(this.t('user_not_found') || 'User not found', 'error');
            }
        })
        .catch(error => {
            console.error('Error checking user:', error);
            this.showMessage(this.t('check_failed') || 'Failed to check user', 'error');
        });
    }

    // 显示用户信息
    showUserInfo(name, needsPassword) {
        const userInfo = document.querySelector('.user-info');
        if (userInfo) {
            userInfo.innerHTML = `
                <strong>${this.t('welcome') || 'Welcome'}, ${name}!</strong>
                ${needsPassword ? 
                    `<br><small>${this.t('password_required') || 'Password required to continue'}</small>` : 
                    `<br><small>${this.t('logging_in') || 'Logging you in...'}</small>`
                }
            `;
            userInfo.style.display = 'block';
        }
    }

    // 显示密码输入框
    showPasswordInput() {
        const passwordInput = document.querySelector('input[name="password"]');
        const submitBtn = document.querySelector('button[type="submit"]');
        
        if (passwordInput && submitBtn) {
            passwordInput.style.display = 'block';
            passwordInput.focus();
            submitBtn.textContent = this.t('login') || 'Login';
            this.needsPassword = true;
        }
    }

    // 提交登录
    submitLogin() {
        const cardIdInput = document.querySelector('input[name="card_id"]');
        const passwordInput = document.querySelector('input[name="password"]');

        const cardId = cardIdInput ? cardIdInput.value : '';
        const password = passwordInput ? passwordInput.value : '';

        // 使用AJAX提交登录
        fetch('/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            body: new URLSearchParams({
                card_id: cardId,
                password: password
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 登录成功，重定向到主页
                console.log('Login successful, redirecting to:', data.redirect);
                window.location.href = data.redirect;
            } else if (data.error) {
                // 登录失败
                console.log('Login error:', data.error, 'Failed attempts:', data.failed_attempts);

                // 统一处理所有密码错误，始终显示内联计数
                if (data.error.includes('Too many failed attempts') || data.error.includes('失败次数过多') ||
                    data.error.includes('密码错误') || data.error.includes('Invalid password')) {

                    // 更新失败次数
                    if (data.failed_attempts) {
                        this.failedAttempts = data.failed_attempts;
                    } else {
                        this.failedAttempts++;
                    }

                    console.log('Password error, count:', this.failedAttempts);

                    // 检查是否达到5次，锁定密码输入框
                    if (this.failedAttempts >= 5) {
                        this.lockPasswordInput();
                        this.showMessage(`Account locked after ${this.failedAttempts} failed attempts. Please contact administrator.`, 'error');
                        // 仍然显示内联计数，即使锁定了
                        const maxAttempts = data.max_attempts || this.maxFailedAttempts;
                        this.showPasswordError(this.failedAttempts, maxAttempts);
                    } else {
                        // 1-4次错误，始终显示内联错误信息
                        const maxAttempts = data.max_attempts || this.maxFailedAttempts;
                        this.showPasswordError(this.failedAttempts, maxAttempts);
                        this.clearPasswordButKeepVisible();

                        // 4次及以上时，额外显示"Too many failed attempts"消息
                        if (this.failedAttempts >= 4) {
                            this.showMessage(data.error, 'error');
                        }
                    }
                } else {
                    // 其他错误
                    this.showMessage(data.error, 'error');
                }
            }
        })
        .catch(error => {
            console.error('Login error:', error);
            this.showMessage(this.t('login_failed') || 'Login failed', 'error');
        });
    }

    // 显示密码错误信息
    showPasswordError(attempts, maxAttempts) {
        console.log('showPasswordError called with attempts:', attempts, 'maxAttempts:', maxAttempts);
        const passwordInput = document.querySelector('input[name="password"]');
        if (!passwordInput) {
            console.log('Password input not found');
            return;
        }

        // 移除现有的错误信息
        const existingError = document.querySelector('.password-error');
        if (existingError) {
            existingError.remove();
        }

        // 创建错误信息容器
        const errorContainer = document.createElement('div');
        errorContainer.className = 'password-error';
        errorContainer.style.cssText = `
            margin-top: 5px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
        `;

        // 错误文本
        const errorText = document.createElement('span');
        errorText.style.cssText = `
            color: #dc3545;
            font-weight: 500;
        `;
        errorText.textContent = `Invalid password ${attempts} time${attempts > 1 ? 's' : ''}`;

        errorContainer.appendChild(errorText);

        // 如果达到3次及以上，显示忘记密码按钮
        if (attempts >= 3) {
            const forgotPasswordButton = document.createElement('button');
            forgotPasswordButton.type = 'button';
            forgotPasswordButton.className = 'forgot-password-btn';
            forgotPasswordButton.textContent = 'Forgot Password';
            forgotPasswordButton.style.cssText = `
                background: none;
                color: #007bff;
                border: none;
                padding: 0;
                cursor: pointer;
                font-size: 14px;
                margin-left: 10px;
                text-decoration: underline;
                font-weight: 500;
                transition: color 0.2s;
                width: 187px;
                text-align: right;
            `;

            // 添加悬停效果
            forgotPasswordButton.addEventListener('mouseenter', () => {
                forgotPasswordButton.style.color = '#0056b3';
                forgotPasswordButton.style.textDecoration = 'underline';
            });
            forgotPasswordButton.addEventListener('mouseleave', () => {
                forgotPasswordButton.style.color = '#007bff';
                forgotPasswordButton.style.textDecoration = 'underline';
            });

            forgotPasswordButton.addEventListener('click', () => {
                this.showForgotPasswordModal();
            });

            errorContainer.appendChild(forgotPasswordButton);
        }

        // 插入到密码输入框后面
        passwordInput.parentNode.insertBefore(errorContainer, passwordInput.nextSibling);
    }

    // 锁定密码输入框
    lockPasswordInput() {
        const passwordInput = document.querySelector('input[name="password"]');
        if (passwordInput) {
            passwordInput.disabled = true;
            passwordInput.placeholder = 'Account locked - Contact administrator';
            passwordInput.style.backgroundColor = '#f8f9fa';
            passwordInput.style.color = '#6c757d';
            passwordInput.style.cursor = 'not-allowed';
        }

        // 禁用提交按钮
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.style.backgroundColor = '#6c757d';
            submitBtn.style.cursor = 'not-allowed';
            submitBtn.textContent = 'Account Locked';
        }
    }

    // 解锁密码输入框
    unlockPasswordInput() {
        const passwordInput = document.querySelector('input[name="password"]');
        if (passwordInput) {
            passwordInput.disabled = false;
            passwordInput.placeholder = 'Enter Password';
            passwordInput.style.backgroundColor = '';
            passwordInput.style.color = '';
            passwordInput.style.cursor = '';
        }

        // 启用提交按钮
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.style.backgroundColor = '';
            submitBtn.style.cursor = '';
            submitBtn.textContent = 'Login';
        }
    }

    // 清除密码但保持输入框显示
    clearPasswordButKeepVisible() {
        const passwordInput = document.querySelector('input[name="password"]');
        if (passwordInput && !passwordInput.disabled) {
            passwordInput.value = '';
            passwordInput.focus(); // 重新聚焦到密码输入框
        }
    }

    // 重置表单
    resetForm() {
        const passwordInput = document.querySelector('input[name="password"]');
        const userInfo = document.querySelector('.user-info');
        const submitBtn = document.querySelector('button[type="submit"]');

        if (passwordInput) {
            passwordInput.style.display = 'none';
            passwordInput.value = '';
        }

        if (userInfo) {
            userInfo.style.display = 'none';
        }

        if (submitBtn) {
            submitBtn.textContent = this.t('continue') || 'Continue';
        }

        // 清除密码错误信息
        this.clearPasswordError();

        // 清除"忘记密码"按钮
        const forgotButtonContainer = document.querySelector('.forgot-password-above-signup');
        if (forgotButtonContainer) {
            forgotButtonContainer.remove();
        }

        // 解锁密码输入框
        this.unlockPasswordInput();

        this.needsPassword = false;
        this.currentCardId = '';
        this.failedAttempts = 0;
    }

    // 显示忘记密码悬浮窗
    showForgotPasswordModal() {
        // 检查是否已经有悬浮窗
        if (document.querySelector('.forgot-password-modal')) return;

        // 创建悬浮窗背景
        const modalOverlay = document.createElement('div');
        modalOverlay.className = 'forgot-password-modal';
        modalOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;

        // 创建悬浮窗内容
        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            max-width: 450px;
            width: 90%;
            position: relative;
        `;

        modalContent.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 style="margin: 0; color: #333; font-size: 18px;">Forgot Password</h3>
                <button class="close-modal" style="
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                    padding: 0;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                ">&times;</button>
            </div>

            <form class="forgot-password-form">
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">Badge ID:</label>
                    <input type="text" name="card_id" value="${this.currentCardId}" readonly style="
                        width: 100%;
                        padding: 10px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        background: #f8f9fa;
                        box-sizing: border-box;
                        color: #666;
                    ">
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">Login:</label>
                    <input type="text" name="employee_code" placeholder="Enter your user code" required style="
                        width: 100%;
                        padding: 10px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        box-sizing: border-box;
                    ">
                    <small style="color: #666; font-size: 12px;">Enter your user code for verification</small>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">Department:</label>
                    <input type="text" name="department_display" readonly style="
                        width: 100%;
                        padding: 10px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        background: #f8f9fa;
                        box-sizing: border-box;
                        color: #666;
                    " value="Loading...">
                    <input type="hidden" name="department_id" value="">
                    <small style="color: #666; font-size: 12px;">Your department (auto-detected)</small>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">New Password:</label>
                    <input type="password" name="new_password" placeholder="Enter your new password" required style="
                        width: 100%;
                        padding: 10px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        box-sizing: border-box;
                    ">
                    <small style="color: #666; font-size: 12px;">Enter your new password (leave empty to clear password)</small>
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">Confirm Password:</label>
                    <input type="password" name="confirm_password" placeholder="Confirm your new password" style="
                        width: 100%;
                        padding: 10px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        box-sizing: border-box;
                    ">
                    <small style="color: #666; font-size: 12px;">Re-enter your new password for confirmation</small>
                </div>

                <div style="margin-bottom: 20px;">
                    <p style="color: #666; font-size: 14px; margin: 0; padding: 10px; background: #f8f9fa; border-radius: 4px; border-left: 4px solid #007bff;">
                        <strong>Note:</strong> Enter your user code to verify identity. You can set a new password or leave it empty to clear the password.
                    </p>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" class="cancel-btn" style="
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 4px;
                        cursor: pointer;
                        transition: background-color 0.2s;
                    ">Cancel</button>
                    <button type="submit" style="
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 4px;
                        cursor: pointer;
                        transition: background-color 0.2s;
                    ">Verify Identity</button>
                </div>
            </form>

            <div class="forgot-message" style="margin-top: 15px; display: none;"></div>
        `;

        modalOverlay.appendChild(modalContent);
        document.body.appendChild(modalOverlay);

        // 绑定事件
        const closeBtn = modalContent.querySelector('.close-modal');
        const cancelBtn = modalContent.querySelector('.cancel-btn');
        const forgotForm = modalContent.querySelector('.forgot-password-form');

        const closeModal = () => {
            document.body.removeChild(modalOverlay);
        };

        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);
        modalOverlay.addEventListener('click', (e) => {
            if (e.target === modalOverlay) closeModal();
        });

        // 添加按钮悬停效果
        cancelBtn.addEventListener('mouseenter', () => {
            cancelBtn.style.backgroundColor = '#5a6268';
        });
        cancelBtn.addEventListener('mouseleave', () => {
            cancelBtn.style.backgroundColor = '#6c757d';
        });

        const submitBtn = modalContent.querySelector('button[type="submit"]');
        submitBtn.addEventListener('mouseenter', () => {
            submitBtn.style.backgroundColor = '#0056b3';
        });
        submitBtn.addEventListener('mouseleave', () => {
            submitBtn.style.backgroundColor = '#007bff';
        });

        forgotForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handlePasswordResetWithDepartment(forgotForm, closeModal);
        });

        // 加载用户部门数据
        this.loadUserDepartment(modalContent);

        // 聚焦到员工代码输入框
        setTimeout(() => {
            const codeInput = modalContent.querySelector('input[name="employee_code"]');
            if (codeInput) codeInput.focus();
        }, 100);
    }

    // 加载用户部门数据
    loadUserDepartment(modalContent) {
        fetch(`/api/user-department/${this.currentCardId}`)
            .then(response => response.json())
            .then(data => {
                const departmentDisplay = modalContent.querySelector('input[name="department_display"]');
                const departmentId = modalContent.querySelector('input[name="department_id"]');

                if (departmentDisplay && departmentId && data.department_id) {
                    departmentDisplay.value = data.department_name;
                    departmentId.value = data.department_id;
                    console.log('User department loaded:', data.department_name);
                } else {
                    departmentDisplay.value = 'Department not found';
                    console.error('User department not found');
                }
            })
            .catch(error => {
                console.error('Error loading user department:', error);
                const departmentDisplay = modalContent.querySelector('input[name="department_display"]');
                if (departmentDisplay) {
                    departmentDisplay.value = 'Failed to load department';
                }
            });
    }

    // 处理密码重置（带部门验证）
    handlePasswordResetWithDepartment(form, closeModal) {
        const formData = new FormData(form);

        // 获取密码字段
        const newPassword = formData.get('new_password');
        const confirmPassword = formData.get('confirm_password');

        // 验证密码匹配
        if (newPassword && newPassword !== confirmPassword) {
            this.showResetMessage('Passwords do not match', 'error', form);
            return;
        }

        const data = {
            card_id: formData.get('card_id'),
            employee_code: formData.get('employee_code'),
            department_id: formData.get('department_id'),
            new_password: newPassword || '' // 如果为空则清除密码
        };

        console.log('Form data being sent:', data);
        console.log('Employee code value:', data.employee_code);
        console.log('Employee code length:', data.employee_code ? data.employee_code.length : 'null/undefined');
        console.log('Card ID:', data.card_id);
        console.log('Department ID:', data.department_id);
        console.log('New password length:', data.new_password ? data.new_password.length : 'empty');

        // 检查表单元素
        const employeeInput = form.querySelector('input[name="employee_code"]');
        const cardInput = form.querySelector('input[name="card_id"]');
        const deptInput = form.querySelector('input[name="department_id"]');

        console.log('Employee input element:', employeeInput);
        console.log('Employee input value:', employeeInput ? employeeInput.value : 'not found');
        console.log('Card input value:', cardInput ? cardInput.value : 'not found');
        console.log('Dept input value:', deptInput ? deptInput.value : 'not found');

        // 显示加载状态
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Verifying...';
        submitBtn.disabled = true;

        fetch('/api/reset-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response ok:', response.ok);
            return response.json();
        })
        .then(result => {
            console.log('Response result:', result);
            if (result.success) {
                closeModal();

                // 如果需要跳转，直接跳转到登录页面
                if (result.redirect) {
                    // 显示成功消息并跳转
                    this.showMessage(result.message || 'Password reset successfully! Redirecting to login...', 'success');

                    // 2秒后跳转到登录页面
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 2000);
                } else {
                    // 原有逻辑：重置状态但不跳转
                    this.showMessage(result.message || 'Password reset successfully!', 'success');
                    this.failedAttempts = 0;
                    this.clearPasswordError();
                    // 解锁并清空密码输入框
                    this.unlockPasswordInput();
                    const passwordInput = document.querySelector('input[name="password"]');
                    if (passwordInput) {
                        passwordInput.value = '';
                        passwordInput.focus();
                    }
                    // 清除"忘记密码"按钮
                    const forgotButtonContainer = document.querySelector('.forgot-password-above-signup');
                    if (forgotButtonContainer) {
                        forgotButtonContainer.remove();
                    }
                }
            } else {
                this.showResetMessage(result.error || 'Reset failed', 'error', form);
            }
        })
        .catch(error => {
            console.error('Password reset error:', error);
            this.showResetMessage('Reset failed, please try again later', 'error', form);
        })
        .finally(() => {
            // 恢复按钮状态
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        });
    }

    // 显示重置密码的消息
    showResetMessage(message, type, form) {
        const messageDiv = form.parentElement.querySelector('.forgot-message');
        if (messageDiv) {
            messageDiv.style.display = 'block';
            messageDiv.style.padding = '10px';
            messageDiv.style.borderRadius = '4px';
            messageDiv.style.fontSize = '14px';
            messageDiv.textContent = message;

            if (type === 'error') {
                messageDiv.style.backgroundColor = '#f8d7da';
                messageDiv.style.color = '#721c24';
                messageDiv.style.border = '1px solid #f5c6cb';
            } else {
                messageDiv.style.backgroundColor = '#d4edda';
                messageDiv.style.color = '#155724';
                messageDiv.style.border = '1px solid #c3e6cb';
            }
        }
    }

    // 在sign up按钮上方显示"忘记密码"按钮
    showForgotPasswordButtonAboveSignup() {
        // 检查是否已经显示了按钮
        if (document.querySelector('.forgot-password-above-signup')) return;

        // 查找sign up按钮或注册相关元素
        const loginContainer = document.querySelector('.login-container');
        if (!loginContainer) return;

        // 创建"忘记密码"按钮容器
        const forgotContainer = document.createElement('div');
        forgotContainer.className = 'forgot-password-above-signup';
        forgotContainer.style.cssText = `
            margin: 15px 0;
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        `;

        // 创建"忘记密码"按钮
        const forgotButton = document.createElement('button');
        forgotButton.type = 'button';
        forgotButton.className = 'forgot-password-main-btn';
        forgotButton.textContent = 'Forgot Password';
        forgotButton.style.cssText = `
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        `;

        // 添加悬停效果
        forgotButton.addEventListener('mouseenter', () => {
            forgotButton.style.backgroundColor = '#0056b3';
        });
        forgotButton.addEventListener('mouseleave', () => {
            forgotButton.style.backgroundColor = '#007bff';
        });

        // 点击事件
        forgotButton.addEventListener('click', () => {
            this.showForgotPasswordModal();
        });

        forgotContainer.appendChild(forgotButton);

        // 插入到登录容器的末尾（sign up按钮上方的位置）
        loginContainer.appendChild(forgotContainer);
    }

    // 清除密码错误信息
    clearPasswordError() {
        const errorElement = document.querySelector('.password-error');
        if (errorElement) {
            errorElement.remove();
        }
    }

    // 显示密码重置选项（旧方法，保留兼容性）
    showPasswordResetOption() {
        // 检查是否已经有悬浮窗
        if (document.querySelector('.forgot-password-modal')) return;

        // 创建悬浮窗背景
        const modalOverlay = document.createElement('div');
        modalOverlay.className = 'forgot-password-modal';
        modalOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;

        // 创建悬浮窗内容
        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 90%;
            position: relative;
        `;

        modalContent.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 style="margin: 0; color: #333;">Reset Password</h3>
                <button class="close-modal" style="
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                    padding: 0;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                ">&times;</button>
            </div>

            <form class="reset-form">
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Card ID:</label>
                    <input type="text" name="card_id" value="${this.currentCardId}" readonly style="
                        width: 100%;
                        padding: 10px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        background: #f8f9fa;
                        box-sizing: border-box;
                    ">
                </div>

                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">Employee Code:</label>
                    <input type="text" name="employee_code" placeholder="Enter your employee code" required style="
                        width: 100%;
                        padding: 10px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        box-sizing: border-box;
                    ">
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: 500;">New Password:</label>
                    <input type="password" name="new_password" placeholder="Enter new password (leave empty to remove)" style="
                        width: 100%;
                        padding: 10px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        box-sizing: border-box;
                    ">
                    <small style="color: #666; font-size: 12px;">Leave empty to remove password</small>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" class="cancel-btn" style="
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 4px;
                        cursor: pointer;
                    ">Cancel</button>
                    <button type="submit" style="
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 4px;
                        cursor: pointer;
                    ">Reset Password</button>
                </div>
            </form>

            <div class="reset-message" style="margin-top: 15px; display: none;"></div>
        `;

        modalOverlay.appendChild(modalContent);
        document.body.appendChild(modalOverlay);

        // 绑定事件
        const closeBtn = modalContent.querySelector('.close-modal');
        const cancelBtn = modalContent.querySelector('.cancel-btn');
        const resetForm = modalContent.querySelector('.reset-form');

        const closeModal = () => {
            document.body.removeChild(modalOverlay);
        };

        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);
        modalOverlay.addEventListener('click', (e) => {
            if (e.target === modalOverlay) closeModal();
        });

        resetForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handlePasswordResetSimple(resetForm, closeModal);
        });

        // 聚焦到员工代码输入框
        setTimeout(() => {
            const codeInput = modalContent.querySelector('input[name="employee_code"]');
            if (codeInput) codeInput.focus();
        }, 100);
    }

    // 处理密码修改
    handlePasswordChange(form, closeModal) {
        const formData = new FormData(form);
        const data = {
            card_id: formData.get('card_id'),
            employee_code: formData.get('employee_code'),
            new_password: formData.get('new_password')
        };

        // 显示加载状态
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '修改中...';
        submitBtn.disabled = true;

        fetch('/api/change-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                closeModal();
                this.showMessage(result.message || '密码修改成功！', 'success');
                this.failedAttempts = 0;
                this.clearPasswordError();
                // 清空密码输入框
                const passwordInput = document.querySelector('input[name="password"]');
                if (passwordInput) {
                    passwordInput.value = '';
                    passwordInput.focus();
                }
            } else {
                this.showChangeMessage(result.error || '修改失败', 'error', form);
            }
        })
        .catch(error => {
            console.error('Password change error:', error);
            this.showChangeMessage('修改失败，请稍后重试', 'error', form);
        })
        .finally(() => {
            // 恢复按钮状态
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        });
    }

    // 显示修改密码的消息
    showChangeMessage(message, type, form) {
        const messageDiv = form.parentElement.querySelector('.change-message');
        if (messageDiv) {
            messageDiv.style.display = 'block';
            messageDiv.style.padding = '10px';
            messageDiv.style.borderRadius = '4px';
            messageDiv.style.fontSize = '14px';
            messageDiv.textContent = message;

            if (type === 'error') {
                messageDiv.style.backgroundColor = '#f8d7da';
                messageDiv.style.color = '#721c24';
                messageDiv.style.border = '1px solid #f5c6cb';
            } else {
                messageDiv.style.backgroundColor = '#d4edda';
                messageDiv.style.color = '#155724';
                messageDiv.style.border = '1px solid #c3e6cb';
            }
        }
    }

    // 处理密码重置（简单版本）
    handlePasswordResetSimple(form, closeModal) {
        const formData = new FormData(form);
        const data = {
            card_id: formData.get('card_id'),
            employee_code: formData.get('employee_code'),
            department_id: formData.get('department_id')
        };

        console.log('Password reset data being sent:', data);
        console.log('Card ID:', data.card_id);
        console.log('Employee code:', data.employee_code);
        console.log('Department ID:', data.department_id);

        // 显示加载状态
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Verifying...';
        submitBtn.disabled = true;

        fetch('/api/reset-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            // 恢复按钮状态
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;

            if (result.success) {
                closeModal();
                this.showMessage(result.message || 'Password reset successfully!', 'success');
                this.failedAttempts = 0;
                this.clearPasswordError();
                // 清空密码输入框
                const passwordInput = document.querySelector('input[name="password"]');
                if (passwordInput) {
                    passwordInput.value = '';
                    passwordInput.focus();
                }
            } else {
                this.showResetMessage(result.error || 'Reset failed', 'error', form);
            }
        })
        .catch(error => {
            console.error('Password reset error:', error);
            // 恢复按钮状态
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
            this.showResetMessage('Reset failed', 'error', form);
        });
    }

    // 清除密码错误信息
    clearPasswordError() {
        const errorElement = document.querySelector('.password-error');
        if (errorElement) {
            errorElement.remove();
        }
    }

    // 显示密码重置选项
    showPasswordResetOption() {
        const form = document.querySelector('.login-container form');
        if (!form) return;

        // 检查是否已经显示了重置选项
        if (document.querySelector('.password-reset-options')) return;

        const resetOptions = document.createElement('div');
        resetOptions.className = 'password-reset-options';
        resetOptions.style.cssText = `
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #dc3545;
        `;

        resetOptions.innerHTML = `
            <p style="margin: 0 0 10px 0; color: #dc3545; font-weight: bold;">
                ${this.t('password_error_limit_reached') || '密码错误次数已达到3次，您可以选择修改密码：'}
            </p>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button type="button" class="btn-reset-password" style="
                    background: #007bff;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                ">
                    ${this.t('change_password') || '修改密码'}
                </button>
                <button type="button" class="btn-contact-admin" style="
                    background: #6c757d;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                ">
                    ${this.t('contact_admin') || '联系管理员'}
                </button>
            </div>
        `;

        form.appendChild(resetOptions);

        // 添加重置密码按钮事件
        const resetBtn = resetOptions.querySelector('.btn-reset-password');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.showPasswordResetModal();
            });
        }

        // 添加联系管理员按钮事件
        const contactBtn = resetOptions.querySelector('.btn-contact-admin');
        if (contactBtn) {
            contactBtn.addEventListener('click', () => {
                this.showContactAdminInfo();
            });
        }
    }

    // 显示密码重置模态框
    showPasswordResetModal() {
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'password-reset-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;

        modal.innerHTML = `
            <div style="
                background: white;
                padding: 30px;
                border-radius: 8px;
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
            ">
                <h3 style="margin: 0 0 20px 0; color: #333;">
                    ${this.t('reset_password') || '重置密码'}
                </h3>
                <p style="margin: 0 0 20px 0; color: #666;">
                    ${this.t('reset_password_instruction') || '请填写以下信息进行身份验证，验证成功后密码将被清除。'}
                </p>
                <form class="password-reset-form">
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                            ${this.t('badge') || 'Badge ID'}:
                        </label>
                        <input type="text" name="badge" required style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                        " placeholder="Enter your badge/card ID">
                        <small style="color: #666; font-size: 12px;">Enter your employee badge/card ID</small>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                            ${this.t('login_code') || 'Login Code'}:
                        </label>
                        <input type="text" name="login_code" required style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                        " placeholder="Enter your user code">
                        <small style="color: #666; font-size: 12px;">Enter your user code for verification</small>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <p style="color: #666; font-size: 14px; margin: 0; padding: 10px; background: #f8f9fa; border-radius: 4px; border-left: 4px solid #007bff;">
                            <strong>Note:</strong> Only Badge ID and Login Code are required for verification. Your password will be cleared after successful verification.
                        </p>
                    </div>
                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button type="button" class="btn-cancel" style="
                            background: #6c757d;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 4px;
                            cursor: pointer;
                        ">
                            ${this.t('cancel') || '取消'}
                        </button>
                        <button type="submit" style="
                            background: #dc3545;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 4px;
                            cursor: pointer;
                        ">
                            ${this.t('reset_password') || '重置密码'}
                        </button>
                    </div>
                </form>
            </div>
        `;

        document.body.appendChild(modal);

        // 添加事件监听器
        const form = modal.querySelector('.password-reset-form');
        const cancelBtn = modal.querySelector('.btn-cancel');

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handlePasswordResetBadge(form, modal);
        });

        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        // 点击模态框外部关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    // 处理密码重置（Badge版本）
    handlePasswordResetBadge(form, modal) {
        const formData = new FormData(form);
        const data = {
            card_id: formData.get('badge'),
            employee_code: formData.get('login_code')
        };

        console.log('Password reset data being sent:', data);
        console.log('Card ID:', data.card_id);
        console.log('Employee code:', data.employee_code);

        // 显示加载状态
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Verifying...';
        submitBtn.disabled = true;

        fetch('/api/reset-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            // 恢复按钮状态
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;

            if (result.success) {
                document.body.removeChild(modal);
                this.showMessage(result.message || this.t('password_reset_success') || '密码已经清除，请直接登入！', 'success');
                this.failedAttempts = 0;
                this.hidePasswordResetOption();
                // 清空密码输入框并聚焦
                const passwordInput = document.querySelector('input[name="password"]');
                if (passwordInput) {
                    passwordInput.value = '';
                    passwordInput.focus();
                }
            } else {
                this.showResetMessage(result.error || this.t('reset_failed') || '重置失败，请检查信息是否正确', 'error', form);
            }
        })
        .catch(error => {
            console.error('Password reset error:', error);
            // 恢复按钮状态
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
            this.showResetMessage(this.t('reset_failed') || '重置失败', 'error', form);
        });
    }

    // 显示重置消息
    showResetMessage(message, type, form) {
        let messageEl = form.querySelector('.reset-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.className = 'reset-message';
            messageEl.style.cssText = `
                margin: 10px 0;
                padding: 10px;
                border-radius: 4px;
                text-align: center;
            `;
            form.appendChild(messageEl);
        }

        messageEl.textContent = message;
        messageEl.style.background = type === 'error' ? '#f8d7da' : '#d4edda';
        messageEl.style.color = type === 'error' ? '#721c24' : '#155724';
        messageEl.style.border = type === 'error' ? '1px solid #f5c6cb' : '1px solid #c3e6cb';
    }

    // 隐藏密码重置选项
    hidePasswordResetOption() {
        const resetOptions = document.querySelector('.password-reset-options');
        if (resetOptions) {
            resetOptions.remove();
        }
    }

    // 显示联系管理员信息
    showContactAdminInfo() {
        alert(this.t('contact_admin_info') || '请联系系统管理员重置您的密码。');
    }

    // Translation helper
    t(key) {
        return window.i18n ? window.i18n.t(key) : null;
    }

    // Show message
    showMessage(message, type = 'info') {
        // Create or update message element
        let messageEl = document.querySelector('.login-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.className = 'login-message';
            messageEl.style.cssText = `
                margin: 10px 0;
                padding: 10px;
                border-radius: 4px;
                text-align: center;
            `;
            
            const form = document.querySelector('.login-container form');
            if (form) {
                form.appendChild(messageEl);
            }
        }
        
        messageEl.textContent = message;
        messageEl.className = `login-message ${type}`;
        
        // Style based on type
        if (type === 'error') {
            messageEl.style.background = '#f8d7da';
            messageEl.style.color = '#721c24';
            messageEl.style.border = '1px solid #f5c6cb';
        } else {
            messageEl.style.background = '#d4edda';
            messageEl.style.color = '#155724';
            messageEl.style.border = '1px solid #c3e6cb';
        }
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 5000);
    }

    // Destroy module
    destroy() {
        this.isInitialized = false;
    }
}

// Export module
window.EnhancedLoginModule = EnhancedLoginModule;
