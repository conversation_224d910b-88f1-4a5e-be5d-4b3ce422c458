/**
 * 反馈模块
 * 处理用户反馈的提交和显示
 */
class FeedbackModule {
    constructor() {
        this.init();
    }

    init() {
        console.log('初始化反馈模块...');
        this.setupFeedbackButton();
    }

    // 设置反馈按钮
    setupFeedbackButton() {
        const feedbackBtn = document.querySelector('.btn-feedback');
        if (feedbackBtn) {
            feedbackBtn.addEventListener('click', () => this.showFeedbackForm());
            console.log('反馈按钮已设置');
        } else {
            console.log('未找到反馈按钮');
        }
    }

    // 显示反馈表单
    showFeedbackForm() {
        console.log('显示反馈表单...');
        const modal = document.createElement('div');
        modal.className = 'feedback-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;
        
        modal.innerHTML = `
            <div class="feedback-modal-content" style="
                background: white;
                padding: 30px;
                border-radius: 15px;
                width: 500px;
                max-width: 90vw;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            ">
                <div class="feedback-modal-header" style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                ">
                    <h3 style="margin: 0; color: #333;">📝 提交反馈</h3>
                    <button onclick="this.closest('.feedback-modal').remove()" style="
                        background: none;
                        border: none;
                        font-size: 24px;
                        cursor: pointer;
                        color: #666;
                    ">&times;</button>
                </div>
                <form id="feedbackForm" style="display: flex; flex-direction: column; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">标题：</label>
                        <input type="text" id="feedbackTitle" placeholder="请输入反馈标题..." required style="
                            width: 100%;
                            padding: 12px;
                            border: 2px solid #e1e5e9;
                            border-radius: 6px;
                            font-family: inherit;
                            box-sizing: border-box;
                        ">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">内容：</label>
                        <textarea id="feedbackContent" placeholder="请详细描述您的问题或建议..." required style="
                            width: 100%;
                            padding: 12px;
                            border: 2px solid #e1e5e9;
                            border-radius: 6px;
                            font-family: inherit;
                            resize: vertical;
                            min-height: 120px;
                            box-sizing: border-box;
                        "></textarea>
                    </div>
                    <div class="feedback-actions" style="
                        display: flex;
                        gap: 10px;
                        justify-content: flex-end;
                        margin-top: 20px;
                    ">
                        <button type="button" onclick="this.closest('.feedback-modal').remove()" style="
                            padding: 10px 20px;
                            border: 1px solid #ddd;
                            background: #f8f9fa;
                            border-radius: 6px;
                            cursor: pointer;
                        ">取消</button>
                        <button type="submit" style="
                            padding: 10px 20px;
                            background: linear-gradient(45deg, #667eea, #764ba2);
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: 600;
                        ">提交反馈</button>
                    </div>
                </form>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 设置表单提交事件
        const form = modal.querySelector('#feedbackForm');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitFeedback(modal);
        });
        
        // 点击外部关闭模态框
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        console.log('反馈表单已创建');
    }

    // 提交反馈
    submitFeedback(modal) {
        const title = document.getElementById('feedbackTitle').value.trim();
        const content = document.getElementById('feedbackContent').value.trim();
        
        if (!title || !content) {
            if (window.showMessage) {
                showMessage('请填写完整的反馈信息', 'error');
            } else {
                alert('请填写完整的反馈信息');
            }
            return;
        }
        
        console.log('提交反馈:', { title, content });
        
        fetch('/api/feedback', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                title: title,
                content: content
            })
        })
        .then(response => {
            console.log('反馈API响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('反馈提交响应:', data);
            if (data.success) {
                if (window.showMessage) {
                    showMessage('反馈提交成功，感谢您的反馈！', 'success');
                } else {
                    alert('反馈提交成功，感谢您的反馈！');
                }
                modal.remove();
            } else {
                if (window.showMessage) {
                    showMessage(data.error || '反馈提交失败', 'error');
                } else {
                    alert(data.error || '反馈提交失败');
                }
            }
        })
        .catch(error => {
            console.error('反馈提交失败:', error);
            if (window.showMessage) {
                showMessage('反馈提交失败，请重试', 'error');
            } else {
                alert('反馈提交失败，请重试');
            }
        });
    }

    // 检查反馈权限
    checkFeedbackPermission() {
        // 这里可以添加权限检查逻辑
        return true;
    }

    // 销毁模块
    destroy() {
        console.log('销毁反馈模块...');
        // 清理事件监听器等资源
    }
}

// 导出模块
window.FeedbackModule = FeedbackModule; 