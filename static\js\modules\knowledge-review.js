/**
 * Knowledge Review Module
 * Allows admins to review and approve/reject knowledge submissions
 */
class KnowledgeReviewModule {
    constructor() {
        this.isInitialized = false;
        this.init();
    }

    init() {
        if (this.isInitialized) return;
        
        this.createReviewButton();
        this.isInitialized = true;
    }

    // Create the knowledge review button in header
    createReviewButton() {
        const headerRight = document.querySelector('.header-right');
        if (!headerRight) return;

        // Check if user has review permission
        const userPermissions = window.userPermissions || {};
        if (!userPermissions.can_review_knowledge_submissions) return;

        // Create review button
        const reviewBtn = document.createElement('a');
        reviewBtn.href = '#';
        reviewBtn.className = 'btn-knowledge-review';
        reviewBtn.textContent = this.t('knowledge_review') || 'Knowledge Review';
        reviewBtn.style.cssText = `
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 600;
            margin-right: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        `;

        reviewBtn.addEventListener('mouseenter', () => {
            reviewBtn.style.transform = 'translateY(-2px)';
            reviewBtn.style.boxShadow = '0 4px 12px rgba(255, 107, 107, 0.4)';
        });

        reviewBtn.addEventListener('mouseleave', () => {
            reviewBtn.style.transform = 'translateY(0)';
            reviewBtn.style.boxShadow = '0 2px 8px rgba(255, 107, 107, 0.3)';
        });

        reviewBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.showReviewInterface();
        });

        // Insert after Problem Solve button
        const problemSolveBtn = headerRight.querySelector('.btn-problem-solve');
        if (problemSolveBtn) {
            problemSolveBtn.parentNode.insertBefore(reviewBtn, problemSolveBtn.nextSibling);
        } else {
            // Insert before admin button if no problem solve button
            const adminBtn = headerRight.querySelector('.btn-admin');
            if (adminBtn) {
                headerRight.insertBefore(reviewBtn, adminBtn);
            } else {
                headerRight.insertBefore(reviewBtn, headerRight.firstChild);
            }
        }

        // Add notification badge
        this.createNotificationBadge(reviewBtn);
        this.checkPendingSubmissions();
    }

    // Create notification badge
    createNotificationBadge(button) {
        const badge = document.createElement('span');
        badge.id = 'knowledgeReviewBadge';
        badge.style.cssText = `
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff3b30;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 12px;
            font-weight: bold;
            min-width: 18px;
            text-align: center;
            display: none;
        `;
        
        button.style.position = 'relative';
        button.appendChild(badge);
    }

    // Check pending submissions
    checkPendingSubmissions() {
        fetch('/api/knowledge-submissions/pending-count')
            .then(response => response.json())
            .then(data => {
                const badge = document.getElementById('knowledgeReviewBadge');
                if (badge && data.count > 0) {
                    badge.textContent = data.count;
                    badge.style.display = 'inline-block';
                } else if (badge) {
                    badge.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error checking pending submissions:', error);
            });
    }

    // Show review interface
    showReviewInterface() {
        const modal = document.createElement('div');
        modal.className = 'knowledge-review-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;

        const modalContent = document.createElement('div');
        modalContent.className = 'knowledge-review-modal-content';
        modalContent.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 15px;
            width: 800px;
            max-width: 90vw;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        `;

        modalContent.innerHTML = `
            <div class="knowledge-review-header" style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #f0f0f0;
                padding-bottom: 15px;
            ">
                <h3 style="margin: 0; color: #333; font-size: 1.5em;">
                    🔍 ${this.t('knowledge_review') || 'Knowledge Review'}
                </h3>
                <button class="close-btn" style="
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                ">&times;</button>
            </div>
            
            <div class="submissions-list" id="submissionsList">
                <div style="text-align: center; padding: 20px; color: #666;">
                    ${this.t('loading') || 'Loading...'}
                </div>
            </div>
        `;

        modal.appendChild(modalContent);
        document.body.appendChild(modal);

        // Event listeners
        const closeBtn = modalContent.querySelector('.close-btn');
        closeBtn.addEventListener('click', () => modal.remove());
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) modal.remove();
        });

        // Load submissions
        this.loadSubmissions();
    }

    // Load pending submissions
    loadSubmissions() {
        const submissionsList = document.getElementById('submissionsList');
        
        fetch('/api/knowledge-submissions')
            .then(response => response.json())
            .then(data => {
                if (data.length === 0) {
                    submissionsList.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #666; font-style: italic;">
                            ${this.t('no_pending_submissions') || 'No pending submissions'}
                        </div>
                    `;
                    return;
                }

                submissionsList.innerHTML = data.map(submission => `
                    <div class="submission-item" style="
                        border: 1px solid #e1e5e9;
                        border-radius: 8px;
                        padding: 20px;
                        margin-bottom: 15px;
                        background: #f8f9fa;
                    ">
                        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px;">
                            <div>
                                <strong style="color: #333; font-size: 1.1em;">${this.t('submitted_by') || 'Submitted by'}: ${submission.user_name}</strong>
                                <div style="color: #666; font-size: 0.9em; margin-top: 5px;">
                                    ${new Date(submission.created_at).toLocaleString()}
                                </div>
                            </div>
                            <span style="
                                background: #ffc107;
                                color: #856404;
                                padding: 4px 8px;
                                border-radius: 4px;
                                font-size: 0.8em;
                                font-weight: 600;
                            ">${this.t('pending') || 'Pending'}</span>
                        </div>
                        
                        <div style="margin-bottom: 15px;">
                            <div style="font-weight: 600; color: #333; margin-bottom: 8px;">
                                ${this.t('question') || 'Question'}:
                            </div>
                            <div style="
                                background: white;
                                padding: 12px;
                                border-radius: 6px;
                                border-left: 4px solid #667eea;
                            ">${submission.question}</div>
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <div style="font-weight: 600; color: #333; margin-bottom: 8px;">
                                ${this.t('answer') || 'Answer'}:
                            </div>
                            <div style="
                                background: white;
                                padding: 12px;
                                border-radius: 6px;
                                border-left: 4px solid #28a745;
                            ">${submission.answer}</div>
                        </div>
                        
                        <div style="display: flex; gap: 10px; justify-content: flex-end;">
                            <button class="reject-btn" onclick="knowledgeReviewModule.reviewSubmission(${submission.id}, 'rejected')" style="
                                background: #dc3545;
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 4px;
                                cursor: pointer;
                                font-weight: 600;
                            ">${this.t('reject') || 'Reject'}</button>
                            <button class="approve-btn" onclick="knowledgeReviewModule.reviewSubmission(${submission.id}, 'approved')" style="
                                background: #28a745;
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 4px;
                                cursor: pointer;
                                font-weight: 600;
                            ">${this.t('approve') || 'Approve'}</button>
                        </div>
                    </div>
                `).join('');
            })
            .catch(error => {
                console.error('Error loading submissions:', error);
                submissionsList.innerHTML = `
                    <div style="text-align: center; padding: 20px; color: #dc3545;">
                        ${this.t('load_failed') || 'Failed to load submissions'}
                    </div>
                `;
            });
    }

    // Review submission (approve/reject)
    reviewSubmission(submissionId, action) {
        fetch(`/api/knowledge-submissions/${submissionId}/review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ action: action })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const message = action === 'approved' 
                    ? (this.t('submission_approved') || 'Submission approved and added to knowledge base!')
                    : (this.t('submission_rejected') || 'Submission rejected.');
                this.showMessage(message, 'success');
                this.loadSubmissions(); // Reload the list
                this.checkPendingSubmissions(); // Update badge
            } else {
                this.showMessage(data.error || (this.t('review_failed') || 'Review failed'), 'error');
            }
        })
        .catch(error => {
            console.error('Review error:', error);
            this.showMessage(this.t('review_failed') || 'Review failed', 'error');
        });
    }

    // Translation helper
    t(key) {
        return window.i18n ? window.i18n.t(key) : null;
    }

    // Show message
    showMessage(message, type = 'info') {
        if (window.showMessage) {
            window.showMessage(message, type);
        } else {
            alert(message);
        }
    }

    // Destroy module
    destroy() {
        const reviewBtn = document.querySelector('.btn-knowledge-review');
        if (reviewBtn) {
            reviewBtn.remove();
        }
        this.isInitialized = false;
    }
}

// Export module
window.KnowledgeReviewModule = KnowledgeReviewModule;
