// 搜索模块
class SearchModule {
    constructor() {
        this.searchTimeout = null;
        this.selectedIndex = -1;
        this.searchResults = [];
        this.searchInput = null;
        this.resultsContainer = null;
        this.isInitialized = false;
    }

    // 初始化搜索功能
    init() {
        if (this.isInitialized) return;
        
        this.searchInput = document.getElementById('searchInput');
        this.resultsContainer = document.getElementById('searchResults');
        
        if (!this.searchInput) {
            console.warn('搜索输入框未找到');
            return;
        }

        // 检查搜索权限
        if (!this.checkSearchPermission()) {
            this.disableSearch();
            return;
        }

        this.setupEventListeners();
        this.isInitialized = true;
    }

    // 检查搜索权限
    checkSearchPermission() {
        return this.checkFrontPermission('can_search_labels');
    }

    // 权限检查函数
    checkFrontPermission(permissionName) {
        if (window.userPermissions) {
            return !!window.userPermissions[permissionName];
        }
        return false;
    }

    // 禁用搜索功能
    disableSearch() {
        this.searchInput.disabled = true;
        this.searchInput.placeholder = '无标签搜索权限';
        
        if (this.resultsContainer) {
            this.resultsContainer.innerHTML = '<div class="no-results">无标签搜索权限</div>';
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        // 输入事件
        this.searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            
            // 清除之前的超时
            clearTimeout(this.searchTimeout);
            
            // 如果查询为空，隐藏结果
            if (query === '') {
                this.hideSearchResults();
                return;
            }
            
            // 设置超时以避免过多请求
            this.searchTimeout = setTimeout(() => {
                this.performSearch(query);
            }, 300);
        });

        // 键盘事件
        this.searchInput.addEventListener('keydown', (e) => {
            const items = this.resultsContainer.querySelectorAll('.search-result-item');
            
            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    this.selectedIndex = Math.min(this.selectedIndex + 1, items.length - 1);
                    this.updateSelection(items);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
                    this.updateSelection(items);
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (this.selectedIndex >= 0 && this.searchResults[this.selectedIndex]) {
                        this.selectSearchResult(this.searchResults[this.selectedIndex]);
                    } else if (this.searchResults.length > 0) {
                        // 如果没有选中项但有搜索结果，选择第一个
                        this.selectSearchResult(this.searchResults[0]);
                    }
                    break;
                case 'Escape':
                    this.hideSearchResults();
                    break;
            }
        });

        // 点击外部关闭搜索结果
        document.addEventListener('click', (e) => {
            const searchContainer = document.querySelector('.search-container');
            if (!searchContainer.contains(e.target)) {
                this.hideSearchResults();
            }
        });
    }

    // 执行搜索
    performSearch(query) {
        fetch(`/api/quick-search?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                this.searchResults = data;
                this.displaySearchResults(data);
            })
            .catch(error => {
                console.error('搜索错误:', error);
                this.displaySearchResults([]);
            });
    }

    // 显示搜索结果
    displaySearchResults(results) {
        this.selectedIndex = -1;
        
        if (results.length === 0) {
            this.resultsContainer.innerHTML = '<div class="no-results">没有找到匹配的标签</div>';
        } else {
            this.resultsContainer.innerHTML = results.map((label, index) => `
                <div class="search-result-item" data-index="${index}" data-label='${JSON.stringify(label).replace(/'/g, "&#39;")}'>
                    <div class="search-result-icon">${this.getLabelTypeIcon(label.type)}</div>
                    <div class="search-result-content">
                        <div class="search-result-name">${this.highlightQuery(label.name, this.searchInput.value)}</div>
                        <div class="search-result-type">${this.getLabelTypeName(label.type)}</div>
                    </div>
                </div>
            `).join('');
            
            // 为搜索结果项添加点击事件监听器
            this.resultsContainer.querySelectorAll('.search-result-item').forEach((item, index) => {
                item.addEventListener('click', () => {
                    this.selectSearchResult(results[index]);
                });
            });
        }
        
        this.resultsContainer.classList.add('show');
    }

    // 更新选择状态
    updateSelection(items) {
        items.forEach((item, index) => {
            item.classList.toggle('selected', index === this.selectedIndex);
        });
    }

    // 选择搜索结果
    selectSearchResult(label) {
        // 隐藏搜索结果
        this.hideSearchResults();
        
        // 清空搜索输入
        this.searchInput.value = '';
        
        // 处理标签点击（使用main.js中的handleLabelClick函数）
        if (typeof handleLabelClick === 'function') {
            handleLabelClick(label);
        } else {
            console.error('handleLabelClick函数未找到');
        }
    }

    // 隐藏搜索结果
    hideSearchResults() {
        this.resultsContainer.classList.remove('show');
        this.selectedIndex = -1;
    }

    // 高亮查询文本
    highlightQuery(text, query) {
        if (!query) return text;
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<strong>$1</strong>');
    }

    // 获取标签类型图标
    getLabelTypeIcon(type) {
        const icons = {
            'normal': '🏷️',
            'advanced': '⚙️',
            'special': '⭐',
            'global': '🌍'
        };
        return icons[type] || '🏷️';
    }

    // 获取标签类型名称
    getLabelTypeName(type) {
        const names = {
            'normal': '普通标签',
            'advanced': '高级标签',
            'special': '特殊标签',
            'global': '全局标签'
        };
        return names[type] || '未知类型';
    }
}

// 导出搜索模块
window.SearchModule = SearchModule; 