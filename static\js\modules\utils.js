// 工具函数模块
class UtilsModule {
    constructor() {
        this.isInitialized = false;
    }

    // 初始化工具模块
    init() {
        if (this.isInitialized) return;
        
        // 将工具函数暴露到全局作用域
        this.exposeToGlobal();
        
        // 添加CSS动画样式
        this.addStyles();
        
        this.isInitialized = true;
    }

    // 将工具函数暴露到全局作用域
    exposeToGlobal() {
        window.showMessage = this.showMessage.bind(this);
        window.checkFrontPermission = this.checkFrontPermission.bind(this);
        window.copyToClipboard = this.copyToClipboard.bind(this);
        window.fallbackCopyToClipboard = this.fallbackCopyToClipboard.bind(this);
    }

    // 显示消息
    showMessage(message, type = 'info') {
        // 创建简单的消息显示
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            animation: slideIn 0.3s ease;
        `;
        
        switch(type) {
            case 'success':
                messageDiv.style.background = '#28a745';
                break;
            case 'error':
                messageDiv.style.background = '#dc3545';
                break;
            default:
                messageDiv.style.background = '#17a2b8';
        }
        
        messageDiv.textContent = message;
        document.body.appendChild(messageDiv);
        
        // 3秒后移除
        setTimeout(() => {
            messageDiv.remove();
        }, 3000);
    }

    // 权限检查函数
    checkFrontPermission(permissionName) {
        if (window.userPermissions) {
            return !!window.userPermissions[permissionName];
        }
        return false;
    }

    // 复制到剪贴板
    copyToClipboard(text) {
        if (navigator.clipboard && window.isSecureContext) {
            // 使用现代 Clipboard API
            navigator.clipboard.writeText(text).then(() => {
                this.showMessage(`已复制: ${text}`, 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                this.fallbackCopyToClipboard(text);
            });
        } else {
            // 降级方案
            this.fallbackCopyToClipboard(text);
        }
    }

    // 降级复制方案
    fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            this.showMessage(`已复制: ${text}`, 'success');
        } catch (err) {
            console.error('复制失败:', err);
            this.showMessage('复制失败，请手动复制', 'error');
        }
        
        document.body.removeChild(textArea);
    }

    // 添加CSS样式
    addStyles() {
        // 检查是否已经添加过样式
        if (document.getElementById('utils-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'utils-styles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            .search-result {
                padding: 8px;
                border-bottom: 1px solid #eee;
                cursor: pointer;
                transition: background-color 0.2s;
            }
            .search-result:hover {
                background-color: #f8f9fa;
            }
            .search-result small {
                color: #666;
                margin-left: 8px;
            }
            .search-result code {
                background-color: #e9ecef;
                padding: 2px 4px;
                border-radius: 3px;
                font-size: 0.9em;
                margin-left: 8px;
            }
        `;
        document.head.appendChild(style);
    }

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }

    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // 安全的JSON解析
    safeJsonParse(str, defaultValue = null) {
        try {
            return JSON.parse(str);
        } catch (e) {
            console.warn('JSON解析失败:', e);
            return defaultValue;
        }
    }

    // 检查元素是否存在
    elementExists(selector) {
        return document.querySelector(selector) !== null;
    }

    // 等待元素出现
    waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            if (this.elementExists(selector)) {
                resolve(document.querySelector(selector));
                return;
            }

            const observer = new MutationObserver(() => {
                if (this.elementExists(selector)) {
                    observer.disconnect();
                    resolve(document.querySelector(selector));
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`等待元素 ${selector} 超时`));
            }, timeout);
        });
    }

    // 获取URL参数
    getUrlParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }

    // 设置URL参数
    setUrlParam(name, value) {
        const url = new URL(window.location);
        url.searchParams.set(name, value);
        window.history.replaceState({}, '', url);
    }

    // 移除URL参数
    removeUrlParam(name) {
        const url = new URL(window.location);
        url.searchParams.delete(name);
        window.history.replaceState({}, '', url);
    }

    // 本地存储封装
    storage = {
        set: (key, value) => {
            try {
                localStorage.setItem(key, JSON.stringify(value));
            } catch (e) {
                console.error('存储失败:', e);
            }
        },
        get: (key, defaultValue = null) => {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (e) {
                console.error('读取存储失败:', e);
                return defaultValue;
            }
        },
        remove: (key) => {
            try {
                localStorage.removeItem(key);
            } catch (e) {
                console.error('删除存储失败:', e);
            }
        },
        clear: () => {
            try {
                localStorage.clear();
            } catch (e) {
                console.error('清空存储失败:', e);
            }
        }
    };

    // 会话存储封装
    sessionStorage = {
        set: (key, value) => {
            try {
                sessionStorage.setItem(key, JSON.stringify(value));
            } catch (e) {
                console.error('会话存储失败:', e);
            }
        },
        get: (key, defaultValue = null) => {
            try {
                const item = sessionStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (e) {
                console.error('读取会话存储失败:', e);
                return defaultValue;
            }
        },
        remove: (key) => {
            try {
                sessionStorage.removeItem(key);
            } catch (e) {
                console.error('删除会话存储失败:', e);
            }
        },
        clear: () => {
            try {
                sessionStorage.clear();
            } catch (e) {
                console.error('清空会话存储失败:', e);
            }
        }
    };
}

// 导出工具模块
window.UtilsModule = UtilsModule; 