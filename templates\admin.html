<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="admin_title">后台管理 - 标签打印系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css', v='2024062111') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/i18n.css', v=version) }}">
</head>
<body>
    <script>
        window.userPermissions = {{ permissions|tojson }};
    </script>
    <header>
        <div class="header-left">
            <span class="title" data-i18n="admin_system_title">标签打印系统 - 后台管理</span>
        </div>
        <div class="header-right">
            {% if user_info %}
                <div class="user-dropdown">
                    <span class="user-info" data-user-code="{{ user_info.code or '' }}">
                        {{ user_info.name or '用户' }} ({{ user_info.department }})
                        <span class="dropdown-arrow">▼</span>
                    </span>
                    <div class="user-dropdown-menu">
                        <div class="menu-item" onclick="showChangePasswordModal()">
                            <span data-i18n="change_password">修改密码</span>
                        </div>
                        <div class="language-menu-item">
                            <span data-i18n="language">Language</span>
                            <span class="submenu-arrow">▶</span>
                            <div class="language-submenu">
                                <div class="language-option" data-lang="en">
                                    <span class="flag">🇺🇸</span>
                                    <span>English</span>
                                </div>
                                <div class="language-option" data-lang="es">
                                    <span class="flag">🇪🇸</span>
                                    <span>Español</span>
                                </div>
                                <div class="language-option" data-lang="zh">
                                    <span class="flag">🇨🇳</span>
                                    <span>中文</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
            <a href="{{ url_for('index') }}" data-i18n="back_to_home">返回主页</a>
            <a href="{{ url_for('logout') }}" data-i18n="logout">登出</a>
        </div>
    </header>

    <div id="app">
        <div class="admin-container">
            <nav class="admin-nav">
                <ul>
                    <li><a href="#" data-target="labels" class="active" data-i18n="label_management">标签管理</a></li>
                    <li><a href="#" data-target="category-center" data-i18n="category_center">分类中心</a></li>
                    <li><a href="#" data-target="users" data-i18n="user_management">用户管理</a></li>
                    <li><a href="#" data-target="departments" data-i18n="department_management">部门管理</a></li>
                    <li><a href="#" data-target="user-groups" data-i18n="user_group_management">用户组管理</a></li>
                    <li><a href="#" data-target="announcements" data-i18n="announcement_management">公告管理</a></li>
                    <li><a href="#" data-target="feedback" data-i18n="feedback_view">反馈查看<span id="feedbackBadge" class="bell-badge" style="display:none"></span></a></li>
                    <li><a href="#" data-target="problem-solves" data-i18n="problem_solve_title">Problem Solve<span id="psBadge" class="bell-badge" style="display:none"></span></a></li>
                    <li><a href="#" data-target="barcode-prefixes" data-i18n="barcode_prefix_management">条形码前缀</a></li>
                    <li><a href="#" data-target="chatbot-kb" data-i18n="chatbot_kb_management">聊天机器人词库</a></li>
                    <li><a href="#" data-target="audit-log" data-i18n="audit_log">操作日志</a></li>
                    <li><a href="#" data-target="system-settings" data-i18n="system_settings">系统设定</a></li>
                </ul>
            </nav>

            <main class="admin-content">
                <!-- 标签管理 -->
                <section id="labels" style="display: block;">
                    <div class="admin-section-header">
                        <h2 data-i18n="label_management">标签管理</h2>
                        <input type="text" id="search-labels" class="admin-search-input" data-i18n-placeholder="search_labels" placeholder="搜索标签...">
                    </div>
                    <div class="admin-actions">
                        <div class="action-group-left">
                            <button class="btn-add" onclick="showAddLabelModal()" data-i18n="add_label">添加标签</button>
                            <button class="btn-refresh" onclick="loadLabels()" data-i18n="refresh">刷新</button>
                        </div>
                        <div class="action-group-right">
                            <label for="label-type-filter" data-i18n="filter_by_category">分类查看:</label>
                            <select id="label-type-filter" class="form-control">
                                <option value="all">全部类型</option>
                                <option value="普通">普通</option>
                                <option value="高级">高级</option>
                                <option value="特殊">特殊</option>
                                <option value="全局">全局</option>
                            </select>
                        </div>
                    </div>
                    <div class="admin-table-container">
                        <table class="admin-table" id="labels-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>名称</th>
                                    <th>类型</th>
                                    <th>内容</th>
                                    <th>打印提示</th>
                                    <th>打印数量</th>
                                    <th>复制内容</th>
                                    <th>部门</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="labels-table-body">
                                <!-- 标签数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- 分类中心 -->
                <section id="category-center" style="display: none;">
                    <div class="admin-section-header">
                        <h2 data-i18n="category_center">分类中心</h2>
                        <input type="text" id="search-category-codes" class="admin-search-input" data-i18n-placeholder="search_category_codes" placeholder="搜索分类代码...">
                    </div>
                    <div class="admin-actions">
                        <div class="action-group-left">
                            <button class="btn-add" id="add-category-code-btn" data-i18n="add_category_code">添加分类代码</button>
                            <button class="btn-refresh" onclick="categoryCenterModule.loadCategoryCodes()" data-i18n="refresh">刷新</button>
                        </div>
                    </div>
                    <div class="admin-table-container">
                        <div id="category-codes-list" class="category-codes-container">
                            <!-- 分类代码数据将通过JavaScript加载 -->
                        </div>
                    </div>
                </section>

                <!-- 用户管理 -->
                <section id="users" style="display: none;">
                    <div class="admin-section-header">
                        <h2>用户管理</h2>
                        <input type="text" id="search-users" class="admin-search-input" placeholder="搜索用户...">
                    </div>
                    <div class="admin-actions">
                        <button class="btn-add" onclick="adminUsersModule.showAddUserModal()">添加用户</button>
                        <button class="btn-refresh" onclick="adminUsersModule.loadUsers()">刷新</button>
                    </div>
                    <div class="admin-table-container">
                        <table class="admin-table" id="users-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>员工卡ID</th>
                                    <th>姓名</th>
                                    <th>代码</th>
                                    <th>部门</th>
                                    <th>用户组</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 用户数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- 部门管理 -->
                <section id="departments" style="display: none;">
                    <div class="admin-section-header">
                        <h2>部门管理</h2>
                        <input type="text" id="search-departments" class="admin-search-input" placeholder="搜索部门...">
                    </div>
                    <div class="admin-actions">
                        <button class="btn-add" onclick="adminDepartmentsModule.showAddDepartmentModal()">添加部门</button>
                        <button class="btn-refresh" onclick="adminDepartmentsModule.loadDepartments()">刷新</button>
                    </div>
                    <div class="admin-table-container">
                        <table class="admin-table" id="departments-table">
                            <thead>
                                <tr>
                                    <th style="width: 80px;">ID</th>
                                    <th>部门名称</th>
                                    <th style="width: 120px;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="departments-table-body">
                                <!-- 部门数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- 用户组管理 -->
                <section id="user-groups" style="display: none;">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px; padding-right: 20px;">
                        <h2 style="margin: 0;">用户组管理</h2>
                        <button id="refresh-permissions-btn" class="btn-refresh">权限刷新</button>
                    </div>
                    <div class="admin-actions">
                        <div class="action-group-left">
                            <button class="btn-add" onclick="adminUserGroupsModule.showAddUserGroupModal()">添加用户组</button>
                            <button class="btn-refresh" onclick="adminUserGroupsModule.loadUserGroups()">刷新</button>
                        </div>
                        <div class="action-group-right">
                            <button class="btn-secondary" onclick="adminUserGroupsModule.togglePermissionsLegend()">
                                <span id="legend-toggle-text">显示权限分类</span>
                            </button>
                        </div>
                    </div>

                    <!-- 权限分类图例 -->
                    <div id="permissions-legend" class="permissions-legend" style="display: none;">
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #e3f2fd; border-color: #3498db;"></div>
                            <span>后台管理</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #e8f5e8; border-color: #27ae60;"></div>
                            <span>用户管理</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #fff3e0; border-color: #f39c12;"></div>
                            <span>部门管理</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #f3e5f5; border-color: #9b59b6;"></div>
                            <span>用户组管理</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #e0f2f1; border-color: #1abc9c;"></div>
                            <span>标签管理</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #ffebee; border-color: #e74c3c;"></div>
                            <span>公告管理</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #fce4ec; border-color: #e91e63;"></div>
                            <span>反馈管理</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #eceff1; border-color: #34495e;"></div>
                            <span>审计日志</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #efebe9; border-color: #8d6e63;"></div>
                            <span>条形码管理</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #e1f5fe; border-color: #2196f3;"></div>
                            <span>聊天机器人</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #e0f7fa; border-color: #00bcd4;"></div>
                            <span>前台功能</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #e8f5e8; border-color: #4caf50;"></div>
                            <span>数据查看</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #ede7f6; border-color: #673ab7;"></div>
                            <span>系统设置</span>
                        </div>
                    </div>
                    <div class="admin-table-container">
                        <table class="admin-table user-groups-table" id="user-groups-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户组名</th>
                                    <th class="permissions-cell" style="width: calc(100% - 10px);">所拥有的权限</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 用户组数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- 公告管理 -->
                <section id="announcements" style="display: none;">
                    <h2>公告管理</h2>
                    <div class="admin-actions">
                        <button class="btn-add" onclick="window.adminAnnouncementsModule.showAddAnnouncementModal()">发布公告</button>
                        <button class="btn-refresh" onclick="window.adminAnnouncementsModule.loadAnnouncements()">刷新</button>
                    </div>
                    <div class="admin-table-container">
                        <table class="admin-table" id="announcements-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>内容</th>
                                    <th>发布者</th>
                                    <th>发布时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 公告数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- 反馈查看 -->
                <section id="feedback" style="display: none;">
                    <h2>用户反馈</h2>
                    <div class="admin-actions">
                        <button class="btn-refresh" onclick="adminFeedbackModule.loadFeedback()">刷新</button>
                    </div>
                    <div class="admin-table-container">
                        <table class="admin-table" id="feedback-table">
                            <thead>
                                <tr>
                                    <th>反馈人</th>
                                    <th>标题</th>
                                    <th>反馈内容</th>
                                    <th>回复</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 反馈数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- Problem Solve -->
                <section id="problem-solves" style="display: none;">
                    <h2>Problem Solve 表单</h2>
                    <div class="admin-actions">
                        <button class="btn-refresh" onclick="window.adminProblemSolvesModule.loadProblemSolves()">刷新</button>
                    </div>
                    <div class="admin-table-container">
                        <table class="admin-table" id="problem-solve-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>标签</th>
                                    <th>提交者</th>
                                    <th>内容</th>
                                    <th>状态</th>
                                    <th>提交时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Problem Solve数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- 条形码前缀 -->
                <section id="barcode-prefixes" style="display: none;">
                    <h2>条形码前缀管理</h2>
                    <div class="admin-actions">
                        <button class="btn-add" onclick="window.adminBarcodePrefixesModule.showAddBarcodePrefixModal()">添加前缀</button>
                        <button class="btn-refresh" onclick="window.adminBarcodePrefixesModule.loadBarcodePrefixes()">刷新</button>
                    </div>
                    <div class="admin-table-container">
                        <table class="admin-table" id="barcode-prefixes-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>前缀</th>
                                    <th>打印次数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 条形码前缀数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- 聊天机器人词库 -->
                <section id="chatbot-kb" style="display: none;">
                    <h2>聊天机器人词库</h2>
                    <div class="admin-actions">
                        <button class="btn-add" onclick="window.adminChatbotKBModule.showAddChatbotKBModal()">添加词条</button>
                        <button class="btn-refresh" onclick="window.adminChatbotKBModule.loadChatbotKB()">刷新</button>
                    </div>
                    <div class="admin-table-container">
                        <table class="admin-table" id="chatbot-kb-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>关键词</th>
                                    <th>回答</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 聊天机器人词库数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- 操作日志 -->
                <section id="audit-log" style="display: none;">
                    <h2>操作日志</h2>
                    <div class="admin-actions">
                        <button class="btn-refresh" onclick="window.adminAuditLogModule.loadAuditLog()">刷新</button>
                        <button class="btn-clear" onclick="window.adminAuditLogModule.clearAuditLog()">清空</button>
                    </div>
                    <div class="admin-table-container">
                        <table class="admin-table" id="audit-log-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户</th>
                                    <th>操作</th>
                                    <th>时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 操作日志数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- 系统设定 -->
                <section id="system-settings" style="display: none;">
                    <h2>系统设定</h2>
                    <div class="admin-form-container">
                        <div class="admin-form-section">
                            <h3 data-i18n="open_registration_settings">开放注册设定</h3>
                            <p class="form-description" data-i18n="open_registration_description">控制是否允许用户自主注册账户</p>
                            <form id="registration-settings-form" class="admin-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="open-registration-toggle">
                                            <input type="checkbox" id="open-registration-toggle" name="open_registration">
                                            <span data-i18n="enable_open_registration">启用开放注册</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn-primary" data-i18n="save_settings">保存设置</button>
                                    <button type="button" class="btn-secondary" onclick="window.adminSystemSettingsModule.loadRegistrationSettings()" data-i18n="reset">重置</button>
                                </div>
                            </form>
                        </div>

                        <div class="admin-form-section">
                            <h3 data-i18n="chat_retention_settings">聊天记录保存时间设定</h3>
                            <p class="form-description" data-i18n="chat_retention_description">设置公众聊天记录的保存时间，超出时间的记录将自动删除</p>
                            <form id="chat-retention-form" class="admin-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="retention-days">天数:</label>
                                        <input type="number" id="retention-days" name="days" min="0" max="365" value="7">
                                    </div>
                                    <div class="form-group">
                                        <label for="retention-hours">小时:</label>
                                        <input type="number" id="retention-hours" name="hours" min="0" max="23" value="0">
                                    </div>
                                    <div class="form-group">
                                        <label for="retention-minutes">分钟:</label>
                                        <input type="number" id="retention-minutes" name="minutes" min="0" max="59" value="0">
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn-primary">保存设置</button>
                                    <button type="button" class="btn-secondary" onclick="window.adminSystemSettingsModule.loadSettings()">重置</button>
                                    <button type="button" class="btn-danger" onclick="window.adminSystemSettingsModule.cleanupChatRecords()">立即清理过期记录</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <!-- Modal for User Group is now generated by admin.js -->

    <script src="{{ url_for('static', filename='js/i18n.js', v=version) }}"></script>
    <script src="{{ url_for('static', filename='js/modules/admin-users.js') }}"></script>
    <script src="{{ url_for('static', filename='js/modules/admin-feedback.js') }}"></script>
    <script src="{{ url_for('static', filename='js/modules/admin-announcements.js') }}"></script>
    <script src="{{ url_for('static', filename='js/admin.js', v='2024062102') }}"></script>
    <script src="{{ url_for('static', filename='js/modules/admin-departments.js') }}"></script>
    <script src="{{ url_for('static', filename='js/modules/admin-user-groups.js') }}"></script>
    <script src="{{ url_for('static', filename='js/modules/admin-problem-solves.js') }}"></script>
    <script src="{{ url_for('static', filename='js/modules/admin-barcode-prefixes.js') }}"></script>
    <script src="{{ url_for('static', filename='js/modules/admin-chatbot-kb.js') }}"></script>
    <script src="{{ url_for('static', filename='js/modules/admin-audit-log.js') }}"></script>
    <script src="{{ url_for('static', filename='js/modules/admin-system-settings.js') }}"></script>
    <script>
    function checkProblemSolveBadge() {
        console.log('检查问题解决气泡...');

        // 确保气泡元素存在
        let badge = document.getElementById('psBadge');
        if (!badge) {
            console.log('气泡元素不存在，尝试创建...');
            const psParent = document.querySelector('[data-target="problem-solves"]');
            if (psParent) {
                badge = document.createElement('span');
                badge.id = 'psBadge';
                badge.className = 'bell-badge';
                badge.style.display = 'none';
                psParent.appendChild(badge);
                psParent.style.position = 'relative';
                console.log('已创建问题解决气泡元素');
            } else {
                console.error('未找到问题解决菜单项');
                return;
            }
        }

        fetch('/api/problem-solves')
            .then(response => {
                console.log('问题解决API响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('问题解决数据:', data);
                const unreplyCount = data.filter(item => !item.reply).length;
                console.log('未回复问题数量:', unreplyCount);

                if (badge) {
                    badge.textContent = unreplyCount;
                    badge.style.display = unreplyCount > 0 ? 'inline-block' : 'none';
                    console.log('问题解决气泡已更新:', unreplyCount > 0 ? '显示' : '隐藏');
                }
            })
            .catch(error => {
                console.error('检查问题解决气泡时出错:', error);
            });
    }

    // 初始化管理模块
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM加载完成，开始初始化...');

        // 初始化反馈管理模块
        if (window.AdminFeedbackModule) {
            // 延迟初始化，确保DOM完全加载
            setTimeout(() => {
                window.adminFeedbackModule = new AdminFeedbackModule();
                console.log('反馈管理模块已初始化');
            }, 1000);
        }

        // 初始化问题解决气泡检查
        console.log('开始初始化问题解决气泡检查...');
        console.log('当前用户权限:', window.userPermissions);

        // 延迟执行，确保DOM完全加载
        setTimeout(() => {
            checkProblemSolveBadge();
            setInterval(checkProblemSolveBadge, 60000);
        }, 1000);

        // 语言切换功能
        // 为语言选项添加点击事件
        document.querySelectorAll('.language-option').forEach(option => {
            option.addEventListener('click', function() {
                const lang = this.dataset.lang;
                if (lang) {
                    // 保存语言设置到服务器
                    fetch('/api/user-language', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ language: lang })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 刷新页面以应用新语言
                            window.location.reload();
                        } else {
                            console.error('Failed to save language setting');
                        }
                    })
                    .catch(error => {
                        console.error('Error saving language:', error);
                    });
                }
            });
        });

        // 更新当前语言高亮
        function updateActiveLanguage(currentLang) {
            document.querySelectorAll('.language-option').forEach(option => {
                option.classList.remove('active');
                if (option.dataset.lang === currentLang) {
                    option.classList.add('active');
                }
            });
        }

        // 获取当前用户语言设置并高亮显示
        setTimeout(() => {
            if (window.i18n) {
                updateActiveLanguage(window.i18n.currentLanguage);
            }
        }, 100);

        // 为用户组管理页面的权限刷新按钮添加事件处理
        const refreshPermissionsBtn = document.getElementById('refresh-permissions-btn');
        if (refreshPermissionsBtn) {
            refreshPermissionsBtn.onclick = function() {
                if (!window.userPermissions || !window.userPermissions.can_refresh_permissions) {
                    showMessage('您没有权限执行此操作', 'error');
                    return;
                }

                fetch('/api/refresh-permissions', { method: 'POST' })
                    .then(res => res.json())
                    .then(data => {
                        if (data.success) {
                            window.userPermissions = data.permissions;
                            showMessage('权限已刷新', 'success');
                            location.reload();
                        } else {
                            showMessage(data.error || '刷新失败', 'error');
                        }
                    })
                    .catch(() => showMessage('刷新失败', 'error'));
            };
        }
    });
    </script>

    <!-- 分类中心模块 -->
    <script src="{{ url_for('static', filename='js/modules/category-center.js') }}"></script>
    <script>
        // 初始化分类中心模块
        document.addEventListener('DOMContentLoaded', function() {
            // 当切换到分类中心页面时初始化
            const categoryTab = document.querySelector('[data-target="category-center"]');
            if (categoryTab) {
                categoryTab.addEventListener('click', function() {
                    setTimeout(() => {
                        if (window.categoryCenterModule) {
                            window.categoryCenterModule.init();
                        }
                    }, 100);
                });
            }
        });
    </script>
</body>
</html>