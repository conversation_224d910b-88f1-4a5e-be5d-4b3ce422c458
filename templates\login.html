<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Label Printer</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="{{ url_for('static', filename='js/i18n.js') }}?v=1.1.0"></script>
    <script src="{{ url_for('static', filename='js/modules/registration.js') }}?v=1.1.0"></script>
    <script src="{{ url_for('static', filename='js/modules/enhanced-login.js') }}?v=1.1.0"></script>
</head>
<body>
    <div class="login-container">
        <h2>Badge Login</h2>
        <form action="/login" method="post">
            <input type="text" name="card_id" placeholder="Please Scan Your Badge" required autofocus
                   value="{% if card_id %}{{ card_id }}{% endif %}">
            {% if needs_password %}
                <input type="password" name="password" placeholder="Enter Password" required style="margin-top: 10px;">
            {% endif %}
            <button type="submit">{% if needs_password %}Login{% else %}Continue{% endif %}</button>
        </form>
        {% if error %}
            <p class="error">{{ error }}</p>
        {% endif %}
    </div>

    <script>
        // Initialize i18n and registration module
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize i18n with default language (English)
            if (window.I18n) {
                window.i18n = new I18n('en');
            }

            // Initialize registration module
            if (window.RegistrationModule) {
                window.registrationModule = new RegistrationModule();
                window.registrationModule.init();
            }

            // Initialize enhanced login module
            if (window.EnhancedLoginModule) {
                window.enhancedLoginModule = new EnhancedLoginModule();
                window.enhancedLoginModule.init();
            }
        });
    </script>
</body>
</html>
