<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="problem_solve_title">问题解决列表 - 标签打印系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/i18n.css', v=version) }}">
    <style>
        .problem-solve-header {
            text-align: center;
            margin: 40px 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .problem-solve-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        
        .problem-solve-header p {
            margin: 10px 0 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .problem-solve-actions {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 20px;
            padding: 0 20px;
        }
        
        .problem-solve-table {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .problem-solve-table th {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
            padding: 15px;
            text-align: left;
        }
        
        .problem-solve-table td {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .problem-solve-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .problem-content {
            max-width: 300px;
            word-wrap: break-word;
            line-height: 1.5;
        }
        
        .problem-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn-reply {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .btn-reply:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
        
        .reply-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .reply-modal-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            width: 500px;
            max-width: 90vw;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .reply-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .reply-modal-header h3 {
            margin: 0;
            color: #333;
        }
        
        .reply-form textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-family: inherit;
            resize: vertical;
            min-height: 100px;
            box-sizing: border-box;
        }
        
        .reply-form textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .reply-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        .status-pending {
            color: #ffc107;
            font-weight: 600;
        }
        
        .status-replied {
            color: #28a745;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-left">
            <a href="{{ url_for('index') }}" class="title" data-i18n="label_printing">标签打印</a>
        </div>
        <div class="header-right">
            {% if permissions.can_manage_backend %}
            <a href="{{ url_for('admin') }}" class="btn-admin" data-i18n="admin_panel">后台管理</a>
            {% endif %}
            <a href="{{ url_for('index') }}" class="btn-feedback" data-i18n="back_to_home">返回首页</a>
            {% if user_info %}
            <span class="user-info">{{ user_info.name }} ({{ user_info.department }})</span>
            {% endif %}
            <a href="{{ url_for('logout') }}" data-i18n="logout">登出</a>
        </div>
    </header>

    <main>
        <div class="problem-solve-header">
            <h1 data-i18n="problem_solve_center">🔧 问题解决中心</h1>
            <p data-i18n="problem_solve_description">查看和管理用户提交的问题反馈</p>
        </div>

        <div class="problem-solve-actions">
            <button class="btn-refresh" onclick="loadProblemSolves()" data-i18n="refresh_list">🔄 刷新列表</button>
        </div>
        
        <div class="problem-solve-table">
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th data-i18n="problem_title">问题</th>
                        <th data-i18n="submitted_by">提交用户</th>
                        <th data-i18n="problem_content">问题内容</th>
                        <th data-i18n="status">状态</th>
                        <th data-i18n="submitted_time">提交时间</th>
                        <th data-i18n="actions">操作</th>
                    </tr>
                </thead>
                <tbody id="problemSolvesTable">
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 20px;">
                            <div class="loading" data-i18n="loading">加载中...</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </main>

    <script>
        // 获取用户权限信息
        const userPermissions = {{ permissions|tojson|safe }};
        
        document.addEventListener('DOMContentLoaded', function() {
            loadProblemSolves();
        });

        function loadProblemSolves() {
            const tableBody = document.getElementById('problemSolvesTable');
            const loadingText = window.t ? window.t('loading') : '加载中...';
            tableBody.innerHTML = `<tr><td colspan="7" style="text-align: center; padding: 20px;"><div class="loading">${loadingText}</div></td></tr>`;

            fetch('/api/problem-solves')
                .then(response => response.json())
                .then(data => {
                    if (data.length === 0) {
                        const noRecordsText = window.t ? window.t('no_problem_records') : '暂无问题记录';
                        tableBody.innerHTML = `<tr><td colspan="7" style="text-align: center; padding: 20px; color: #666;">${noRecordsText}</td></tr>`;
                        return;
                    }

                    const repliedText = window.t ? window.t('replied') : '已回复';
                    const pendingText = window.t ? window.t('pending') : '待处理';
                    const replyText = window.t ? window.t('reply') : '回复';
                    const deleteText = window.t ? window.t('delete') : '删除';

                    tableBody.innerHTML = data.map(problem => `
                        <tr>
                            <td>${problem.id}</td>
                            <td>${problem.title}</td>
                            <td>${problem.submitted_by_name}</td>
                            <td class="problem-content">${problem.content}</td>
                            <td>
                                <span class="status-${problem.reply ? 'replied' : 'pending'}">
                                    ${problem.reply ? repliedText : pendingText}
                                </span>
                            </td>
                            <td>${new Date(problem.created_at).toLocaleString()}</td>
                            <td class="problem-actions">
                                ${userPermissions.can_reply_problem_solve ? `<button class="btn-reply" onclick="showReplyModal(${problem.id}, '${problem.content.replace(/'/g, "\\'")}')">${replyText}</button>` : ''}
                                ${userPermissions.can_delete_problem_solve ? `<button class="btn-delete" onclick="deleteProblemSolve(${problem.id})">${deleteText}</button>` : ''}
                            </td>
                        </tr>
                    `).join('');
                })
                .catch(error => {
                    console.error('Error loading problem solves:', error);
                    const loadFailedText = window.t ? window.t('load_failed') : '加载失败';
                    tableBody.innerHTML = `<tr><td colspan="7" style="text-align: center; padding: 20px; color: #dc3545;">${loadFailedText}</td></tr>`;
                });
        }

        function showReplyModal(problemId, problemContent) {
            const replyProblemText = window.t ? window.t('reply_problem') : '回复问题';
            const originalProblemText = window.t ? window.t('original_problem') : '原问题：';
            const replyContentText = window.t ? window.t('reply_content') : '回复内容：';
            const replyPlaceholderText = window.t ? window.t('reply_placeholder') : '请输入您的回复...';
            const cancelText = window.t ? window.t('cancel') : '取消';
            const submitReplyText = window.t ? window.t('submit_reply') : '提交回复';

            const modal = document.createElement('div');
            modal.className = 'reply-modal';
            modal.innerHTML = `
                <div class="reply-modal-content">
                    <div class="reply-modal-header">
                        <h3>${replyProblemText} #${problemId}</h3>
                        <button onclick="this.closest('.reply-modal').remove()" style="
                            background: none;
                            border: none;
                            font-size: 24px;
                            cursor: pointer;
                            color: #666;
                        ">&times;</button>
                    </div>
                    <div class="reply-form">
                        <div style="margin-bottom: 15px;">
                            <strong>${originalProblemText}</strong>
                            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 5px;">
                                ${problemContent}
                            </div>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">${replyContentText}</label>
                            <textarea id="replyContent" placeholder="${replyPlaceholderText}" required></textarea>
                        </div>
                        <div class="reply-actions">
                            <button onclick="this.closest('.reply-modal').remove()" style="
                                padding: 10px 20px;
                                border: 1px solid #ddd;
                                background: #f8f9fa;
                                border-radius: 6px;
                                cursor: pointer;
                            ">${cancelText}</button>
                            <button onclick="submitReply(${problemId}, this.closest('.reply-modal'))" style="
                                padding: 10px 20px;
                                background: linear-gradient(45deg, #667eea, #764ba2);
                                color: white;
                                border: none;
                                border-radius: 6px;
                                cursor: pointer;
                                font-weight: 600;
                            ">${submitReplyText}</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Close modal when clicking outside
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        function submitReply(problemId, modal) {
            const replyContent = document.getElementById('replyContent').value.trim();
            if (!replyContent) {
                const enterReplyText = window.t ? window.t('enter_reply_content') : '请填写回复内容';
                alert(enterReplyText);
                return;
            }

            fetch(`/api/problem-solves/${problemId}/reply`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    reply: replyContent
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const replySubmittedText = window.t ? window.t('reply_submitted') : '回复已提交';
                    showMessage(replySubmittedText, 'success');
                    modal.remove();
                    loadProblemSolves(); // 重新加载列表
                    if (typeof window.checkProblemSolveBadge === 'function') window.checkProblemSolveBadge();
                } else {
                    const replyFailedText = window.t ? window.t('reply_failed') : '回复失败';
                    showMessage(data.error || replyFailedText, 'error');
                }
            })
            .catch(error => {
                console.error('Reply error:', error);
                const replyFailedText = window.t ? window.t('reply_failed') : '回复失败';
                showMessage(replyFailedText, 'error');
            });
        }

        function deleteProblemSolve(id) {
            const confirmDeleteText = window.t ? window.t('confirm_delete_problem') : '确定要删除这个问题记录吗？';
            if (!confirm(confirmDeleteText)) {
                return;
            }

            fetch(`/api/problem-solves/${id}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const deleteSuccessText = window.t ? window.t('delete_success') : '删除成功';
                    showMessage(deleteSuccessText, 'success');
                    loadProblemSolves();
                } else {
                    const deleteFailedText = window.t ? window.t('delete_failed') : '删除失败';
                    showMessage(data.error || deleteFailedText, 'error');
                }
            })
            .catch(error => {
                console.error('Delete error:', error);
                const deleteFailedText = window.t ? window.t('delete_failed') : '删除失败';
                showMessage(deleteFailedText, 'error');
            });
        }

        function showMessage(message, type = 'info') {
            const msgDiv = document.createElement('div');
            msgDiv.className = `message message-${type}`;
            msgDiv.textContent = message;
            msgDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 10px 20px;
                border-radius: 4px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            `;
            
            switch(type) {
                case 'success':
                    msgDiv.style.backgroundColor = '#28a745';
                    break;
                case 'error':
                    msgDiv.style.backgroundColor = '#dc3545';
                    break;
                default:
                    msgDiv.style.backgroundColor = '#007bff';
            }
            
            document.body.appendChild(msgDiv);
            
            setTimeout(() => {
                msgDiv.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (msgDiv.parentNode) {
                        msgDiv.parentNode.removeChild(msgDiv);
                    }
                }, 300);
            }, 3000);
        }
    </script>

    <script src="{{ url_for('static', filename='js/i18n.js', v=version) }}"></script>
</body>
</html>